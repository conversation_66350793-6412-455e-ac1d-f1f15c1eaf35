# 🇩🇴 SOLOYLIBRE WORDPRESS - INSTALACIÓN RÁPIDA

**Desarrollado por Jose L Encarnacion (JoseTusabe)**  
**Desde San José de Ocoa, República Dominicana 🇩🇴**

---

## 🚀 **INSTALACIÓN EN 3 PASOS**

### **Paso 1: Ejecutar Instalador**
```bash
./install_wordpress_fresh.sh
```

### **Paso 2: Configurar Base de Datos (si es necesario)**
```bash
mysql -u root -p < database_setup.sql
```

### **Paso 3: Acceder al Sitio**
```
🌐 Sitio Web: http://localhost:8080
🔧 Admin Panel: http://localhost:8080/wp-admin
```

---

## 🔐 **CREDENCIALES DE ACCESO**

### **WordPress Admin**
```
👤 Usuario: josetusabe
🔑 Contraseña: JoseTusabe2025!
📧 Email: <EMAIL>
```

### **Base de Datos**
```
🗄️ Base de Datos: soloylibre_wp
👤 Usuario: soloylibre_admin
🔑 Contraseña: SoloYLibre2025!
🏠 Host: localhost
```

### **Usuarios Adicionales de BD**
```
📖 Solo Lectura: soloylibre_readonly / SoloYLibreRead2025!
💾 Backups: soloylibre_backup / SoloYLibreBackup2025!
```

---

## 📋 **VERIFICACIÓN POST-INSTALACIÓN**

### **1. Verificar Servicios**
```bash
# Verificar MySQL
mysql -u soloylibre_admin -p -e "SHOW DATABASES;"

# Verificar PHP
php -v

# Verificar servidor web
curl -I http://localhost:8080
```

### **2. Verificar WordPress**
```bash
# Verificar archivos
ls -la SoloYLibre-WordPress-Fresh/

# Verificar permisos
ls -la SoloYLibre-WordPress-Fresh/wp-config.php

# Verificar tema
ls -la SoloYLibre-WordPress-Fresh/wp-content/themes/soloylibre-ultimate/
```

### **3. Verificar Base de Datos**
```sql
-- Conectar a MySQL
mysql -u soloylibre_admin -p

-- Usar base de datos
USE soloylibre_wp;

-- Verificar tablas
SHOW TABLES;

-- Verificar información del desarrollador
SELECT * FROM wp_soloylibre_info;

-- Verificar configuración
SELECT * FROM wp_soloylibre_config;
```

---

## 🛠️ **COMANDOS ÚTILES**

### **Gestión del Servidor**
```bash
# Iniciar servidor
cd SoloYLibre-WordPress-Fresh
php -S localhost:8080

# Detener servidor
# Presionar Ctrl+C

# Cambiar puerto
php -S localhost:8081
```

### **Gestión de Base de Datos**
```bash
# Backup completo
mysqldump -u soloylibre_admin -p soloylibre_wp > backup_$(date +%Y%m%d).sql

# Restaurar backup
mysql -u soloylibre_admin -p soloylibre_wp < backup_20250624.sql

# Verificar conexión
mysql -u soloylibre_admin -p -e "SELECT 'Conexión exitosa' as status;"
```

### **Gestión de Archivos**
```bash
# Permisos correctos
chmod 755 SoloYLibre-WordPress-Fresh/
chmod -R 644 SoloYLibre-WordPress-Fresh/*
chmod -R 755 SoloYLibre-WordPress-Fresh/wp-content/

# Limpiar cache
rm -rf SoloYLibre-WordPress-Fresh/wp-content/cache/*

# Ver logs de error
tail -f SoloYLibre-WordPress-Fresh/wp-content/debug.log
```

---

## 🔧 **CONFIGURACIÓN MULTISITE**

### **Habilitar Multisite**
1. Agregar a `wp-config.php`:
```php
define('WP_ALLOW_MULTISITE', true);
```

2. Ir a **Herramientas > Configuración de Red**

3. Seguir el asistente de configuración

4. Actualizar `wp-config.php` y `.htaccess` según las instrucciones

---

## 🎨 **PERSONALIZACIÓN DEL TEMA**

### **Archivos del Tema**
```
wp-content/themes/soloylibre-ultimate/
├── style.css          # Estilos principales
├── index.php          # Plantilla principal
├── functions.php      # Funciones del tema
├── header.php         # Cabecera
├── footer.php         # Pie de página
├── sidebar.php        # Barra lateral
└── screenshot.png     # Captura del tema
```

### **Colores Personalizados**
```css
:root {
    --primary-color: #667eea;      /* Azul SoloYLibre */
    --secondary-color: #764ba2;    /* Púrpura SoloYLibre */
    --dominican-red: #ce1126;      /* Rojo bandera RD */
    --dominican-blue: #002d62;     /* Azul bandera RD */
}
```

---

## 🔌 **PLUGINS RECOMENDADOS**

### **Seguridad**
- Wordfence Security
- Sucuri Security
- iThemes Security

### **Rendimiento**
- WP Rocket
- W3 Total Cache
- Autoptimize

### **SEO**
- Yoast SEO
- RankMath
- All in One SEO

### **Backup**
- UpdraftPlus
- BackWPup
- Duplicator

---

## 📱 **CONFIGURACIÓN PWA**

### **Manifest.json**
Ubicación: `wp-content/themes/soloylibre-ultimate/manifest.json`

### **Service Worker**
Ubicación: `wp-content/themes/soloylibre-ultimate/sw.js`

### **Verificar PWA**
1. Abrir DevTools (F12)
2. Ir a pestaña "Application"
3. Verificar "Manifest" y "Service Workers"

---

## 🚨 **SOLUCIÓN DE PROBLEMAS**

### **Error de Conexión a BD**
```bash
# Verificar MySQL
sudo systemctl status mysql    # Linux
brew services list | grep mysql    # macOS

# Reiniciar MySQL
sudo systemctl restart mysql    # Linux
brew services restart mysql    # macOS
```

### **Error de Permisos**
```bash
# Corregir permisos
sudo chown -R www-data:www-data SoloYLibre-WordPress-Fresh/    # Linux
sudo chown -R _www:_www SoloYLibre-WordPress-Fresh/    # macOS

# Permisos específicos
chmod 644 wp-config.php
chmod -R 755 wp-content/
chmod -R 755 wp-admin/
chmod -R 755 wp-includes/
```

### **Error 500**
```bash
# Verificar logs de error
tail -f /var/log/apache2/error.log    # Apache
tail -f /var/log/nginx/error.log      # Nginx
tail -f wp-content/debug.log          # WordPress
```

### **Problemas de Memoria**
Agregar a `wp-config.php`:
```php
ini_set('memory_limit', '512M');
define('WP_MEMORY_LIMIT', '512M');
```

---

## 📞 **SOPORTE TÉCNICO**

### **Contacto del Desarrollador**
```
👨‍💻 Nombre: Jose L Encarnacion (JoseTusabe)
🏔️ Ubicación: San José de Ocoa, República Dominicana 🇩🇴
📧 Email: <EMAIL>
📞 Teléfono: ************
🌐 Sitios Web:
   - josetusabe.com
   - soloylibre.com
   - 1and1photo.com
   - joselencarnacion.com
```

### **Información del Servidor**
```
🖥️ Servidor: Synology RS3618xs
💾 Memoria: 56GB RAM
💿 Almacenamiento: 36TB
🔧 Sistema: WordPress Ultimate Multisite
```

---

## ✅ **CHECKLIST FINAL**

- [ ] ✅ WordPress instalado y funcionando
- [ ] ✅ Base de datos configurada
- [ ] ✅ Tema SoloYLibre activado
- [ ] ✅ Usuario administrador creado
- [ ] ✅ Permisos de archivos correctos
- [ ] ✅ Multisite habilitado (opcional)
- [ ] ✅ SSL configurado (opcional)
- [ ] ✅ Backup configurado
- [ ] ✅ Plugins de seguridad instalados
- [ ] ✅ SEO configurado
- [ ] ✅ PWA configurada (opcional)

---

## 🎉 **¡INSTALACIÓN COMPLETADA!**

Tu WordPress SoloYLibre Ultimate está listo para usar. 

**🇩🇴 ¡Dale paisano, que ya tienes tu WordPress más brutal del mundo!**

Desarrollado con ❤️ desde las montañas de San José de Ocoa, República Dominicana por Jose L Encarnacion (JoseTusabe).

**¡Que viva la República Dominicana!** 🇩🇴🚀
