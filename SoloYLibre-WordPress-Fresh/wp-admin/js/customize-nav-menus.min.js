/*! This file is auto-generated */
!function(u,l,m){"use strict";function c(e){return(e=(e=l.sanitize.stripTagsAndEncodeText(e=e||"")).toString().trim())||u.Menus.data.l10n.unnamed}wpNavMenu.originalInit=wpNavMenu.init,wpNavMenu.options.menuItemDepthPerLevel=20,wpNavMenu.options.sortableItems="> .customize-control-nav_menu_item",wpNavMenu.options.targetTolerance=10,wpNavMenu.init=function(){this.jQueryExtensions()},u.Menus=u.Menus||{},u.Menus.data={itemTypes:[],l10n:{},settingTransport:"refresh",phpIntMax:0,defaultSettingValues:{nav_menu:{},nav_menu_item:{}},locationSlugMappedToName:{}},"undefined"!=typeof _wpCustomizeNavMenusSettings&&m.extend(u.Menus.data,_wpCustomizeNavMenusSettings),u.Menus.generatePlaceholderAutoIncrementId=function(){return-Math.ceil(u.Menus.data.phpIntMax*Math.random())},u.Menus.AvailableItemModel=Backbone.Model.extend(m.extend({id:null},u.Menus.data.defaultSettingValues.nav_menu_item)),u.Menus.AvailableItemCollection=Backbone.Collection.extend({model:u.Menus.AvailableItemModel,sort_key:"order",comparator:function(e){return-e.get(this.sort_key)},sortByField:function(e){this.sort_key=e,this.sort()}}),u.Menus.availableMenuItems=new u.Menus.AvailableItemCollection(u.Menus.data.availableMenuItems),u.Menus.insertAutoDraftPost=function(n){var i=m.Deferred(),e=l.ajax.post("customize-nav-menus-insert-auto-draft",{"customize-menus-nonce":u.settings.nonce["customize-menus"],wp_customize:"on",customize_changeset_uuid:u.settings.changeset.uuid,params:n});return e.done(function(t){t.post_id&&(u("nav_menus_created_posts").set(u("nav_menus_created_posts").get().concat([t.post_id])),"page"===n.post_type&&(u.section.has("static_front_page")&&u.section("static_front_page").activate(),u.control.each(function(e){"dropdown-pages"===e.params.type&&e.container.find('select[name^="_customize-dropdown-pages-"]').append(new Option(n.post_title,t.post_id))})),i.resolve(t))}),e.fail(function(e){var t=e||"";void 0!==e.message&&(t=e.message),console.error(t),i.rejectWith(t)}),i.promise()},u.Menus.AvailableMenuItemsPanelView=l.Backbone.View.extend({el:"#available-menu-items",events:{"input #menu-items-search":"debounceSearch","focus .menu-item-tpl":"focus","click .menu-item-tpl":"_submit","click #custom-menu-item-submit":"_submitLink","keypress #custom-menu-item-name":"_submitLink","click .new-content-item .add-content":"_submitNew","keypress .create-item-input":"_submitNew",keydown:"keyboardAccessible"},selected:null,currentMenuControl:null,debounceSearch:null,$search:null,$clearResults:null,searchTerm:"",rendered:!1,pages:{},sectionContent:"",loading:!1,addingNew:!1,initialize:function(){var n=this;u.panel.has("nav_menus")&&(this.$search=m("#menu-items-search"),this.$clearResults=this.$el.find(".clear-results"),this.sectionContent=this.$el.find(".available-menu-items-list"),this.debounceSearch=_.debounce(n.search,500),_.bindAll(this,"close"),m("#customize-controls, .customize-section-back").on("click keydown",function(e){var t=m(e.target).is(".item-delete, .item-delete *"),e=m(e.target).is(".add-new-menu-item, .add-new-menu-item *");!m("body").hasClass("adding-menu-items")||t||e||n.close()}),this.$clearResults.on("click",function(){n.$search.val("").trigger("focus").trigger("input")}),this.$el.on("input","#custom-menu-item-name.invalid, #custom-menu-item-url.invalid",function(){m(this).removeClass("invalid");var e=m(this).attr("aria-describedby");m("#"+e).hide(),m(this).removeAttr("aria-invalid").removeAttr("aria-describedby")}),u.panel("nav_menus").container.on("expanded",function(){n.rendered||(n.initList(),n.rendered=!0)}),this.sectionContent.on("scroll",function(){var e=n.$el.find(".accordion-section.open .available-menu-items-list").prop("scrollHeight"),t=n.$el.find(".accordion-section.open").height();!n.loading&&m(this).scrollTop()>.75*e-t&&(e=m(this).data("type"),t=m(this).data("object"),"search"===e?n.searchTerm&&n.doSearch(n.pages.search):n.loadItems([{type:e,object:t}]))}),u.previewer.bind("url",this.close),n.delegateEvents())},search:function(e){var t=m("#available-menu-items-search"),n=m("#available-menu-items .accordion-section").not(t);e&&this.searchTerm!==e.target.value&&(""===e.target.value||t.hasClass("open")?""===e.target.value&&(t.removeClass("open"),n.show(),this.$clearResults.removeClass("is-visible")):(n.fadeOut(100),t.find(".accordion-section-content").slideDown("fast"),t.addClass("open"),this.$clearResults.addClass("is-visible")),this.searchTerm=e.target.value,this.pages.search=1,this.doSearch(1))},doSearch:function(t){var e,n=this,i=m("#available-menu-items-search"),a=i.find(".accordion-section-content"),o=l.template("available-menu-item");if(n.currentRequest&&n.currentRequest.abort(),!(t<0)){if(1<t)i.addClass("loading-more"),a.attr("aria-busy","true"),l.a11y.speak(u.Menus.data.l10n.itemsLoadingMore);else if(""===n.searchTerm)return a.html(""),void l.a11y.speak("");i.addClass("loading"),n.loading=!0,e=u.previewer.query({excludeCustomizedSaved:!0}),_.extend(e,{"customize-menus-nonce":u.settings.nonce["customize-menus"],wp_customize:"on",search:n.searchTerm,page:t}),n.currentRequest=l.ajax.post("search-available-menu-items-customizer",e),n.currentRequest.done(function(e){1===t&&a.empty(),i.removeClass("loading loading-more"),a.attr("aria-busy","false"),i.addClass("open"),n.loading=!1,e=new u.Menus.AvailableItemCollection(e.items),n.collection.add(e.models),e.each(function(e){a.append(o(e.attributes))}),e.length<20?n.pages.search=-1:n.pages.search=n.pages.search+1,e&&1<t?l.a11y.speak(u.Menus.data.l10n.itemsFoundMore.replace("%d",e.length)):e&&1===t&&l.a11y.speak(u.Menus.data.l10n.itemsFound.replace("%d",e.length))}),n.currentRequest.fail(function(e){e.message&&(a.empty().append(m('<li class="nothing-found"></li>').text(e.message)),l.a11y.speak(e.message)),n.pages.search=-1}),n.currentRequest.always(function(){i.removeClass("loading loading-more"),a.attr("aria-busy","false"),n.loading=!1,n.currentRequest=null})}},initList:function(){var t=this;_.each(u.Menus.data.itemTypes,function(e){t.pages[e.type+":"+e.object]=0}),t.loadItems(u.Menus.data.itemTypes)},loadItems:function(e,t){var i=this,a=[],o={},s=l.template("available-menu-item"),t=_.isString(e)&&_.isString(t)?[{type:e,object:t}]:e;_.each(t,function(e){var t,n=e.type+":"+e.object;-1!==i.pages[n]&&((t=m("#available-menu-items-"+e.type+"-"+e.object)).find(".accordion-section-title").addClass("loading"),o[n]=t,a.push({object:e.object,type:e.type,page:i.pages[n]}))}),0!==a.length&&(i.loading=!0,e=u.previewer.query({excludeCustomizedSaved:!0}),_.extend(e,{"customize-menus-nonce":u.settings.nonce["customize-menus"],wp_customize:"on",item_types:a}),(t=l.ajax.post("load-available-menu-items-customizer",e)).done(function(e){var n;_.each(e.items,function(e,t){0===e.length?(0===i.pages[t]&&o[t].find(".accordion-section-title").addClass("cannot-expand").removeClass("loading").find(".accordion-section-title > button").prop("tabIndex",-1),i.pages[t]=-1):("post_type:page"!==t||o[t].hasClass("open")||o[t].find(".accordion-section-title > button").trigger("click"),e=new u.Menus.AvailableItemCollection(e),i.collection.add(e.models),n=o[t].find(".available-menu-items-list"),e.each(function(e){n.append(s(e.attributes))}),i.pages[t]+=1)})}),t.fail(function(e){"undefined"!=typeof console&&console.error&&console.error(e)}),t.always(function(){_.each(o,function(e){e.find(".accordion-section-title").removeClass("loading")}),i.loading=!1}))},itemSectionHeight:function(){var e=window.innerHeight,t=this.$el.find(".accordion-section:not( #available-menu-items-search ) .accordion-section-content"),n=this.$el.find('.accordion-section:not( #available-menu-items-search ) .available-menu-items-list:not(":only-child")'),e=e-(46*(1+t.length)+14);120<e&&e<290&&(t.css("max-height",e),n.css("max-height",e-60))},select:function(e){this.selected=m(e),this.selected.siblings(".menu-item-tpl").removeClass("selected"),this.selected.addClass("selected")},focus:function(e){this.select(m(e.currentTarget))},_submit:function(e){"keypress"===e.type&&13!==e.which&&32!==e.which||this.submit(m(e.currentTarget))},submit:function(e){var t;(e=e||this.selected)&&this.currentMenuControl&&(this.select(e),t=m(this.selected).data("menu-item-id"),t=this.collection.findWhere({id:t}))&&(this.currentMenuControl.addItemToMenu(t.attributes),m(e).find(".menu-item-handle").addClass("item-added"))},_submitLink:function(e){"keypress"===e.type&&13!==e.which||this.submitLink()},submitLink:function(){var e,t,n,i=m("#custom-menu-item-name"),a=m("#custom-menu-item-url"),o=m("#custom-url-error"),s=m("#custom-name-error"),r=a.val().trim();this.currentMenuControl&&((t=/^((\w+:)?\/\/\w.*|\w+:(?!\/\/$)|\/|\?|#)/).test(r)&&""!==i.val()?(o.hide(),s.hide(),i.removeClass("invalid").removeAttr("aria-invalid","true").removeAttr("aria-describedby","custom-name-error"),a.removeClass("invalid").removeAttr("aria-invalid","true").removeAttr("aria-describedby","custom-name-error"),e={title:i.val(),url:r,type:"custom",type_label:u.Menus.data.l10n.custom_label,object:"custom"},this.currentMenuControl.addItemToMenu(e),a.val("").attr("placeholder","https://"),i.val("")):(t.test(r)||(a.addClass("invalid").attr("aria-invalid","true").attr("aria-describedby","custom-url-error"),o.show(),n=o.text(),l.a11y.speak(n,"assertive")),""===i.val()&&(i.addClass("invalid").attr("aria-invalid","true").attr("aria-describedby","custom-name-error"),s.show(),n=""===n?s.text():n+s.text(),l.a11y.speak(n,"assertive"))))},_submitNew:function(e){"keypress"===e.type&&13!==e.which||this.addingNew||(e=m(e.target).closest(".accordion-section"),this.submitNew(e))},submitNew:function(n){var i=this,a=n.find(".create-item-input"),e=a.val(),t=n.find(".available-menu-items-list"),o=t.data("type"),s=t.data("object"),r=t.data("type_label");this.currentMenuControl&&"post_type"===o&&(""===a.val().trim()?(a.addClass("invalid"),a.focus()):(a.removeClass("invalid"),n.find(".accordion-section-title").addClass("loading"),i.addingNew=!0,a.attr("disabled","disabled"),u.Menus.insertAutoDraftPost({post_title:e,post_type:s}).done(function(e){var t,e=new u.Menus.AvailableItemModel({id:"post-"+e.post_id,title:a.val(),type:o,type_label:r,object:s,object_id:e.post_id,url:e.url});i.currentMenuControl.addItemToMenu(e.attributes),u.Menus.availableMenuItemsPanel.collection.add(e),t=n.find(".available-menu-items-list"),(e=m(l.template("available-menu-item")(e.attributes))).find(".menu-item-handle:first").addClass("item-added"),t.prepend(e),t.scrollTop(),a.val("").removeAttr("disabled"),i.addingNew=!1,n.find(".accordion-section-title").removeClass("loading")})))},open:function(e){var t,n=this;this.currentMenuControl=e,this.itemSectionHeight(),u.section.has("publish_settings")&&u.section("publish_settings").collapse(),m("body").addClass("adding-menu-items"),t=function(){n.close(),m(this).off("click",t)},m("#customize-preview").on("click",t),_(this.currentMenuControl.getMenuItemControls()).each(function(e){e.collapseForm()}),this.$el.find(".selected").removeClass("selected"),this.$search.trigger("focus")},close:function(e){(e=e||{}).returnFocus&&this.currentMenuControl&&this.currentMenuControl.container.find(".add-new-menu-item").focus(),this.currentMenuControl=null,this.selected=null,m("body").removeClass("adding-menu-items"),m("#available-menu-items .menu-item-handle.item-added").removeClass("item-added"),this.$search.val("").trigger("input")},keyboardAccessible:function(e){var t=13===e.which,n=27===e.which,i=9===e.which&&e.shiftKey,a=m(e.target).is(this.$search);t&&!this.$search.val()||(a&&i?(this.currentMenuControl.container.find(".add-new-menu-item").focus(),e.preventDefault()):n&&this.close({returnFocus:!0}))}}),u.Menus.MenusPanel=u.Panel.extend({attachEvents:function(){u.Panel.prototype.attachEvents.call(this);var t=this.container.find(".panel-meta"),n=t.find(".customize-help-toggle"),i=t.find(".customize-panel-description"),a=m("#screen-options-wrap"),o=t.find(".customize-screen-options-toggle");o.on("click keydown",function(e){if(!u.utils.isKeydownButNotEnterEvent(e))return e.preventDefault(),i.not(":hidden")&&(i.slideUp("fast"),n.attr("aria-expanded","false")),"true"===o.attr("aria-expanded")?(o.attr("aria-expanded","false"),t.removeClass("open"),t.removeClass("active-menu-screen-options"),a.slideUp("fast")):(o.attr("aria-expanded","true"),t.addClass("open"),t.addClass("active-menu-screen-options"),a.slideDown("fast")),!1}),n.on("click keydown",function(e){u.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),"true"===o.attr("aria-expanded")&&(o.attr("aria-expanded","false"),n.attr("aria-expanded","true"),t.addClass("open"),t.removeClass("active-menu-screen-options"),a.slideUp("fast"),i.slideDown("fast")))})},ready:function(){var e=this;e.container.find(".hide-column-tog").on("click",function(){e.saveManageColumnsState()}),u.section("menu_locations",function(e){e.headContainer.prepend(l.template("nav-menu-locations-header")(u.Menus.data))})},saveManageColumnsState:_.debounce(function(){var e=this;e._updateHiddenColumnsRequest&&e._updateHiddenColumnsRequest.abort(),e._updateHiddenColumnsRequest=l.ajax.post("hidden-columns",{hidden:e.hidden(),screenoptionnonce:m("#screenoptionnonce").val(),page:"nav-menus"}),e._updateHiddenColumnsRequest.always(function(){e._updateHiddenColumnsRequest=null})},2e3),checked:function(){},unchecked:function(){},hidden:function(){return m(".hide-column-tog").not(":checked").map(function(){var e=this.id;return e.substring(0,e.length-5)}).get().join(",")}}),u.Menus.MenuSection=u.Section.extend({initialize:function(e,t){u.Section.prototype.initialize.call(this,e,t),this.deferred.initSortables=m.Deferred()},ready:function(){var e,t,n=this;if(void 0===n.params.menu_id)throw new Error("params.menu_id was not defined");n.active.validate=function(){return!!u.has(n.id)&&!!u(n.id).get()},n.populateControls(),n.navMenuLocationSettings={},n.assignedLocations=new u.Value([]),u.each(function(e,t){t=t.match(/^nav_menu_locations\[(.+?)]/);t&&(n.navMenuLocationSettings[t[1]]=e).bind(function(){n.refreshAssignedLocations()})}),n.assignedLocations.bind(function(e){n.updateAssignedLocationsInSectionTitle(e)}),n.refreshAssignedLocations(),u.bind("pane-contents-reflowed",function(){n.contentContainer.parent().length&&(n.container.find(".menu-item .menu-item-reorder-nav button").attr({tabindex:"0","aria-hidden":"false"}),n.container.find(".menu-item.move-up-disabled .menus-move-up").attr({tabindex:"-1","aria-hidden":"true"}),n.container.find(".menu-item.move-down-disabled .menus-move-down").attr({tabindex:"-1","aria-hidden":"true"}),n.container.find(".menu-item.move-left-disabled .menus-move-left").attr({tabindex:"-1","aria-hidden":"true"}),n.container.find(".menu-item.move-right-disabled .menus-move-right").attr({tabindex:"-1","aria-hidden":"true"}))}),t=function(){var e="field-"+m(this).val()+"-active";n.contentContainer.toggleClass(e,m(this).prop("checked"))},(e=u.panel("nav_menus").contentContainer.find(".metabox-prefs:first").find(".hide-column-tog")).each(t),e.on("click",t)},populateControls:function(){var e,t=this,n=t.id+"[name]",i=u.control(n);i||(i=new u.controlConstructor.nav_menu_name(n,{type:"nav_menu_name",label:u.Menus.data.l10n.menuNameLabel,section:t.id,priority:0,settings:{default:t.id}}),u.control.add(i),i.active.set(!0)),(n=u.control(t.id))||(n=new u.controlConstructor.nav_menu(t.id,{type:"nav_menu",section:t.id,priority:998,settings:{default:t.id},menu_id:t.params.menu_id}),u.control.add(n),n.active.set(!0)),i=t.id+"[locations]",u.control(i)||(i=new u.controlConstructor.nav_menu_locations(i,{section:t.id,priority:999,settings:{default:t.id},menu_id:t.params.menu_id}),u.control.add(i.id,i),n.active.set(!0)),i=t.id+"[auto_add]",(n=u.control(i))||(n=new u.controlConstructor.nav_menu_auto_add(i,{type:"nav_menu_auto_add",label:"",section:t.id,priority:1e3,settings:{default:t.id}}),u.control.add(n),n.active.set(!0)),i=t.id+"[delete]",(e=u.control(i))||(e=new u.Control(i,{section:t.id,priority:1001,templateId:"nav-menu-delete-button"}),u.control.add(e.id,e),e.active.set(!0),e.deferred.embedded.done(function(){e.container.find("button").on("click",function(){var e=t.params.menu_id;u.Menus.getMenuControl(e).setting.set(!1)})}))},refreshAssignedLocations:function(){var n=this.params.menu_id,i=[];_.each(this.navMenuLocationSettings,function(e,t){e()===n&&i.push(t)}),this.assignedLocations.set(i)},updateAssignedLocationsInSectionTitle:function(e){var n=this.container.find(".accordion-section-title button:first");n.find(".menu-in-location").remove(),_.each(e,function(e){var t=m('<span class="menu-in-location"></span>'),e=u.Menus.data.locationSlugMappedToName[e];t.text(u.Menus.data.l10n.menuLocation.replace("%s",e)),n.append(t)}),this.container.toggleClass("assigned-to-menu-location",0!==e.length)},onChangeExpanded:function(e,t){var n,i=this;e&&(wpNavMenu.menuList=i.contentContainer,wpNavMenu.targetList=wpNavMenu.menuList,m("#menu-to-edit").removeAttr("id"),wpNavMenu.menuList.attr("id","menu-to-edit").addClass("menu"),u.Menus.MenuItemControl.prototype.initAccessibility(),_.each(u.section(i.id).controls(),function(e){"nav_menu_item"===e.params.type&&e.actuallyEmbed()}),t.completeCallback&&(n=t.completeCallback),t.completeCallback=function(){"resolved"!==i.deferred.initSortables.state()&&(wpNavMenu.initSortables(),i.deferred.initSortables.resolve(wpNavMenu.menuList),u.control("nav_menu["+String(i.params.menu_id)+"]").reflowMenuItems()),_.isFunction(n)&&n()}),u.Section.prototype.onChangeExpanded.call(i,e,t)},highlightNewItemButton:function(){u.utils.highlightButton(this.contentContainer.find(".add-new-menu-item"),{delay:2e3})}}),u.Menus.createNavMenu=function(e){var t=u.Menus.generatePlaceholderAutoIncrementId(),n="nav_menu["+String(t)+"]";return u.create(n,n,{},{type:"nav_menu",transport:u.Menus.data.settingTransport,previewer:u.previewer}).set(m.extend({},u.Menus.data.defaultSettingValues.nav_menu,{name:e||""})),u.section.add(new u.Menus.MenuSection(n,{panel:"nav_menus",title:c(e),customizeAction:u.Menus.data.l10n.customizingMenus,priority:10,menu_id:t}))},u.Menus.NewMenuSection=u.Section.extend({attachEvents:function(){var t=this,e=t.container,n=t.contentContainer,i=/^nav_menu\[/;function a(){var t;e.find(".add-new-menu-notice").prop("hidden",(t=0,u.each(function(e){i.test(e.id)&&!1!==e.get()&&(t+=1)}),0<t))}function o(e){i.test(e.id)&&(e.bind(a),a())}t.headContainer.find(".accordion-section-title").replaceWith(l.template("nav-menu-create-menu-section-title")),e.on("click",".customize-add-menu-button",function(){t.expand()}),n.on("keydown",".menu-name-field",function(e){13===e.which&&t.submit()}),n.on("click","#customize-new-menu-submit",function(e){t.submit(),e.stopPropagation(),e.preventDefault()}),u.each(o),u.bind("add",o),u.bind("removed",function(e){i.test(e.id)&&(e.unbind(a),a())}),a(),u.Section.prototype.attachEvents.apply(t,arguments)},ready:function(){this.populateControls()},populateControls:function(){var e=this,t=e.id+"[name]",n=u.control(t);n||(n=new u.controlConstructor.nav_menu_name(t,{label:u.Menus.data.l10n.menuNameLabel,description:u.Menus.data.l10n.newMenuNameDescription,section:e.id,priority:0}),u.control.add(n.id,n),n.active.set(!0)),t=e.id+"[locations]",(n=u.control(t))||(n=new u.controlConstructor.nav_menu_locations(t,{section:e.id,priority:1,menu_id:"",isCreating:!0}),u.control.add(t,n),n.active.set(!0)),t=e.id+"[submit]",(n=u.control(t))||(n=new u.Control(t,{section:e.id,priority:1,templateId:"nav-menu-submit-new-button"}),u.control.add(t,n),n.active.set(!0))},submit:function(){var t,e=this.contentContainer,n=e.find(".menu-name-field").first(),i=n.val();i?(t=u.Menus.createNavMenu(i),n.val(""),n.removeClass("invalid"),e.find(".assigned-menu-location input[type=checkbox]").each(function(){var e=m(this);e.prop("checked")&&(u("nav_menu_locations["+e.data("location-id")+"]").set(t.params.menu_id),e.prop("checked",!1))}),l.a11y.speak(u.Menus.data.l10n.menuAdded),t.focus({completeCallback:function(){t.highlightNewItemButton()}})):(n.addClass("invalid"),n.focus())},selectDefaultLocation:function(e){var t=u.control(this.id+"[locations]"),n={};null!==e&&(n[e]=!0),t.setSelections(n)}}),u.Menus.MenuLocationControl=u.Control.extend({initialize:function(e,t){var n=e.match(/^nav_menu_locations\[(.+?)]/);this.themeLocation=n[1],u.Control.prototype.initialize.call(this,e,t)},ready:function(){var n=this,i=/^nav_menu\[(-?\d+)]/;n.setting.validate=function(e){return""===e?0:parseInt(e,10)},n.container.find(".create-menu").on("click",function(){var e=u.section("add_menu");e.selectDefaultLocation(this.dataset.locationId),e.focus()}),n.container.find(".edit-menu").on("click",function(){var e=n.setting();u.section("nav_menu["+e+"]").focus()}),n.setting.bind("change",function(){var e=0!==n.setting();n.container.find(".create-menu").toggleClass("hidden",e),n.container.find(".edit-menu").toggleClass("hidden",!e)}),u.bind("add",function(e){var t=e.id.match(i);t&&!1!==e()&&(t=t[1],e=new Option(c(e().name),t),n.container.find("select").append(e))}),u.bind("remove",function(e){var e=e.id.match(i);e&&(e=parseInt(e[1],10),n.setting()===e&&n.setting.set(""),n.container.find("option[value="+e+"]").remove())}),u.bind("change",function(e){var t=e.id.match(i);t&&(t=parseInt(t[1],10),!1===e()?(n.setting()===t&&n.setting.set(""),n.container.find("option[value="+t+"]").remove()):n.container.find("option[value="+t+"]").text(c(e().name)))})}}),u.Menus.MenuItemControl=u.Control.extend({initialize:function(e,t){var n=this;n.expanded=new u.Value(!1),n.expandedArgumentsQueue=[],n.expanded.bind(function(e){var t=n.expandedArgumentsQueue.shift(),t=m.extend({},n.defaultExpandedArguments,t);n.onChangeExpanded(e,t)}),u.Control.prototype.initialize.call(n,e,t),n.active.validate=function(){var e=u.section(n.section()),e=!!e&&e.active();return e}},initAccessibility:function(){var e=this,t=m("#menu-to-edit");t.on("mouseenter.refreshAccessibility focus.refreshAccessibility touchstart.refreshAccessibility",".menu-item",function(){e.refreshAdvancedAccessibilityOfItem(m(this).find("button.item-edit"))}),t.on("click","button.item-edit",function(){e.refreshAdvancedAccessibilityOfItem(m(this))})},refreshAdvancedAccessibilityOfItem:function(e){var t,n,i,a,o,s,r,d;!0===m(e).data("needs_accessibility_refresh")&&(o=0===(a=(i=(e=m(e)).closest("li.menu-item").first()).menuItemDepth()),s=e.closest(".menu-item-handle").find(".menu-item-title").text(),r=e.closest(".menu-item-handle").find(".item-type").text(),d=m("#menu-to-edit li").length,d=o?(t=(o=m(".menu-item-depth-0")).index(i)+1,d=o.length,menus.menuFocus.replace("%1$s",s).replace("%2$s",r).replace("%3$d",t).replace("%4$d",d)):(d=(o=i.prevAll(".menu-item-depth-"+parseInt(a-1,10)).first()).find(".menu-item-data-db-id").val(),o=o.find(".menu-item-title").text(),n=(d=m('.menu-item .menu-item-data-parent-id[value="'+d+'"]')).length,t=m(d.parents(".menu-item").get().reverse()).index(i)+1,a<2?menus.subMenuFocus.replace("%1$s",s).replace("%2$s",r).replace("%3$d",t).replace("%4$d",n).replace("%5$s",o):menus.subMenuMoreDepthFocus.replace("%1$s",s).replace("%2$s",r).replace("%3$d",t).replace("%4$d",n).replace("%5$s",o).replace("%6$d",a)),e.find(".screen-reader-text").text(d),e.data("needs_accessibility_refresh",!1))},embed:function(){var e=this.section();e&&((e=u.section(e))&&e.expanded()||u.settings.autofocus.control===this.id)&&this.actuallyEmbed()},actuallyEmbed:function(){"resolved"!==this.deferred.embedded.state()&&(this.renderContent(),this.deferred.embedded.resolve(),m("button.item-edit").data("needs_accessibility_refresh",!0))},ready:function(){if(void 0===this.params.menu_item_id)throw new Error("params.menu_item_id was not defined");this._setupControlToggle(),this._setupReorderUI(),this._setupUpdateUI(),this._setupRemoveUI(),this._setupLinksUI(),this._setupTitleUI()},_setupControlToggle:function(){var i=this;this.container.find(".menu-item-handle").on("click",function(e){e.preventDefault(),e.stopPropagation();var t=i.getMenuControl(),n=m(e.target).is(".item-delete, .item-delete *"),e=m(e.target).is(".add-new-menu-item, .add-new-menu-item *");!m("body").hasClass("adding-menu-items")||n||e||u.Menus.availableMenuItemsPanel.close(),t.isReordering||t.isSorting||i.toggleForm()})},_setupReorderUI:function(){var o=this,e=l.template("menu-item-reorder-nav");o.container.find(".item-controls").after(e),o.container.find(".menu-item-reorder-nav").find(".menus-move-up, .menus-move-down, .menus-move-left, .menus-move-right").on("click",function(){var e=m(this),t=(o.params.depth=o.getDepth(),e.focus(),e.is(".menus-move-up")),n=e.is(".menus-move-down"),i=e.is(".menus-move-left"),a=e.is(".menus-move-right");t?o.moveUp():n?o.moveDown():i?o.moveLeft():a&&(o.moveRight(),o.params.depth+=1),e.focus(),m("button.item-edit").data("needs_accessibility_refresh",!0)})},_setupUpdateUI:function(){var e,s=this,t=s.setting();s.elements={},s.elements.url=new u.Element(s.container.find(".edit-menu-item-url")),s.elements.title=new u.Element(s.container.find(".edit-menu-item-title")),s.elements.attr_title=new u.Element(s.container.find(".edit-menu-item-attr-title")),s.elements.target=new u.Element(s.container.find(".edit-menu-item-target")),s.elements.classes=new u.Element(s.container.find(".edit-menu-item-classes")),s.elements.xfn=new u.Element(s.container.find(".edit-menu-item-xfn")),s.elements.description=new u.Element(s.container.find(".edit-menu-item-description")),_.each(s.elements,function(n,i){n.bind(function(e){n.element.is("input[type=checkbox]")&&(e=e?n.element.val():"");var t=s.setting();t&&t[i]!==e&&((t=_.clone(t))[i]=e,s.setting.set(t))}),t&&("classes"!==i&&"xfn"!==i||!_.isArray(t[i])?n.set(t[i]):n.set(t[i].join(" ")))}),s.setting.bind(function(n,i){var e,t=s.params.menu_item_id,a=[],o=[];!1===n?(e=u.control("nav_menu["+String(i.nav_menu_term_id)+"]"),s.container.remove(),_.each(e.getMenuItemControls(),function(e){i.menu_item_parent===e.setting().menu_item_parent&&e.setting().position>i.position?a.push(e):e.setting().menu_item_parent===t&&o.push(e)}),_.each(a,function(e){var t=_.clone(e.setting());t.position+=o.length,e.setting.set(t)}),_.each(o,function(e,t){var n=_.clone(e.setting());n.position=i.position+t,n.menu_item_parent=i.menu_item_parent,e.setting.set(n)}),e.debouncedReflowMenuItems()):(_.each(n,function(e,t){s.elements[t]&&s.elements[t].set(n[t])}),s.container.find(".menu-item-data-parent-id").val(n.menu_item_parent),n.position===i.position&&n.menu_item_parent===i.menu_item_parent||s.getMenuControl().debouncedReflowMenuItems())}),s.setting.notifications.bind("add",e=function(){s.elements.url.element.toggleClass("invalid",s.setting.notifications.has("invalid_url"))}),s.setting.notifications.bind("removed",e)},_setupRemoveUI:function(){var r=this;r.container.find(".item-delete").on("click",function(){var e,t,n,i=!0,a=0,o=r.params.original_item_id,s=r.getMenuControl().$sectionContent.find(".menu-item");m("body").hasClass("adding-menu-items")||(i=!1),n=r.container.nextAll(".customize-control-nav_menu_item:visible").first(),t=r.container.prevAll(".customize-control-nav_menu_item:visible").first(),e=(n.length?n.find(!1===i?".item-edit":".item-delete"):t.length?t.find(!1===i?".item-edit":".item-delete"):r.container.nextAll(".customize-control-nav_menu").find(".add-new-menu-item")).first(),_.each(s,function(e){m(e).is(":visible")&&(e=e.getAttribute("id").match(/^customize-control-nav_menu_item-(-?\d+)$/,""))&&(e=parseInt(e[1],10),e=u.control("nav_menu_item["+String(e)+"]"))&&o==e.params.original_item_id&&a++}),a<=1&&((n=m("#menu-item-tpl-"+r.params.original_item_id)).removeClass("selected"),n.find(".menu-item-handle").removeClass("item-added")),r.container.slideUp(function(){r.setting.set(!1),l.a11y.speak(u.Menus.data.l10n.itemDeleted),e.focus()}),r.setting.set(!1)})},_setupLinksUI:function(){this.container.find("a.original-link").on("click",function(e){e.preventDefault(),u.previewer.previewUrl(e.target.toString())})},_setupTitleUI:function(){var i;this.container.find(".edit-menu-item-title").on("blur",function(){m(this).val(m(this).val().trim())}),i=this.container.find(".menu-item-title"),this.setting.bind(function(e){var t,n;e&&(e.title=e.title||"",n=(t=e.title.trim())||e.original_title||u.Menus.data.l10n.untitled,e._invalid&&(n=u.Menus.data.l10n.invalidTitleTpl.replace("%s",n)),t||e.original_title?i.text(n).removeClass("no-title"):i.text(n).addClass("no-title"))})},getDepth:function(){var e=this,t=e.setting(),n=0;if(!t)return 0;for(;t&&t.menu_item_parent&&(n+=1,e=u.control("nav_menu_item["+t.menu_item_parent+"]"));)t=e.setting();return n},renderContent:function(){var e,t=this,n=t.setting();t.params.title=n.title||"",t.params.depth=t.getDepth(),t.container.data("item-depth",t.params.depth),e=["menu-item","menu-item-depth-"+String(t.params.depth),"menu-item-"+n.object,"menu-item-edit-inactive"],n._invalid?(e.push("menu-item-invalid"),t.params.title=u.Menus.data.l10n.invalidTitleTpl.replace("%s",t.params.title)):"draft"===n.status&&(e.push("pending"),t.params.title=u.Menus.data.pendingTitleTpl.replace("%s",t.params.title)),t.params.el_classes=e.join(" "),t.params.item_type_label=n.type_label,t.params.item_type=n.type,t.params.url=n.url,t.params.target=n.target,t.params.attr_title=n.attr_title,t.params.classes=_.isArray(n.classes)?n.classes.join(" "):n.classes,t.params.xfn=n.xfn,t.params.description=n.description,t.params.parent=n.menu_item_parent,t.params.original_title=n.original_title||"",t.container.addClass(t.params.el_classes),u.Control.prototype.renderContent.call(t)},getMenuControl:function(){var e=this.setting();return e&&e.nav_menu_term_id?u.control("nav_menu["+e.nav_menu_term_id+"]"):null},expandControlSection:function(){var e=this.container.closest(".accordion-section");e.hasClass("open")||e.find(".accordion-section-title:first").trigger("click")},_toggleExpanded:u.Section.prototype._toggleExpanded,expand:u.Section.prototype.expand,expandForm:function(e){this.expand(e)},collapse:u.Section.prototype.collapse,collapseForm:function(e){this.collapse(e)},toggleForm:function(e,t){(e=void 0===e?!this.expanded():e)?this.expand(t):this.collapse(t)},onChangeExpanded:function(e,t){var n,i=this,a=this.container,o=a.find(".menu-item-settings:first");void 0===e&&(e=!o.is(":visible")),o.is(":visible")===e?t&&t.completeCallback&&t.completeCallback():e?(u.control.each(function(e){i.params.type===e.params.type&&i!==e&&e.collapseForm()}),n=function(){a.removeClass("menu-item-edit-inactive").addClass("menu-item-edit-active"),i.container.trigger("expanded"),t&&t.completeCallback&&t.completeCallback()},a.find(".item-edit").attr("aria-expanded","true"),o.slideDown("fast",n),i.container.trigger("expand")):(n=function(){a.addClass("menu-item-edit-inactive").removeClass("menu-item-edit-active"),i.container.trigger("collapsed"),t&&t.completeCallback&&t.completeCallback()},i.container.trigger("collapse"),a.find(".item-edit").attr("aria-expanded","false"),o.slideUp("fast",n))},focus:function(e){var t=this,n=(e=e||{}).completeCallback,i=function(){t.expandControlSection(),e.completeCallback=function(){t.container.find(".menu-item-settings").find("input, select, textarea, button, object, a[href], [tabindex]").filter(":visible").first().focus(),n&&n()},t.expandForm(e)};u.section.has(t.section())?u.section(t.section()).expand({completeCallback:i}):i()},moveUp:function(){this._changePosition(-1),l.a11y.speak(u.Menus.data.l10n.movedUp)},moveDown:function(){this._changePosition(1),l.a11y.speak(u.Menus.data.l10n.movedDown)},moveLeft:function(){this._changeDepth(-1),l.a11y.speak(u.Menus.data.l10n.movedLeft)},moveRight:function(){this._changeDepth(1),l.a11y.speak(u.Menus.data.l10n.movedRight)},_changePosition:function(e){var t,n=this,i=_.clone(n.setting()),a=[];if(1!==e&&-1!==e)throw new Error("Offset changes by 1 are only supported.");if(n.setting()){if(_(n.getMenuControl().getMenuItemControls()).each(function(e){e.setting().menu_item_parent===i.menu_item_parent&&a.push(e.setting)}),a.sort(function(e,t){return e().position-t().position}),-1===(t=_.indexOf(a,n.setting)))throw new Error("Expected setting to be among siblings.");0===t&&e<0||t===a.length-1&&0<e||((t=a[t+e])&&t.set(m.extend(_.clone(t()),{position:i.position})),i.position+=e,n.setting.set(i))}},_changeDepth:function(e){if(1!==e&&-1!==e)throw new Error("Offset changes by 1 are only supported.");var t,n,i=this,a=_.clone(i.setting()),o=[];if(_(i.getMenuControl().getMenuItemControls()).each(function(e){e.setting().menu_item_parent===a.menu_item_parent&&o.push(e)}),o.sort(function(e,t){return e.setting().position-t.setting().position}),-1===(t=_.indexOf(o,i)))throw new Error("Expected control to be among siblings.");-1===e?a.menu_item_parent&&(n=u.control("nav_menu_item["+a.menu_item_parent+"]"),_(o).chain().slice(t).each(function(e,t){e.setting.set(m.extend({},e.setting(),{menu_item_parent:i.params.menu_item_id,position:t}))}),_(i.getMenuControl().getMenuItemControls()).each(function(e){var t;e.setting().menu_item_parent===n.setting().menu_item_parent&&e.setting().position>n.setting().position&&(t=_.clone(e.setting()),e.setting.set(m.extend(t,{position:t.position+1})))}),a.position=n.setting().position+1,a.menu_item_parent=n.setting().menu_item_parent,i.setting.set(a)):1===e&&0!==t&&(a.menu_item_parent=o[t-1].params.menu_item_id,a.position=0,_(i.getMenuControl().getMenuItemControls()).each(function(e){e.setting().menu_item_parent===a.menu_item_parent&&(a.position=Math.max(a.position,e.setting().position))}),a.position+=1,i.setting.set(a))}}),u.Menus.MenuNameControl=u.Control.extend({ready:function(){var e,n=this;n.setting&&(e=n.setting(),n.nameElement=new u.Element(n.container.find(".menu-name-field")),n.nameElement.bind(function(e){var t=n.setting();t&&t.name!==e&&((t=_.clone(t)).name=e,n.setting.set(t))}),e&&n.nameElement.set(e.name),n.setting.bind(function(e){e&&n.nameElement.set(e.name)}))}}),u.Menus.MenuLocationsControl=u.Control.extend({ready:function(){var d=this;d.container.find(".assigned-menu-location").each(function(){function t(e){var t=u("nav_menu["+String(e)+"]");e&&t&&t()?n.find(".theme-location-set").show().find("span").text(c(t().name)):n.find(".theme-location-set").hide()}var n=m(this),e=n.find("input[type=checkbox]"),i=new u.Element(e),a=u("nav_menu_locations["+e.data("location-id")+"]"),o=""===d.params.menu_id,s=o?_.noop:function(e){i.set(e)},r=o?_.noop:function(e){a.set(e?d.params.menu_id:0)};s(a.get()===d.params.menu_id),e.on("change",function(){r(this.checked)}),a.bind(function(e){s(e===d.params.menu_id),t(e)}),t(a.get())})},setSelections:function(i){this.container.find(".menu-location").each(function(e,t){var n=t.dataset.locationId;t.checked=n in i&&i[n]})}}),u.Menus.MenuAutoAddControl=u.Control.extend({ready:function(){var n=this,e=n.setting();n.active.validate=function(){var e=u.section(n.section()),e=!!e&&e.active();return e},n.autoAddElement=new u.Element(n.container.find("input[type=checkbox].auto_add")),n.autoAddElement.bind(function(e){var t=n.setting();t&&t.name!==e&&((t=_.clone(t)).auto_add=e,n.setting.set(t))}),e&&n.autoAddElement.set(e.auto_add),n.setting.bind(function(e){e&&n.autoAddElement.set(e.auto_add)})}}),u.Menus.MenuControl=u.Control.extend({ready:function(){var t,n,i=this,a=u.section(i.section()),o=i.params.menu_id,e=i.setting();if(void 0===this.params.menu_id)throw new Error("params.menu_id was not defined");i.active.validate=function(){var e=!!a&&a.active();return e},i.$controlSection=a.headContainer,i.$sectionContent=i.container.closest(".accordion-section-content"),this._setupModel(),u.section(i.section(),function(e){e.deferred.initSortables.done(function(e){i._setupSortable(e)})}),this._setupAddition(),this._setupTitle(),e&&(t=c(e.name),u.control.each(function(e){e.extended(u.controlConstructor.widget_form)&&"nav_menu"===e.params.widget_id_base&&(e.container.find(".nav-menu-widget-form-controls:first").show(),e.container.find(".nav-menu-widget-no-menus-message:first").hide(),0===(n=e.container.find("select")).find("option[value="+String(o)+"]").length)&&n.append(new Option(t,o))}),(e=m("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )")).find(".nav-menu-widget-form-controls:first").show(),e.find(".nav-menu-widget-no-menus-message:first").hide(),0===(n=e.find(".widget-inside select:first")).find("option[value="+String(o)+"]").length)&&n.append(new Option(t,o)),_.defer(function(){i.updateInvitationVisibility()})},_setupModel:function(){var n=this,i=n.params.menu_id;n.setting.bind(function(e){var t;!1===e?n._handleDeletion():(t=c(e.name),u.control.each(function(e){e.extended(u.controlConstructor.widget_form)&&"nav_menu"===e.params.widget_id_base&&e.container.find("select").find("option[value="+String(i)+"]").text(t)}))})},_setupSortable:function(e){var a=this;if(!e.is(a.$sectionContent))throw new Error("Unexpected menuList.");e.on("sortstart",function(){a.isSorting=!0}),e.on("sortstop",function(){setTimeout(function(){var e=a.$sectionContent.sortable("toArray"),t=[],n=0,i=10;a.isSorting=!1,a.$sectionContent.scrollLeft(0),_.each(e,function(e){var e=e.match(/^customize-control-nav_menu_item-(-?\d+)$/,"");e&&(e=parseInt(e[1],10),e=u.control("nav_menu_item["+String(e)+"]"))&&t.push(e)}),_.each(t,function(e){var t;!1!==e.setting()&&(t=_.clone(e.setting()),n+=1,i+=1,t.position=n,e.priority(i),t.menu_item_parent=parseInt(e.container.find(".menu-item-data-parent-id").val(),10),t.menu_item_parent||(t.menu_item_parent=0),e.setting.set(t))}),m("button.item-edit").data("needs_accessibility_refresh",!0)})}),a.isReordering=!1,this.container.find(".reorder-toggle").on("click",function(){a.toggleReordering(!a.isReordering)})},_setupAddition:function(){var t=this;this.container.find(".add-new-menu-item").on("click",function(e){t.$sectionContent.hasClass("reordering")||(m("body").hasClass("adding-menu-items")?(m(this).attr("aria-expanded","false"),u.Menus.availableMenuItemsPanel.close(),e.stopPropagation()):(m(this).attr("aria-expanded","true"),u.Menus.availableMenuItemsPanel.open(t)))})},_handleDeletion:function(){var e,n=this.params.menu_id,i=0,t=u.section(this.section()),a=function(){t.container.remove(),u.section.remove(t.id)};t&&t.expanded()?t.collapse({completeCallback:function(){a(),l.a11y.speak(u.Menus.data.l10n.menuDeleted),u.panel("nav_menus").focus()}}):a(),u.each(function(e){/^nav_menu\[/.test(e.id)&&!1!==e()&&(i+=1)}),u.control.each(function(e){var t;e.extended(u.controlConstructor.widget_form)&&"nav_menu"===e.params.widget_id_base&&((t=e.container.find("select")).val()===String(n)&&t.prop("selectedIndex",0).trigger("change"),e.container.find(".nav-menu-widget-form-controls:first").toggle(0!==i),e.container.find(".nav-menu-widget-no-menus-message:first").toggle(0===i),e.container.find("option[value="+String(n)+"]").remove())}),(e=m("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )")).find(".nav-menu-widget-form-controls:first").toggle(0!==i),e.find(".nav-menu-widget-no-menus-message:first").toggle(0===i),e.find("option[value="+String(n)+"]").remove()},_setupTitle:function(){var d=this;d.setting.bind(function(e){var t,n,i,a,o,s,r;e&&(t=u.section(d.section()),n=d.params.menu_id,i=t.headContainer.find(".accordion-section-title"),a=t.contentContainer.find(".customize-section-title h3"),o=t.headContainer.find(".menu-in-location"),s=a.find(".customize-action"),r=c(e.name),i.text(r),o.length&&o.appendTo(i),a.text(r),s.length&&s.prependTo(a),u.control.each(function(e){/^nav_menu_locations\[/.test(e.id)&&e.container.find("option[value="+n+"]").text(r)}),t.contentContainer.find(".customize-control-checkbox input").each(function(){m(this).prop("checked")&&m(".current-menu-location-name-"+m(this).data("location-id")).text(r)}))})},toggleReordering:function(e){var t=this.container.find(".add-new-menu-item"),n=this.container.find(".reorder-toggle"),i=this.$sectionContent.find(".item-title");(e=Boolean(e))!==this.$sectionContent.hasClass("reordering")&&(this.isReordering=e,this.$sectionContent.toggleClass("reordering",e),this.$sectionContent.sortable(this.isReordering?"disable":"enable"),this.isReordering?(t.attr({tabindex:"-1","aria-hidden":"true"}),n.attr("aria-label",u.Menus.data.l10n.reorderLabelOff),l.a11y.speak(u.Menus.data.l10n.reorderModeOn),i.attr("aria-hidden","false")):(t.removeAttr("tabindex aria-hidden"),n.attr("aria-label",u.Menus.data.l10n.reorderLabelOn),l.a11y.speak(u.Menus.data.l10n.reorderModeOff),i.attr("aria-hidden","true")),e)&&_(this.getMenuItemControls()).each(function(e){e.collapseForm()})},getMenuItemControls:function(){var t=[],n=this.params.menu_id;return u.control.each(function(e){"nav_menu_item"===e.params.type&&e.setting()&&n===e.setting().nav_menu_term_id&&t.push(e)}),t},reflowMenuItems:function(){var e=this.getMenuItemControls(),a=function(n){var t=[],i=n.currentParent;_.each(n.menuItemControls,function(e){i===e.setting().menu_item_parent&&t.push(e)}),t.sort(function(e,t){return e.setting().position-t.setting().position}),_.each(t,function(t){n.currentAbsolutePosition+=1,t.priority.set(n.currentAbsolutePosition),t.container.hasClass("menu-item-depth-"+String(n.currentDepth))||(_.each(t.container.prop("className").match(/menu-item-depth-\d+/g),function(e){t.container.removeClass(e)}),t.container.addClass("menu-item-depth-"+String(n.currentDepth))),t.container.data("item-depth",n.currentDepth),n.currentDepth+=1,n.currentParent=t.params.menu_item_id,a(n),--n.currentDepth,n.currentParent=i}),t.length&&(_(t).each(function(e){e.container.removeClass("move-up-disabled move-down-disabled move-left-disabled move-right-disabled"),0===n.currentDepth?e.container.addClass("move-left-disabled"):10===n.currentDepth&&e.container.addClass("move-right-disabled")}),t[0].container.addClass("move-up-disabled").addClass("move-right-disabled").toggleClass("move-down-disabled",1===t.length),t[t.length-1].container.addClass("move-down-disabled").toggleClass("move-up-disabled",1===t.length))};a({menuItemControls:e,currentParent:0,currentDepth:0,currentAbsolutePosition:0}),this.updateInvitationVisibility(e),this.container.find(".reorder-toggle").toggle(1<e.length)},debouncedReflowMenuItems:_.debounce(function(){this.reflowMenuItems.apply(this,arguments)},0),addItemToMenu:function(e){var t,n,i,a=0,o=10,s=e.id||"";return _.each(this.getMenuItemControls(),function(e){!1!==e.setting()&&(o=Math.max(o,e.priority()),0===e.setting().menu_item_parent)&&(a=Math.max(a,e.setting().position))}),a+=1,o+=1,delete(e=m.extend({},u.Menus.data.defaultSettingValues.nav_menu_item,e,{nav_menu_term_id:this.params.menu_id,original_title:e.title,position:a})).id,i=u.Menus.generatePlaceholderAutoIncrementId(),t="nav_menu_item["+String(i)+"]",n={type:"nav_menu_item",transport:u.Menus.data.settingTransport,previewer:u.previewer},(n=u.create(t,t,{},n)).set(e),e=new u.controlConstructor.nav_menu_item(t,{type:"nav_menu_item",section:this.id,priority:o,settings:{default:t},menu_item_id:i,original_item_id:s}),u.control.add(e),n.preview(),this.debouncedReflowMenuItems(),l.a11y.speak(u.Menus.data.l10n.itemAdded),e},updateInvitationVisibility:function(e){e=e||this.getMenuItemControls();this.container.find(".new-menu-item-invitation").toggle(0===e.length)}}),m.extend(u.controlConstructor,{nav_menu_location:u.Menus.MenuLocationControl,nav_menu_item:u.Menus.MenuItemControl,nav_menu:u.Menus.MenuControl,nav_menu_name:u.Menus.MenuNameControl,nav_menu_locations:u.Menus.MenuLocationsControl,nav_menu_auto_add:u.Menus.MenuAutoAddControl}),m.extend(u.panelConstructor,{nav_menus:u.Menus.MenusPanel}),m.extend(u.sectionConstructor,{nav_menu:u.Menus.MenuSection,new_menu:u.Menus.NewMenuSection}),u.bind("ready",function(){u.Menus.availableMenuItemsPanel=new u.Menus.AvailableMenuItemsPanelView({collection:u.Menus.availableMenuItems}),u.bind("saved",function(e){(e.nav_menu_updates||e.nav_menu_item_updates)&&u.Menus.applySavedData(e)}),u.state("changesetStatus").bind(function(e){"publish"===e&&(u("nav_menus_created_posts")._value=[])}),u.previewer.bind("focus-nav-menu-item-control",u.Menus.focusMenuItemControl)}),u.Menus.applySavedData=function(e){var c={},r={};_(e.nav_menu_updates).each(function(n){var e,t,i,a,o,s,r,d;if("inserted"===n.status){if(!n.previous_term_id)throw new Error("Expected previous_term_id");if(!n.term_id)throw new Error("Expected term_id");if(e="nav_menu["+String(n.previous_term_id)+"]",!u.has(e))throw new Error("Expected setting to exist: "+e);if(i=u(e),!u.section.has(e))throw new Error("Expected control to exist: "+e);if(o=u.section(e),!(s=i.get()))throw new Error("Did not expect setting to be empty (deleted).");s=m.extend(_.clone(s),n.saved_value),c[n.previous_term_id]=n.term_id,a="nav_menu["+String(n.term_id)+"]",t=u.create(a,a,s,{type:"nav_menu",transport:u.Menus.data.settingTransport,previewer:u.previewer}),(d=o.expanded())&&o.collapse(),a=new u.Menus.MenuSection(a,{panel:"nav_menus",title:s.name,customizeAction:u.Menus.data.l10n.customizingMenus,type:"nav_menu",priority:o.priority.get(),menu_id:n.term_id}),u.section.add(a),u.control.each(function(e){var t;e.extended(u.controlConstructor.widget_form)&&"nav_menu"===e.params.widget_id_base&&(t=(e=e.container.find("select")).find("option[value="+String(n.previous_term_id)+"]"),e.find("option[value="+String(n.term_id)+"]").prop("selected",t.prop("selected")),t.remove())}),i.callbacks.disable(),i.set(!1),i.preview(),t.preview(),i._dirty=!1,o.container.remove(),u.section.remove(e),r=0,u.each(function(e){/^nav_menu\[/.test(e.id)&&!1!==e()&&(r+=1)}),(s=m("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )")).find(".nav-menu-widget-form-controls:first").toggle(0!==r),s.find(".nav-menu-widget-no-menus-message:first").toggle(0===r),s.find("option[value="+String(n.previous_term_id)+"]").remove(),l.customize.control.each(function(e){/^nav_menu_locations\[/.test(e.id)&&e.container.find("option[value="+String(n.previous_term_id)+"]").remove()}),u.each(function(e){var t=u.state("saved").get();/^nav_menu_locations\[/.test(e.id)&&e.get()===n.previous_term_id&&(e.set(n.term_id),e._dirty=!1,u.state("saved").set(t),e.preview())}),d&&a.expand()}else if("updated"===n.status){if(t="nav_menu["+String(n.term_id)+"]",!u.has(t))throw new Error("Expected setting to exist: "+t);i=u(t),_.isEqual(n.saved_value,i.get())||(o=u.state("saved").get(),i.set(n.saved_value),i._dirty=!1,u.state("saved").set(o))}}),_(e.nav_menu_item_updates).each(function(e){e.previous_post_id&&(r[e.previous_post_id]=e.post_id)}),_(e.nav_menu_item_updates).each(function(e){var t,n,i,a,o,s;if("inserted"===e.status){if(!e.previous_post_id)throw new Error("Expected previous_post_id");if(!e.post_id)throw new Error("Expected post_id");if(t="nav_menu_item["+String(e.previous_post_id)+"]",!u.has(t))throw new Error("Expected setting to exist: "+t);if(i=u(t),!u.control.has(t))throw new Error("Expected control to exist: "+t);if(o=u.control(t),!(s=i.get()))throw new Error("Did not expect setting to be empty (deleted).");if((s=_.clone(s)).menu_item_parent<0){if(!r[s.menu_item_parent])throw new Error("inserted ID for menu_item_parent not available");s.menu_item_parent=r[s.menu_item_parent]}c[s.nav_menu_term_id]&&(s.nav_menu_term_id=c[s.nav_menu_term_id]),n="nav_menu_item["+String(e.post_id)+"]",a=u.create(n,n,s,{type:"nav_menu_item",transport:u.Menus.data.settingTransport,previewer:u.previewer}),s=new u.controlConstructor.nav_menu_item(n,{type:"nav_menu_item",menu_id:e.post_id,section:"nav_menu["+String(s.nav_menu_term_id)+"]",priority:o.priority.get(),settings:{default:n},menu_item_id:e.post_id}),o.container.remove(),u.control.remove(t),u.control.add(s),i.callbacks.disable(),i.set(!1),i.preview(),a.preview(),i._dirty=!1,s.container.toggleClass("menu-item-edit-inactive",o.container.hasClass("menu-item-edit-inactive"))}}),_.each(e.widget_nav_menu_updates,function(e,t){t=u(t);t&&(t._value=e,t.preview())})},u.Menus.focusMenuItemControl=function(e){e=u.Menus.getMenuItemControl(e);e&&e.focus()},u.Menus.getMenuControl=function(e){return u.control("nav_menu["+e+"]")},u.Menus.getMenuItemControl=function(e){return u.control("nav_menu_item["+e+"]")}}(wp.customize,wp,jQuery);