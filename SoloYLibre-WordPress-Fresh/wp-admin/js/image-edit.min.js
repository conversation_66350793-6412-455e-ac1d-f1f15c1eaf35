/*! This file is auto-generated */
!function(c){var s=wp.i18n.__,d=window.imageEdit={iasapi:{},hold:{},postid:"",_view:!1,toggleCropTool:function(t,i,e){var a,o,r,n=c("#image-preview-"+t),s=this.iasapi.getSelection();d.toggleControls(e),"false"==("true"===c(e).attr("aria-expanded")?"true":"false")?(this.iasapi.cancelSelection(),d.setDisabled(c(".imgedit-crop-clear"),0)):(d.setDisabled(c(".imgedit-crop-clear"),1),e=c("#imgedit-start-x-"+t).val()?c("#imgedit-start-x-"+t).val():0,a=c("#imgedit-start-y-"+t).val()?c("#imgedit-start-y-"+t).val():0,o=c("#imgedit-sel-width-"+t).val()?c("#imgedit-sel-width-"+t).val():n.innerWidth(),r=c("#imgedit-sel-height-"+t).val()?c("#imgedit-sel-height-"+t).val():n.innerHeight(),isNaN(s.x1)&&(this.setCropSelection(t,{x1:e,y1:a,x2:o,y2:r,width:o,height:r}),s=this.iasapi.getSelection()),0===s.x1&&0===s.y1&&0===s.x2&&0===s.y2?this.iasapi.setSelection(0,0,n.innerWidth(),n.innerHeight(),!0):this.iasapi.setSelection(e,a,o,r,!0),this.iasapi.setOptions({show:!0}),this.iasapi.update())},handleCropToolClick:function(t,i,e){e.classList.contains("imgedit-crop-clear")?(this.iasapi.cancelSelection(),d.setDisabled(c(".imgedit-crop-apply"),0),c("#imgedit-sel-width-"+t).val(""),c("#imgedit-sel-height-"+t).val(""),c("#imgedit-start-x-"+t).val("0"),c("#imgedit-start-y-"+t).val("0"),c("#imgedit-selection-"+t).val("")):d.crop(t,i,e)},intval:function(t){return 0|t},setDisabled:function(t,i){i?t.removeClass("disabled").prop("disabled",!1):t.addClass("disabled").prop("disabled",!0)},init:function(e){var t=this,i=c("#image-editor-"+t.postid);t.postid!==e&&i.length&&t.close(t.postid),t.hold.sizer=parseFloat(c("#imgedit-sizer-"+e).val()),t.postid=e,c("#imgedit-response-"+e).empty(),c("#imgedit-panel-"+e).on("keypress",function(t){var i=c("#imgedit-nonce-"+e).val();26===t.which&&t.ctrlKey&&d.undo(e,i),25===t.which&&t.ctrlKey&&d.redo(e,i)}),c("#imgedit-panel-"+e).on("keypress",'input[type="text"]',function(t){var i=t.keyCode;if(36<i&&i<41&&c(this).trigger("blur"),13===i)return t.preventDefault(),t.stopPropagation(),!1}),c(document).on("image-editor-ui-ready",this.focusManager)},calculateImgSize:function(t){var i=this,e=i.intval(c("#imgedit-x-"+t).val()),a=i.intval(c("#imgedit-y-"+t).val());i.hold.w=i.hold.ow=e,i.hold.h=i.hold.oh=a,i.hold.xy_ratio=e/a,i.hold.sizer=parseFloat(c("#imgedit-sizer-"+t).val()),i.currentCropSelection=null},toggleEditor:function(t,i,e){t=c("#imgedit-wait-"+t);i?t.fadeIn("fast"):t.fadeOut("fast",function(){e&&c(document).trigger("image-editor-ui-ready")})},togglePopup:function(t){var i=c(t),t=c(t).attr("aria-controls"),t=c("#"+t);return i.attr("aria-expanded","false"===i.attr("aria-expanded")?"true":"false"),t.toggleClass("imgedit-popup-menu-open").slideToggle("fast").css({"z-index":2e5}),"true"===i.attr("aria-expanded")&&t.find("button").first().trigger("focus"),!1},monitorPopup:function(){var e=document.querySelector(".imgedit-rotate-menu-container"),a=document.querySelector(".imgedit-rotate-menu-container .imgedit-rotate");return setTimeout(function(){var t=document.activeElement,i=e.contains(t);t&&!i&&"true"===a.getAttribute("aria-expanded")&&d.togglePopup(a)},100),!1},browsePopup:function(t,i){var e=c(i),i=c(i).parent(".imgedit-popup-menu").find("button"),e=i.index(e),a=e-1,e=e+1,o=i.length,o=(a<0&&(a=o-1),e===o&&(e=0),!1);return 40===t.keyCode?o=i.get(e):38===t.keyCode&&(o=i.get(a)),o&&(o.focus(),t.preventDefault()),!1},closePopup:function(t){var t=c(t).parent(".imgedit-popup-menu"),i=t.attr("id");return c('button[aria-controls="'+i+'"]').attr("aria-expanded","false").trigger("focus"),t.toggleClass("imgedit-popup-menu-open").slideToggle("fast"),!1},toggleHelp:function(t){t=c(t);return t.attr("aria-expanded","false"===t.attr("aria-expanded")?"true":"false").parents(".imgedit-group-top").toggleClass("imgedit-help-toggled").find(".imgedit-help").slideToggle("fast"),!1},toggleControls:function(t){var t=c(t),i=c("#"+t.attr("aria-controls"));return t.attr("aria-expanded","false"===t.attr("aria-expanded")?"true":"false"),i.parent(".imgedit-group").toggleClass("imgedit-panel-active"),!1},getTarget:function(t){var i=c("#imgedit-save-target-"+t);return i.length?i.find('input[name="imgedit-target-'+t+'"]:checked').val()||"full":"all"},scaleChanged:function(t,i,e){var a=c("#imgedit-scale-width-"+t),o=c("#imgedit-scale-height-"+t),t=c("#imgedit-scale-warn-"+t),r="",n="",s=c("#imgedit-scale-button");!1!==this.validateNumeric(e)&&(i?(n=""!==a.val()?Math.round(a.val()/this.hold.xy_ratio):"",o.val(n)):(r=""!==o.val()?Math.round(o.val()*this.hold.xy_ratio):"",a.val(r)),n&&n>this.hold.oh||r&&r>this.hold.ow?(t.css("visibility","visible"),s.prop("disabled",!0)):(t.css("visibility","hidden"),s.prop("disabled",!1)))},getSelRatio:function(t){var i=this.hold.w,e=this.hold.h,a=this.intval(c("#imgedit-crop-width-"+t).val()),t=this.intval(c("#imgedit-crop-height-"+t).val());return a&&t?a+":"+t:i&&e?i+":"+e:"1:1"},filterHistory:function(t,i){var e,a,o,r=c("#imgedit-history-"+t).val(),n=[];if(""===r)return"";if(r=JSON.parse(r),0<(e=this.intval(c("#imgedit-undone-"+t).val())))for(;0<e;)r.pop(),e--;if(i){if(!r.length)return this.hold.w=this.hold.ow,this.hold.h=this.hold.oh,"";(t=(t=r[r.length-1]).c||t.r||t.f||!1)&&(this.hold.w=t.fw,this.hold.h=t.fh)}for(a in r)(o=r[a]).hasOwnProperty("c")?n[a]={c:{x:o.c.x,y:o.c.y,w:o.c.w,h:o.c.h,r:o.c.r}}:o.hasOwnProperty("r")?n[a]={r:o.r.r}:o.hasOwnProperty("f")&&(n[a]={f:o.f.f});return JSON.stringify(n)},refreshEditor:function(o,t,r){var n,i=this;i.toggleEditor(o,1),t={action:"imgedit-preview",_ajax_nonce:t,postid:o,history:i.filterHistory(o,1),rand:i.intval(1e6*Math.random())},n=c('<img id="image-preview-'+o+'" alt="" />').on("load",{history:t.history},function(t){var i=c("#imgedit-crop-"+o),e=d,a=(""!==t.data.history&&(t=JSON.parse(t.data.history))[t.length-1].hasOwnProperty("c")&&(e.setDisabled(c("#image-undo-"+o),!0),c("#image-undo-"+o).trigger("focus")),i.empty().append(n),t=Math.max(e.hold.w,e.hold.h),a=Math.max(c(n).width(),c(n).height()),e.hold.sizer=a<t?a/t:1,e.initCrop(o,n,i),null!=r&&r(),c("#imgedit-history-"+o).val()&&"0"===c("#imgedit-undone-"+o).val()?c("button.imgedit-submit-btn","#imgedit-panel-"+o).prop("disabled",!1):c("button.imgedit-submit-btn","#imgedit-panel-"+o).prop("disabled",!0),s("Image updated."));e.toggleEditor(o,0),wp.a11y.speak(a,"assertive")}).on("error",function(){var t=s("Could not load the preview image. Please reload the page and try again.");c("#imgedit-crop-"+o).empty().append('<div class="notice notice-error" tabindex="-1" role="alert"><p>'+t+"</p></div>"),i.toggleEditor(o,0,!0),wp.a11y.speak(t,"assertive")}).attr("src",ajaxurl+"?"+c.param(t))},action:function(i,t,e){var a,o,r,n,s=this;if(s.notsaved(i))return!1;if(t={action:"image-editor",_ajax_nonce:t,postid:i},"scale"===e){if(a=c("#imgedit-scale-width-"+i),o=c("#imgedit-scale-height-"+i),r=s.intval(a.val()),n=s.intval(o.val()),r<1)return a.trigger("focus"),!1;if(n<1)return o.trigger("focus"),!1;if(r===s.hold.ow||n===s.hold.oh)return!1;t.do="scale",t.fwidth=r,t.fheight=n}else{if("restore"!==e)return!1;t.do="restore"}s.toggleEditor(i,1),c.post(ajaxurl,t,function(t){c("#image-editor-"+i).empty().append(t.data.html),s.toggleEditor(i,0,!0),s._view&&s._view.refresh()}).done(function(t){t&&t.data.message.msg?wp.a11y.speak(t.data.message.msg):t&&t.data.message.error&&wp.a11y.speak(t.data.message.error)})},save:function(i,t){var e=this.getTarget(i),a=this.filterHistory(i,0),o=this;if(""===a)return!1;this.toggleEditor(i,1),t={action:"image-editor",_ajax_nonce:t,postid:i,history:a,target:e,context:c("#image-edit-context").length?c("#image-edit-context").val():null,do:"save"},c.post(ajaxurl,t,function(t){t.data.error?(c("#imgedit-response-"+i).html('<div class="notice notice-error" tabindex="-1" role="alert"><p>'+t.data.error+"</p></div>"),d.close(i),wp.a11y.speak(t.data.error)):(t.data.fw&&t.data.fh&&c("#media-dims-"+i).html(t.data.fw+" &times; "+t.data.fh),t.data.thumbnail&&c(".thumbnail","#thumbnail-head-"+i).attr("src",""+t.data.thumbnail),t.data.msg&&(c("#imgedit-response-"+i).html('<div class="notice notice-success" tabindex="-1" role="alert"><p>'+t.data.msg+"</p></div>"),wp.a11y.speak(t.data.msg)),o._view?o._view.save():d.close(i))})},open:function(e,t,i){this._view=i;var a=c("#image-editor-"+e),o=c("#media-head-"+e),r=c("#imgedit-open-btn-"+e),n=r.siblings(".spinner");if(!r.hasClass("button-activated"))return n.addClass("is-active"),c.ajax({url:ajaxurl,type:"post",data:{action:"image-editor",_ajax_nonce:t,postid:e,do:"open"},beforeSend:function(){r.addClass("button-activated")}}).done(function(t){var i;"-1"===t&&(i=s("Could not load the preview image."),a.html('<div class="notice notice-error" tabindex="-1" role="alert"><p>'+i+"</p></div>")),t.data&&t.data.html&&a.html(t.data.html),o.fadeOut("fast",function(){a.fadeIn("fast",function(){i&&c(document).trigger("image-editor-ui-ready")}),r.removeClass("button-activated"),n.removeClass("is-active")}),d.init(e)})},imgLoaded:function(t){var i=c("#image-preview-"+t),e=c("#imgedit-crop-"+t);void 0===this.hold.sizer&&this.init(t),this.calculateImgSize(t),this.initCrop(t,i,e),this.setCropSelection(t,{x1:0,y1:0,x2:0,y2:0,width:i.innerWidth(),height:i.innerHeight()}),this.toggleEditor(t,0,!0)},focusManager:function(){setTimeout(function(){var t=c('.notice[role="alert"]');(t=t.length?t:c(".imgedit-wrap").find(":tabbable:first")).attr("tabindex","-1").trigger("focus")},100)},initCrop:function(r,t,i){var n=this,o=c("#imgedit-sel-width-"+r),s=c("#imgedit-sel-height-"+r),t=c(t);t.data("imgAreaSelect")||(n.iasapi=t.imgAreaSelect({parent:i,instance:!0,handles:!0,keys:!0,minWidth:3,minHeight:3,onInit:function(t){c(t).next().css("position","absolute").nextAll(".imgareaselect-outer").css("position","absolute"),i.children().on("mousedown touchstart",function(t){var i=!1,e=n.iasapi.getSelection(),a=n.intval(c("#imgedit-crop-width-"+r).val()),o=n.intval(c("#imgedit-crop-height-"+r).val());a&&o?i=n.getSelRatio(r):t.shiftKey&&e&&e.width&&e.height&&(i=e.width+":"+e.height),n.iasapi.setOptions({aspectRatio:i})})},onSelectStart:function(){d.setDisabled(c("#imgedit-crop-sel-"+r),1),d.setDisabled(c(".imgedit-crop-clear"),1),d.setDisabled(c(".imgedit-crop-apply"),1)},onSelectEnd:function(t,i){d.setCropSelection(r,i),c("#imgedit-crop > *").is(":visible")||d.toggleControls(c(".imgedit-crop.button"))},onSelectChange:function(t,i){var e=d.hold.sizer,a=d.currentCropSelection;null!=a&&a.width==i.width&&a.height==i.height||(o.val(Math.min(d.hold.w,d.round(i.width/e))),s.val(Math.min(d.hold.h,d.round(i.height/e))),n.currentCropSelection=i)}}))},setCropSelection:function(t,i){var e=c("#imgedit-sel-width-"+t),a=c("#imgedit-sel-height-"+t),o=this.hold.sizer,r=this.hold;if(!(i=i||0)||i.width<3&&i.height<3)return this.setDisabled(c(".imgedit-crop","#imgedit-panel-"+t),1),this.setDisabled(c("#imgedit-crop-sel-"+t),1),c("#imgedit-sel-width-"+t).val(""),c("#imgedit-sel-height-"+t).val(""),c("#imgedit-start-x-"+t).val("0"),c("#imgedit-start-y-"+t).val("0"),c("#imgedit-selection-"+t).val(""),!1;var n=r.w-(Math.round(i.x1/o)+parseInt(e.val())),r=r.h-(Math.round(i.y1/o)+parseInt(a.val())),n={r:1,x:Math.round(i.x1/o)+Math.min(0,n),y:Math.round(i.y1/o)+Math.min(0,r),w:e.val(),h:a.val()};this.setDisabled(c(".imgedit-crop","#imgedit-panel-"+t),1),c("#imgedit-selection-"+t).val(JSON.stringify(n))},close:function(t,i){if((i=i||!1)&&this.notsaved(t))return!1;this.iasapi={},this.hold={},this._view?this._view.back():c("#image-editor-"+t).fadeOut("fast",function(){c("#media-head-"+t).fadeIn("fast",function(){c("#imgedit-open-btn-"+t).trigger("focus")}),c(this).empty()})},notsaved:function(t){var i=c("#imgedit-history-"+t).val(),i=""!==i?JSON.parse(i):[];return this.intval(c("#imgedit-undone-"+t).val())<i.length&&!confirm(c("#imgedit-leaving-"+t).text())},addStep:function(t,i,e){for(var a=this,o=c("#imgedit-history-"+i),r=""!==o.val()?JSON.parse(o.val()):[],n=c("#imgedit-undone-"+i),s=a.intval(n.val());0<s;)r.pop(),s--;n.val(0),r.push(t),o.val(JSON.stringify(r)),a.refreshEditor(i,e,function(){a.setDisabled(c("#image-undo-"+i),!0),a.setDisabled(c("#image-redo-"+i),!1)})},rotate:function(t,i,e,a){if(c(a).hasClass("disabled"))return!1;this.closePopup(a),this.addStep({r:{r:t,fw:this.hold.h,fh:this.hold.w}},i,e),c("#imgedit-sel-width-"+i).val(""),c("#imgedit-sel-height-"+i).val(""),this.currentCropSelection=null},flip:function(t,i,e,a){if(c(a).hasClass("disabled"))return!1;this.closePopup(a),this.addStep({f:{f:t,fw:this.hold.w,fh:this.hold.h}},i,e),c("#imgedit-sel-width-"+i).val(""),c("#imgedit-sel-height-"+i).val(""),this.currentCropSelection=null},crop:function(t,i,e){var a=c("#imgedit-selection-"+t).val(),o=this.intval(c("#imgedit-sel-width-"+t).val()),r=this.intval(c("#imgedit-sel-height-"+t).val());if(c(e).hasClass("disabled")||""===a)return!1;0<(a=JSON.parse(a)).w&&0<a.h&&0<o&&0<r&&(a.fw=o,a.fh=r,this.addStep({c:a},t,i)),c("#imgedit-sel-width-"+t).val(""),c("#imgedit-sel-height-"+t).val(""),c("#imgedit-start-x-"+t).val("0"),c("#imgedit-start-y-"+t).val("0"),this.currentCropSelection=null},undo:function(i,t){var e=this,a=c("#image-undo-"+i),o=c("#imgedit-undone-"+i),r=e.intval(o.val())+1;a.hasClass("disabled")||(o.val(r),e.refreshEditor(i,t,function(){var t=c("#imgedit-history-"+i),t=""!==t.val()?JSON.parse(t.val()):[];e.setDisabled(c("#image-redo-"+i),!0),e.setDisabled(a,r<t.length),t.length===r&&c("#image-redo-"+i).trigger("focus")}))},redo:function(t,i){var e=this,a=c("#image-redo-"+t),o=c("#imgedit-undone-"+t),r=e.intval(o.val())-1;a.hasClass("disabled")||(o.val(r),e.refreshEditor(t,i,function(){e.setDisabled(c("#image-undo-"+t),!0),e.setDisabled(a,0<r),0==r&&c("#image-undo-"+t).trigger("focus")}))},setNumSelection:function(t,i){var e=c("#imgedit-sel-width-"+t),a=c("#imgedit-sel-height-"+t),o=c("#imgedit-start-x-"+t),r=c("#imgedit-start-y-"+t),o=this.intval(o.val()),r=this.intval(r.val()),n=this.intval(e.val()),s=this.intval(a.val()),d=c("#image-preview-"+t),l=d.height(),d=d.width(),h=this.hold.sizer,g=this.iasapi;if(this.currentCropSelection=null,!1!==this.validateNumeric(i))return n<1?(e.val(""),!1):s<1?(a.val(""),!1):void((n&&s||o&&r)&&(i=g.getSelection())&&(n=i.x1+Math.round(n*h),s=i.y1+Math.round(s*h),o=o===i.x1?i.x1:Math.round(o*h),i=r===i.y1?i.y1:Math.round(r*h),d<n&&(o=0,n=d,e.val(Math.min(this.hold.w,Math.round(n/h)))),l<s&&(i=0,s=l,a.val(Math.min(this.hold.h,Math.round(s/h)))),g.setSelection(o,i,n,s),g.update(),this.setCropSelection(t,g.getSelection()),this.currentCropSelection=g.getSelection()))},round:function(t){var i;return t=Math.round(t),.6<this.hold.sizer?t:"1"===(i=t.toString().slice(-1))?t-1:"9"===i?t+1:t},setRatioSelection:function(t,i,e){var a=this.intval(c("#imgedit-crop-width-"+t).val()),o=this.intval(c("#imgedit-crop-height-"+t).val()),r=c("#image-preview-"+t).height();!1===this.validateNumeric(e)?this.iasapi.setOptions({aspectRatio:null}):a&&o&&(this.iasapi.setOptions({aspectRatio:a+":"+o}),e=this.iasapi.getSelection(!0))&&(r<(a=Math.ceil(e.y1+(e.x2-e.x1)/(a/o)))?(a=r,o=s("Selected crop ratio exceeds the boundaries of the image. Try a different ratio."),c("#imgedit-crop-"+t).prepend('<div class="notice notice-error" tabindex="-1" role="alert"><p>'+o+"</p></div>"),wp.a11y.speak(o,"assertive"),c(i?"#imgedit-crop-height-"+t:"#imgedit-crop-width-"+t).val("")):void 0!==(r=c("#imgedit-crop-"+t).find(".notice-error"))&&r.remove(),this.iasapi.setSelection(e.x1,e.y1,e.x2,a),this.iasapi.update())},validateNumeric:function(t){if(!1===this.intval(c(t).val()))return c(t).val(""),!1}}}(jQuery);