/*! This file is auto-generated */
jQuery(function(a){var t,c,e,i=!1;a("#link_name").trigger("focus"),postboxes.add_postbox_toggles("link"),a("#category-tabs a").on("click",function(){var t=a(this).attr("href");return a(this).parent().addClass("tabs").siblings("li").removeClass("tabs"),a(".tabs-panel").hide(),a(t).show(),"#categories-all"==t?deleteUserSetting("cats"):setUserSetting("cats","pop"),!1}),getUserSetting("cats")&&a('#category-tabs a[href="#categories-pop"]').trigger("click"),t=a("#newcat").one("focus",function(){a(this).val("").removeClass("form-input-tip")}),a("#link-category-add-submit").on("click",function(){t.focus()}),c=function(){var t,e;i||(i=!0,t=(e=a(this)).is(":checked"),e=e.val().toString(),a("#in-link-category-"+e+", #in-popular-link_category-"+e).prop("checked",t),i=!1)},e=function(t,e){a(e.what+" response_data",t).each(function(){a(a(this).text()).find("label").each(function(){var t=a(this),e=t.find("input").val(),i=t.find("input")[0].id,t=t.text().trim();a("#"+i).on("change",c),a('<option value="'+parseInt(e,10)+'"></option>').text(t)})})},a("#categorychecklist").wpList({alt:"",what:"link-category",response:"category-ajax-response",addAfter:e}),a('a[href="#categories-all"]').on("click",function(){deleteUserSetting("cats")}),a('a[href="#categories-pop"]').on("click",function(){setUserSetting("cats","pop")}),"pop"==getUserSetting("cats")&&a('a[href="#categories-pop"]').trigger("click"),a("#category-add-toggle").on("click",function(){return a(this).parents("div:first").toggleClass("wp-hidden-children"),a('#category-tabs a[href="#categories-all"]').trigger("click"),a("#newcategory").trigger("focus"),!1}),a(".categorychecklist :checkbox").on("change",c).filter(":checked").trigger("change")});