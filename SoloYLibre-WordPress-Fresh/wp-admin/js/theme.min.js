/*! This file is auto-generated */
window.wp=window.wp||{},function(n){var o,a;function e(e,t){Backbone.history._hasPushState&&Backbone.Router.prototype.navigate.call(this,e,t)}(o=wp.themes=wp.themes||{}).data=_wpThemeSettings,a=o.data.l10n,o.isInstall=!!o.data.settings.isInstall,_.extend(o,{model:{},view:{},routes:{},router:{},template:wp.template}),o.Model=Backbone.Model.extend({initialize:function(){var e;this.get("slug")&&(-1!==_.indexOf(o.data.installedThemes,this.get("slug"))&&this.set({installed:!0}),o.data.activeTheme===this.get("slug"))&&this.set({active:!0}),this.set({id:this.get("slug")||this.get("id")}),this.has("sections")&&(e=this.get("sections").description,this.set({description:e}))}}),o.view.Appearance=wp.Backbone.View.extend({el:"#wpbody-content .wrap .theme-browser",window:n(window),page:0,initialize:function(e){_.bindAll(this,"scroller"),this.SearchView=e.SearchView||o.view.Search,this.window.on("scroll",_.throttle(this.scroller,300))},render:function(){this.view=new o.view.Themes({collection:this.collection,parent:this}),this.search(),this.$el.removeClass("search-loading"),this.view.render(),this.$el.empty().append(this.view.el).addClass("rendered")},searchContainer:n(".search-form"),search:function(){var e;1!==o.data.themes.length&&(e=new this.SearchView({collection:this.collection,parent:this}),(this.SearchView=e).render(),this.searchContainer.find(".search-box").append(n.parseHTML('<label for="wp-filter-search-input">'+a.search+"</label>")).append(e.el),this.searchContainer.on("submit",function(e){e.preventDefault()}))},scroller:function(){var e=this,t=this.window.scrollTop()+e.window.height(),e=e.$el.offset().top+e.$el.outerHeight(!1)-e.window.height();Math.round(.9*e)<t&&this.trigger("theme:scroll")}}),o.Collection=Backbone.Collection.extend({model:o.Model,terms:"",doSearch:function(e){this.terms!==e&&(this.terms=e,0<this.terms.length&&this.search(this.terms),""===this.terms&&(this.reset(o.data.themes),n("body").removeClass("no-results")),this.trigger("themes:update"))},search:function(t){var i,e,s,r,a;this.reset(o.data.themes,{silent:!0}),t=(t=(t=t.trim()).replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")).replace(/ /g,")(?=.*"),i=new RegExp("^(?=.*"+t+").+","i"),0===(e=this.filter(function(e){return s=e.get("name").replace(/(<([^>]+)>)/gi,""),r=e.get("description").replace(/(<([^>]+)>)/gi,""),a=e.get("author").replace(/(<([^>]+)>)/gi,""),s=_.union([s,e.get("id"),r,a,e.get("tags")]),i.test(e.get("author"))&&2<t.length&&e.set("displayAuthor",!0),i.test(s)})).length?this.trigger("query:empty"):n("body").removeClass("no-results"),this.reset(e)},paginate:function(e){var t=this;return e=e||0,t=_(t.rest(20*e)),t=_(t.first(20))},count:!1,query:function(t){var e,i,s,r=this.queries,a=this;if(this.currentQuery.request=t,e=_.find(r,function(e){return _.isEqual(e.request,t)}),(i=_.has(t,"page"))||(this.currentQuery.page=1),e||i){if(i)return this.apiCall(t,i).done(function(e){a.add(e.themes),a.trigger("query:success"),a.loadingThemes=!1}).fail(function(){a.trigger("query:fail")});0===e.themes.length?a.trigger("query:empty"):n("body").removeClass("no-results"),_.isNumber(e.total)&&(this.count=e.total),this.reset(e.themes),e.total||(this.count=this.length),this.trigger("themes:update"),this.trigger("query:success",this.count)}else this.apiCall(t).done(function(e){e.themes&&(a.reset(e.themes),s=e.info.results,r.push({themes:e.themes,request:t,total:s})),a.trigger("themes:update"),a.trigger("query:success",s),e.themes&&0===e.themes.length&&a.trigger("query:empty")}).fail(function(){a.trigger("query:fail")})},queries:[],currentQuery:{page:1,request:{}},apiCall:function(e,t){return wp.ajax.send("query-themes",{data:{request:_.extend({per_page:100},e)},beforeSend:function(){t||n("body").addClass("loading-content").removeClass("no-results")}})},loadingThemes:!1}),o.view.Theme=wp.Backbone.View.extend({className:"theme",state:"grid",html:o.template("theme"),events:{click:o.isInstall?"preview":"expand",keydown:o.isInstall?"preview":"expand",touchend:o.isInstall?"preview":"expand",keyup:"addFocus",touchmove:"preventExpand","click .theme-install":"installTheme","click .update-message":"updateTheme"},touchDrag:!1,initialize:function(){this.model.on("change",this.render,this)},render:function(){var e=this.model.toJSON();this.$el.html(this.html(e)).attr("data-slug",e.id),this.activeTheme(),this.model.get("displayAuthor")&&this.$el.addClass("display-author")},activeTheme:function(){this.model.get("active")&&this.$el.addClass("active")},addFocus:function(){var e=n(":focus").hasClass("theme")?n(":focus"):n(":focus").parents(".theme");n(".theme.focus").removeClass("focus"),e.addClass("focus")},expand:function(e){if("keydown"!==(e=e||window.event).type||13===e.which||32===e.which)return!0===this.touchDrag?this.touchDrag=!1:void(n(e.target).is(".theme-actions a")||n(e.target).is(".theme-actions a, .update-message, .button-link, .notice-dismiss")||(o.focusedTheme=this.$el,this.trigger("theme:expand",this.model.cid)))},preventExpand:function(){this.touchDrag=!0},preview:function(e){var t,i,s=this;if(e=e||window.event,!0===this.touchDrag)return this.touchDrag=!1;n(e.target).not(".install-theme-preview").parents(".theme-actions").length||"keydown"===e.type&&13!==e.which&&32!==e.which||"keydown"===e.type&&13!==e.which&&n(":focus").hasClass("button")||(e.preventDefault(),e=e||window.event,o.focusedTheme=this.$el,o.preview=i=new o.view.Preview({model:this.model}),i.render(),this.setNavButtonsState(),1===this.model.collection.length?i.$el.addClass("no-navigation"):i.$el.removeClass("no-navigation"),n("div.wrap").append(i.el),this.listenTo(i,"theme:next",function(){if(t=s.model,_.isUndefined(s.current)||(t=s.current),s.current=s.model.collection.at(s.model.collection.indexOf(t)+1),_.isUndefined(s.current))return s.options.parent.parent.trigger("theme:end"),s.current=t;i.model=s.current,i.render(),this.setNavButtonsState(),n(".next-theme").trigger("focus")}).listenTo(i,"theme:previous",function(){t=s.model,0===s.model.collection.indexOf(s.current)||(_.isUndefined(s.current)||(t=s.current),s.current=s.model.collection.at(s.model.collection.indexOf(t)-1),_.isUndefined(s.current))||(i.model=s.current,i.render(),this.setNavButtonsState(),n(".previous-theme").trigger("focus"))}),this.listenTo(i,"preview:close",function(){s.current=s.model}))},setNavButtonsState:function(){var e=n(".theme-install-overlay"),t=_.isUndefined(this.current)?this.model:this.current,i=e.find(".previous-theme"),e=e.find(".next-theme");0===this.model.collection.indexOf(t)&&(i.addClass("disabled").prop("disabled",!0),e.trigger("focus")),_.isUndefined(this.model.collection.at(this.model.collection.indexOf(t)+1))&&(e.addClass("disabled").prop("disabled",!0),i.trigger("focus"))},installTheme:function(e){var i=this;e.preventDefault(),wp.updates.maybeRequestFilesystemCredentials(e),n(document).on("wp-theme-install-success",function(e,t){i.model.get("id")===t.slug&&i.model.set({installed:!0}),t.blockTheme&&i.model.set({block_theme:!0})}),wp.updates.installTheme({slug:n(e.target).data("slug")})},updateTheme:function(e){var i=this;this.model.get("hasPackage")&&(e.preventDefault(),wp.updates.maybeRequestFilesystemCredentials(e),n(document).on("wp-theme-update-success",function(e,t){i.model.off("change",i.render,i),i.model.get("id")===t.slug&&i.model.set({hasUpdate:!1,version:t.newVersion}),i.model.on("change",i.render,i)}),wp.updates.updateTheme({slug:n(e.target).parents("div.theme").first().data("slug")}))}}),o.view.Details=wp.Backbone.View.extend({className:"theme-overlay",events:{click:"collapse","click .delete-theme":"deleteTheme","click .left":"previousTheme","click .right":"nextTheme","click #update-theme":"updateTheme","click .toggle-auto-update":"autoupdateState"},html:o.template("theme-single"),render:function(){var e=this.model.toJSON();this.$el.html(this.html(e)),this.activeTheme(),this.navigation(),this.screenshotCheck(this.$el),this.containFocus(this.$el)},activeTheme:function(){this.$el.toggleClass("active",this.model.get("active"))},containFocus:function(s){_.delay(function(){n(".theme-overlay").trigger("focus")},100),s.on("keydown.wp-themes",function(e){var t=s.find(".theme-header button:not(.disabled)").first(),i=s.find(".theme-actions a:visible").last();9===e.which&&(t[0]===e.target&&e.shiftKey?(i.trigger("focus"),e.preventDefault()):i[0]!==e.target||e.shiftKey||(t.trigger("focus"),e.preventDefault()))})},collapse:function(e){var t,i=this;e=e||window.event,1!==o.data.themes.length&&(n(e.target).is(".theme-backdrop")||n(e.target).is(".close")||27===e.keyCode)&&(n("body").addClass("closing-overlay"),this.$el.fadeOut(130,function(){n("body").removeClass("closing-overlay"),i.closeOverlay(),t=document.body.scrollTop,o.router.navigate(o.router.baseUrl("")),document.body.scrollTop=t,o.focusedTheme&&o.focusedTheme.find(".more-details").trigger("focus")}))},navigation:function(){this.model.cid===this.model.collection.at(0).cid&&this.$el.find(".left").addClass("disabled").prop("disabled",!0),this.model.cid===this.model.collection.at(this.model.collection.length-1).cid&&this.$el.find(".right").addClass("disabled").prop("disabled",!0)},closeOverlay:function(){n("body").removeClass("modal-open"),this.remove(),this.unbind(),this.trigger("theme:collapse")},autoupdateState:function(){var s=this,r=function(e,t){var i;s.model.get("id")===t.asset&&((i=s.model.get("autoupdate")).enabled="enable"===t.state,s.model.set({autoupdate:i}),n(document).off("wp-auto-update-setting-changed",r))};n(document).on("wp-auto-update-setting-changed",r)},updateTheme:function(e){var i=this;e.preventDefault(),wp.updates.maybeRequestFilesystemCredentials(e),n(document).on("wp-theme-update-success",function(e,t){i.model.get("id")===t.slug&&i.model.set({hasUpdate:!1,version:t.newVersion}),i.render()}),wp.updates.updateTheme({slug:n(e.target).data("slug")})},deleteTheme:function(e){var i=this,s=i.model.collection,r=o;e.preventDefault(),window.confirm(wp.themes.data.settings.confirmDelete)&&(wp.updates.maybeRequestFilesystemCredentials(e),n(document).one("wp-theme-delete-success",function(e,t){i.$el.find(".close").trigger("click"),n('[data-slug="'+t.slug+'"]').css({backgroundColor:"#faafaa"}).fadeOut(350,function(){n(this).remove(),r.data.themes=_.without(r.data.themes,_.findWhere(r.data.themes,{id:t.slug})),n(".wp-filter-search").val(""),s.doSearch(""),s.remove(i.model),s.trigger("themes:update")})}),wp.updates.deleteTheme({slug:this.model.get("id")}))},nextTheme:function(){return this.trigger("theme:next",this.model.cid),!1},previousTheme:function(){return this.trigger("theme:previous",this.model.cid),!1},screenshotCheck:function(e){var t=e.find(".screenshot img"),i=new Image;i.src=t.attr("src"),i.width&&i.width<=300&&e.addClass("small-screenshot")}}),o.view.Preview=o.view.Details.extend({className:"wp-full-overlay expanded",el:".theme-install-overlay",events:{"click .close-full-overlay":"close","click .collapse-sidebar":"collapse","click .devices button":"previewDevice","click .previous-theme":"previousTheme","click .next-theme":"nextTheme",keyup:"keyEvent","click .theme-install":"installTheme"},html:o.template("theme-preview"),render:function(){var e=this,t=this.model.toJSON(),i=n(document.body);i.attr("aria-busy","true"),this.$el.removeClass("iframe-ready").html(this.html(t)),(t=this.$el.data("current-preview-device"))&&e.togglePreviewDeviceButtons(t),o.router.navigate(o.router.baseUrl(o.router.themePath+this.model.get("id")),{replace:!1}),this.$el.fadeIn(200,function(){i.addClass("theme-installer-active full-overlay-active")}),this.$el.find("iframe").one("load",function(){e.iframeLoaded()})},iframeLoaded:function(){this.$el.addClass("iframe-ready"),n(document.body).attr("aria-busy","false")},close:function(){return this.$el.fadeOut(200,function(){n("body").removeClass("theme-installer-active full-overlay-active"),o.focusedTheme&&o.focusedTheme.find(".more-details").trigger("focus")}).removeClass("iframe-ready"),o.router.selectedTab?(o.router.navigate(o.router.baseUrl("?browse="+o.router.selectedTab)),o.router.selectedTab=!1):o.router.navigate(o.router.baseUrl("")),this.trigger("preview:close"),this.undelegateEvents(),this.unbind(),!1},collapse:function(e){e=n(e.currentTarget);return"true"===e.attr("aria-expanded")?e.attr({"aria-expanded":"false","aria-label":a.expandSidebar}):e.attr({"aria-expanded":"true","aria-label":a.collapseSidebar}),this.$el.toggleClass("collapsed").toggleClass("expanded"),!1},previewDevice:function(e){e=n(e.currentTarget).data("device");this.$el.removeClass("preview-desktop preview-tablet preview-mobile").addClass("preview-"+e).data("current-preview-device",e),this.togglePreviewDeviceButtons(e)},togglePreviewDeviceButtons:function(e){var t=n(".wp-full-overlay-footer .devices");t.find("button").removeClass("active").attr("aria-pressed",!1),t.find("button.preview-"+e).addClass("active").attr("aria-pressed",!0)},keyEvent:function(e){27===e.keyCode&&(this.undelegateEvents(),this.close()),39===e.keyCode&&_.once(this.nextTheme()),37===e.keyCode&&this.previousTheme()},installTheme:function(e){var t=this,i=n(e.target);e.preventDefault(),i.hasClass("disabled")||(wp.updates.maybeRequestFilesystemCredentials(e),n(document).on("wp-theme-install-success",function(){t.model.set({installed:!0})}),wp.updates.installTheme({slug:i.data("slug")}))}}),o.view.Themes=wp.Backbone.View.extend({className:"themes wp-clearfix",$overlay:n("div.theme-overlay"),index:0,count:n(".wrap .theme-count"),liveThemeCount:0,initialize:function(e){var t=this;this.parent=e.parent,this.setView("grid"),t.currentTheme(),this.listenTo(t.collection,"themes:update",function(){t.parent.page=0,t.currentTheme(),t.render(this)}),this.listenTo(t.collection,"query:success",function(e){_.isNumber(e)?(t.count.text(e),t.announceSearchResults(e)):(t.count.text(t.collection.length),t.announceSearchResults(t.collection.length))}),this.listenTo(t.collection,"query:empty",function(){n("body").addClass("no-results")}),this.listenTo(this.parent,"theme:scroll",function(){t.renderThemes(t.parent.page)}),this.listenTo(this.parent,"theme:close",function(){t.overlay&&t.overlay.closeOverlay()}),n("body").on("keyup",function(e){!t.overlay||n("#request-filesystem-credentials-dialog").is(":visible")||(39===e.keyCode&&t.overlay.nextTheme(),37===e.keyCode&&t.overlay.previousTheme(),27===e.keyCode&&t.overlay.collapse(e))})},render:function(){this.$el.empty(),1===o.data.themes.length&&(this.singleTheme=new o.view.Details({model:this.collection.models[0]}),this.singleTheme.render(),this.$el.addClass("single-theme"),this.$el.append(this.singleTheme.el)),0<this.options.collection.size()&&this.renderThemes(this.parent.page),this.liveThemeCount=this.collection.count||this.collection.length,this.count.text(this.liveThemeCount),o.isInstall||this.announceSearchResults(this.liveThemeCount)},renderThemes:function(e){var t=this;t.instance=t.collection.paginate(e),0===t.instance.size()?this.parent.trigger("theme:end"):(!o.isInstall&&1<=e&&n(".add-new-theme").remove(),t.instance.each(function(e){t.theme=new o.view.Theme({model:e,parent:t}),t.theme.render(),t.$el.append(t.theme.el),t.listenTo(t.theme,"theme:expand",t.expand,t)}),!o.isInstall&&o.data.settings.canInstall&&this.$el.append('<div class="theme add-new-theme"><a href="'+o.data.settings.installURI+'"><div class="theme-screenshot"><span></span></div><h2 class="theme-name">'+a.addNew+"</h2></a></div>"),this.parent.page++)},currentTheme:function(){var e=this.collection.findWhere({active:!0});e&&(this.collection.remove(e),this.collection.add(e,{at:0}))},setView:function(e){return e},expand:function(e){var t,i=this;this.model=i.collection.get(e),o.router.navigate(o.router.baseUrl(o.router.themePath+this.model.id)),this.setView("detail"),n("body").addClass("modal-open"),this.overlay=new o.view.Details({model:i.model}),this.overlay.render(),this.model.get("hasUpdate")&&(e=n('[data-slug="'+this.model.id+'"]'),t=n(this.overlay.el),e.find(".updating-message").length?(t.find(".notice-warning h3").remove(),t.find(".notice-warning").removeClass("notice-large").addClass("updating-message").find("p").text(wp.updates.l10n.updating)):e.find(".notice-error").length&&t.find(".notice-warning").remove()),this.$overlay.html(this.overlay.el),this.listenTo(this.overlay,"theme:next",function(){i.next([i.model.cid])}).listenTo(this.overlay,"theme:previous",function(){i.previous([i.model.cid])})},next:function(e){e=this.collection.get(e[0]),e=this.collection.at(this.collection.indexOf(e)+1);void 0!==e&&(this.overlay.closeOverlay(),this.theme.trigger("theme:expand",e.cid))},previous:function(e){e=this.collection.get(e[0]),e=this.collection.at(this.collection.indexOf(e)-1);void 0!==e&&(this.overlay.closeOverlay(),this.theme.trigger("theme:expand",e.cid))},announceSearchResults:function(e){0===e?wp.a11y.speak(a.noThemesFound):wp.a11y.speak(a.themesFound.replace("%d",e))}}),o.view.Search=wp.Backbone.View.extend({tagName:"input",className:"wp-filter-search",id:"wp-filter-search-input",searching:!1,attributes:{type:"search","aria-describedby":"live-search-desc"},events:{input:"search",keyup:"search",blur:"pushState"},initialize:function(e){this.parent=e.parent,this.listenTo(this.parent,"theme:close",function(){this.searching=!1})},search:function(e){"keyup"===e.type&&27===e.which&&(e.target.value=""),this.doSearch(e)},doSearch:function(e){var t={};this.collection.doSearch(e.target.value.replace(/\+/g," ")),this.searching&&13!==e.which?t.replace=!0:this.searching=!0,e.target.value?o.router.navigate(o.router.baseUrl(o.router.searchPath+e.target.value),t):o.router.navigate(o.router.baseUrl(""))},pushState:function(e){var t=o.router.baseUrl("");e.target.value&&(t=o.router.baseUrl(o.router.searchPath+encodeURIComponent(e.target.value))),this.searching=!1,o.router.navigate(t)}}),o.Router=Backbone.Router.extend({routes:{"themes.php?theme=:slug":"theme","themes.php?search=:query":"search","themes.php?s=:query":"search","themes.php":"themes","":"themes"},baseUrl:function(e){return"themes.php"+e},themePath:"?theme=",searchPath:"?search=",search:function(e){n(".wp-filter-search").val(e.replace(/\+/g," "))},themes:function(){n(".wp-filter-search").val("")},navigate:e}),o.Run={init:function(){this.themes=new o.Collection(o.data.themes),this.view=new o.view.Appearance({collection:this.themes}),this.render(),this.view.SearchView.doSearch=_.debounce(this.view.SearchView.doSearch,500)},render:function(){this.view.render(),this.routes(),Backbone.History.started&&Backbone.history.stop(),Backbone.history.start({root:o.data.settings.adminUrl,pushState:!0,hashChange:!1})},routes:function(){var t=this;o.router=new o.Router,o.router.on("route:theme",function(e){t.view.view.expand(e)}),o.router.on("route:themes",function(){t.themes.doSearch(""),t.view.trigger("theme:close")}),o.router.on("route:search",function(){n(".wp-filter-search").trigger("keyup")}),this.extraRoutes()},extraRoutes:function(){return!1}},o.view.InstallerSearch=o.view.Search.extend({events:{input:"search",keyup:"search"},terms:"",search:function(e){("keyup"!==e.type||9!==e.which&&16!==e.which)&&(this.collection=this.options.parent.view.collection,"keyup"===e.type&&27===e.which&&(e.target.value=""),this.doSearch(e.target.value))},doSearch:function(e){var t={};this.terms!==e&&(this.terms=e,"author:"===(t.search=e).substring(0,7)&&(t.search="",t.author=e.slice(7)),"tag:"===e.substring(0,4)&&(t.search="",t.tag=[e.slice(4)]),n(".filter-links li > a.current").removeClass("current").removeAttr("aria-current"),n("body").removeClass("show-filters filters-applied show-favorites-form"),n(".drawer-toggle").attr("aria-expanded","false"),this.collection.query(t),o.router.navigate(o.router.baseUrl(o.router.searchPath+encodeURIComponent(e)),{replace:!0}))}}),o.view.Installer=o.view.Appearance.extend({el:"#wpbody-content .wrap",events:{"click .filter-links li > a":"onSort","click .theme-filter":"onFilter","click .drawer-toggle":"moreFilters","click .filter-drawer .apply-filters":"applyFilters",'click .filter-group [type="checkbox"]':"addFilter","click .filter-drawer .clear-filters":"clearFilters","click .edit-filters":"backToFilters","click .favorites-form-submit":"saveUsername","keyup #wporg-username-input":"saveUsername"},render:function(){var e=this;this.search(),this.uploader(),this.collection=new o.Collection,this.listenTo(this,"theme:end",function(){e.collection.loadingThemes||(e.collection.loadingThemes=!0,e.collection.currentQuery.page++,_.extend(e.collection.currentQuery.request,{page:e.collection.currentQuery.page}),e.collection.query(e.collection.currentQuery.request))}),this.listenTo(this.collection,"query:success",function(){n("body").removeClass("loading-content"),n(".theme-browser").find("div.error").remove()}),this.listenTo(this.collection,"query:fail",function(){n("body").removeClass("loading-content"),n(".theme-browser").find("div.error").remove(),n(".theme-browser").find("div.themes").before('<div class="notice notice-error"><p>'+a.error+'</p><p><button class="button try-again">'+a.tryAgain+"</button></p></div>"),n(".theme-browser .error .try-again").on("click",function(e){e.preventDefault(),n("input.wp-filter-search").trigger("input")})}),this.view&&this.view.remove(),this.view=new o.view.Themes({collection:this.collection,parent:this}),this.page=0,this.$el.find(".themes").remove(),this.view.render(),this.$el.find(".theme-browser").append(this.view.el).addClass("rendered")},browse:function(e){"block-themes"===e?this.collection.query({tag:"full-site-editing"}):this.collection.query({browse:e})},onSort:function(e){var t=n(e.target),i=t.data("sort");e.preventDefault(),n("body").removeClass("filters-applied show-filters"),n(".drawer-toggle").attr("aria-expanded","false"),t.hasClass(this.activeClass)||(this.sort(i),o.router.navigate(o.router.baseUrl(o.router.browsePath+i)))},sort:function(e){this.clearSearch(),o.router.selectedTab=e,n(".filter-links li > a, .theme-filter").removeClass(this.activeClass).removeAttr("aria-current"),n('[data-sort="'+e+'"]').addClass(this.activeClass).attr("aria-current","page"),"favorites"===e?n("body").addClass("show-favorites-form"):n("body").removeClass("show-favorites-form"),this.browse(e)},onFilter:function(e){var e=n(e.target),t=e.data("filter");e.hasClass(this.activeClass)||(n(".filter-links li > a, .theme-section").removeClass(this.activeClass).removeAttr("aria-current"),e.addClass(this.activeClass).attr("aria-current","page"),t&&(t=_.union([t,this.filtersChecked()]),this.collection.query({tag:[t]})))},addFilter:function(){this.filtersChecked()},applyFilters:function(e){var t,i=this.filtersChecked(),s={tag:i},r=n(".filtered-by .tags");e&&e.preventDefault(),i?(n("body").addClass("filters-applied"),n(".filter-links li > a.current").removeClass("current").removeAttr("aria-current"),r.empty(),_.each(i,function(e){t=n('label[for="filter-id-'+e+'"]').text(),r.append('<span class="tag">'+t+"</span>")}),this.collection.query(s)):wp.a11y.speak(a.selectFeatureFilter)},saveUsername:function(e){var t=n("#wporg-username-input").val(),i=n("#wporg-username-nonce").val(),s={browse:"favorites",user:t},r=this;if(e&&e.preventDefault(),"keyup"!==e.type||13===e.which)return wp.ajax.send("save-wporg-username",{data:{_wpnonce:i,username:t},success:function(){r.collection.query(s)}})},filtersChecked:function(){var e=n(".filter-group").find(":checkbox"),t=[];return _.each(e.filter(":checked"),function(e){t.push(n(e).prop("value"))}),0===t.length?(n(".filter-drawer .apply-filters").find("span").text(""),n(".filter-drawer .clear-filters").hide(),n("body").removeClass("filters-applied"),!1):(n(".filter-drawer .apply-filters").find("span").text(t.length),n(".filter-drawer .clear-filters").css("display","inline-block"),t)},activeClass:"current",uploader:function(){var e=n(".upload-view-toggle"),t=n(document.body);e.on("click",function(){t.toggleClass("show-upload-view"),e.attr("aria-expanded",t.hasClass("show-upload-view"))})},moreFilters:function(e){var t=n("body"),i=n(".drawer-toggle");if(e.preventDefault(),t.hasClass("filters-applied"))return this.backToFilters();this.clearSearch(),o.router.navigate(o.router.baseUrl("")),t.toggleClass("show-filters"),i.attr("aria-expanded",t.hasClass("show-filters"))},clearFilters:function(e){var t=n(".filter-group").find(":checkbox"),i=this;e.preventDefault(),_.each(t.filter(":checked"),function(e){return n(e).prop("checked",!1),i.filtersChecked()})},backToFilters:function(e){e&&e.preventDefault(),n("body").removeClass("filters-applied")},clearSearch:function(){n("#wp-filter-search-input").val("")}}),o.InstallerRouter=Backbone.Router.extend({routes:{"theme-install.php?theme=:slug":"preview","theme-install.php?browse=:sort":"sort","theme-install.php?search=:query":"search","theme-install.php":"sort"},baseUrl:function(e){return"theme-install.php"+e},themePath:"?theme=",browsePath:"?browse=",searchPath:"?search=",search:function(e){n(".wp-filter-search").val(e.replace(/\+/g," "))},navigate:e}),o.RunInstaller={init:function(){this.view=new o.view.Installer({section:"popular",SearchView:o.view.InstallerSearch}),this.render(),this.view.SearchView.doSearch=_.debounce(this.view.SearchView.doSearch,500)},render:function(){this.view.render(),this.routes(),Backbone.History.started&&Backbone.history.stop(),Backbone.history.start({root:o.data.settings.adminUrl,pushState:!0,hashChange:!1})},routes:function(){var t=this,i={};o.router=new o.InstallerRouter,o.router.on("route:preview",function(e){o.preview&&(o.preview.undelegateEvents(),o.preview.unbind()),t.view.view.theme&&t.view.view.theme.preview?(t.view.view.theme.model=t.view.collection.findWhere({slug:e}),t.view.view.theme.preview()):(i.theme=e,t.view.collection.query(i),t.view.collection.trigger("update"),t.view.collection.once("query:success",function(){n('div[data-slug="'+e+'"]').trigger("click")}))}),o.router.on("route:sort",function(e){e||(e="popular",o.router.navigate(o.router.baseUrl("?browse=popular"),{replace:!0})),t.view.sort(e),o.preview&&o.preview.close()}),o.router.on("route:search",function(){n(".wp-filter-search").trigger("focus").trigger("keyup")}),this.extraRoutes()},extraRoutes:function(){return!1}},n(function(){(o.isInstall?o.RunInstaller:o.Run).init(),n(document.body).on("click",".load-customize",function(){var e=n(this),t=document.createElement("a");t.href=e.prop("href"),t.search=n.param(_.extend(wp.customize.utils.parseQueryString(t.search.substr(1)),{return:window.location.href})),e.prop("href",t.href)}),n(".broken-themes .delete-theme").on("click",function(){return confirm(_wpThemeSettings.settings.confirmDelete)})})}(jQuery),jQuery(function(r){window.tb_position=function(){var e=r("#TB_window"),t=r(window).width(),i=r(window).height(),t=1040<t?1040:t,s=0;r("#wpadminbar").length&&(s=parseInt(r("#wpadminbar").css("height"),10)),1<=e.length&&(e.width(t-50).height(i-45-s),r("#TB_iframeContent").width(t-50).height(i-75-s),e.css({"margin-left":"-"+parseInt((t-50)/2,10)+"px"}),void 0!==document.body.style.maxWidth)&&e.css({top:20+s+"px","margin-top":"0"})},r(window).on("resize",function(){tb_position()})});