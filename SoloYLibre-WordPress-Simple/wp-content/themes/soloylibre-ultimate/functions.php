<?php
/**
 * SoloYLibre Ultimate Theme Functions
 * Desarrollado por Jose <PERSON> Encarnacion (JoseTusabe)
 * Desde San José de <PERSON>coa, República Dominicana 🇩🇴
 */

// Evitar acceso directo
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Configuración del tema
function soloylibre_theme_setup() {
    add_theme_support( 'title-tag' );
    add_theme_support( 'post-thumbnails' );
    add_theme_support( 'html5', array( 'search-form', 'comment-form', 'comment-list', 'gallery', 'caption' ) );
    add_theme_support( 'automatic-feed-links' );
}
add_action( 'after_setup_theme', 'soloylibre_theme_setup' );

// Encolar estilos
function soloylibre_scripts() {
    wp_enqueue_style( 'soloylibre-style', get_stylesheet_uri(), array(), '1.0.0' );
}
add_action( 'wp_enqueue_scripts', 'soloylibre_scripts' );

// Personalizar footer del admin
function soloylibre_admin_footer() {
    echo '<span id="footer-thankyou">🇩🇴 Desarrollado por <strong><PERSON> (JoseTusabe)</strong> desde San José de Ocoa, República Dominicana | 📧 <EMAIL> | 📞 718-713-5500</span>';
}
add_filter( 'admin_footer_text', 'soloylibre_admin_footer' );
?>
