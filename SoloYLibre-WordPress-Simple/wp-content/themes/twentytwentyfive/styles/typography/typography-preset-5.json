{"version": 3, "$schema": "https://schemas.wp.org/wp/6.7/theme.json", "title": "Literata & Ysabeau Office", "slug": "typography-preset-5", "settings": {"typography": {"fontFamilies": [{"name": "Literata", "slug": "literata", "fontFamily": "Literata, serif", "fontFace": [{"src": ["file:./assets/fonts/literata/Literata72pt-ExtraLight.woff2"], "fontWeight": "200", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraLightItalic.woff2"], "fontWeight": "200", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Light.woff2"], "fontWeight": "300", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-LightItalic.woff2"], "fontWeight": "300", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Regular.woff2"], "fontWeight": "400", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-RegularItalic.woff2"], "fontWeight": "400", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Medium.woff2"], "fontWeight": "500", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-MediumItalic.woff2"], "fontWeight": "500", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-SemiBold.woff2"], "fontWeight": "600", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-SemiBoldItalic.woff2"], "fontWeight": "600", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Bold.woff2"], "fontWeight": "700", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-BoldItalic.woff2"], "fontWeight": "700", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraBold.woff2"], "fontWeight": "800", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraBoldItalic.woff2"], "fontWeight": "800", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Black.woff2"], "fontWeight": "900", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-BlackItalic.woff2"], "fontWeight": "900", "fontStyle": "italic", "fontFamily": "Literata"}]}, {"name": "Ysabeau Office", "slug": "ysabeau-office", "fontFamily": "\"Ysabeau Office\", sans-serif", "fontFace": [{"src": ["file:./assets/fonts/ysabeau-office/YsabeauOffice-VariableFont_wght.woff2"], "fontWeight": "100 900", "fontStyle": "normal", "fontFamily": "\"Ysabeau Office\""}, {"src": ["file:./assets/fonts/ysabeau-office/YsabeauOffice-Italic-VariableFont_wght.woff2"], "fontWeight": "100 900", "fontStyle": "italic", "fontFamily": "\"Ysabeau Office\""}]}], "fontSizes": [{"fluid": false, "name": "Small", "size": "0.875rem", "slug": "small"}, {"fluid": {"max": "1.125rem", "min": "1rem"}, "name": "Medium", "size": "1rem", "slug": "medium"}, {"fluid": {"max": "1.375rem", "min": "1.125rem"}, "name": "Large", "size": "1.38rem", "slug": "large"}, {"fluid": {"max": "2rem", "min": "1.75rem"}, "name": "Extra Large", "size": "1.75rem", "slug": "x-large"}, {"fluid": {"max": "2.6rem", "min": "1.4rem"}, "name": "Extra Extra Large", "size": "2.6rem", "slug": "xx-large"}]}}, "styles": {"typography": {"fontFamily": "var:preset|font-family|ysabeau-office", "letterSpacing": "-0.24px"}, "blocks": {"core/navigation": {"typography": {"fontSize": "1.25rem"}}, "core/post-title": {"typography": {"fontWeight": "900", "letterSpacing": "-0.96px"}}, "core/pullquote": {"typography": {"fontSize": "var:preset|font-size|xx-large"}, "elements": {"cite": {"typography": {"fontSize": "var:preset|font-size|medium", "letterSpacing": "-0.14px"}}}}, "core/quote": {"elements": {"cite": {"typography": {"fontSize": "var:preset|font-size|medium", "letterSpacing": "-0.14px"}}}}, "core/query-title": {"typography": {"fontWeight": "900"}}, "core/site-title": {"typography": {"fontFamily": "var:preset|font-family|ysabeau-office", "textTransform": "uppercase", "letterSpacing": "1.6px"}}}, "elements": {"button": {"typography": {"fontFamily": "var:preset|font-family|literata", "fontSize": "var:preset|font-size|medium", "fontWeight": "900", "letterSpacing": "-0.36px"}}, "heading": {"typography": {"fontFamily": "var:preset|font-family|literata", "fontWeight": "900", "lineHeight": "1.2"}}, "h5": {"typography": {"letterSpacing": "0px"}}, "h6": {"typography": {"fontWeight": "900", "letterSpacing": "0px"}}}, "variations": {"post-terms-1": {"typography": {"fontSize": "var:preset|font-size|medium"}}}}}