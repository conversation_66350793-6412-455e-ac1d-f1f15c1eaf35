# 🇩🇴 WORDPRESS TRADICIONAL - PASO A PASO

**Desarrollado por Jose L Encarnacion (JoseTusabe)**  
**Desde San José de <PERSON>coa, República Dominicana 🇩🇴**

---

## 🎯 **INSTALACIÓN WORDPRESS OFICIAL (MÉTODO TRADICIONAL)**

Esta es la forma "oficial" de instalar WordPress, paso a paso, como lo haría cualquier desarrollador profesional.

---

## 📋 **REQUISITOS PREVIOS**

### **Lo que necesitas tener instalado:**
```bash
✅ PHP 7.4 o superior
✅ MySQL 5.7 o superior (o MariaDB)
✅ Servidor web (Apache o Nginx) - O usar PHP built-in server
✅ Extensiones PHP: mysqli, curl, gd, mbstring, xml, zip
```

### **Verificar que tienes todo:**
```bash
# Verificar PHP
php --version

# Verificar MySQL
mysql --version

# Verificar extensiones PHP
php -m | grep -E "(mysqli|curl|gd|mbstring|xml|zip)"
```

---

## 🚀 **PASO 1: PREPARAR EL ENTORNO**

### **1.1 Crear directorio del proyecto**
```bash
mkdir WordPress-Tradicional
cd WordPress-Tradicional
```

### **1.2 Descargar WordPress**
```bash
# Opción 1: Con curl
curl -O https://wordpress.org/latest.tar.gz
tar -xzf latest.tar.gz
mv wordpress/* .
rm -rf wordpress latest.tar.gz

# Opción 2: Con wget
wget https://wordpress.org/latest.tar.gz
tar -xzf latest.tar.gz
mv wordpress/* .
rm -rf wordpress latest.tar.gz
```

### **1.3 Verificar descarga**
```bash
ls -la
# Deberías ver archivos como:
# index.php, wp-config-sample.php, wp-admin/, wp-content/, wp-includes/
```

---

## 🗄️ **PASO 2: CONFIGURAR BASE DE DATOS**

### **2.1 Acceder a MySQL**
```bash
# Como root (sin contraseña)
mysql -u root

# O con contraseña
mysql -u root -p
```

### **2.2 Crear base de datos**
```sql
-- Crear base de datos
CREATE DATABASE wordpress_tradicional CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Crear usuario
CREATE USER 'wp_user'@'localhost' IDENTIFIED BY 'wp_password_123';

-- Dar permisos
GRANT ALL PRIVILEGES ON wordpress_tradicional.* TO 'wp_user'@'localhost';

-- Aplicar cambios
FLUSH PRIVILEGES;

-- Verificar
SHOW DATABASES;
SELECT User, Host FROM mysql.user WHERE User = 'wp_user';

-- Salir
EXIT;
```

### **2.3 Verificar conexión**
```bash
mysql -u wp_user -p'wp_password_123' wordpress_tradicional -e "SELECT 'Conexión exitosa' as test;"
```

---

## ⚙️ **PASO 3: CONFIGURAR WORDPRESS**

### **3.1 Copiar archivo de configuración**
```bash
cp wp-config-sample.php wp-config.php
```

### **3.2 Editar wp-config.php**
```bash
# Abrir con tu editor favorito
nano wp-config.php
# o
vim wp-config.php
# o
code wp-config.php
```

### **3.3 Configurar base de datos**
Buscar estas líneas y cambiarlas:
```php
// ** Configuración de Base de Datos ** //
define( 'DB_NAME', 'wordpress_tradicional' );
define( 'DB_USER', 'wp_user' );
define( 'DB_PASSWORD', 'wp_password_123' );
define( 'DB_HOST', 'localhost' );
define( 'DB_CHARSET', 'utf8mb4' );
define( 'DB_COLLATE', '' );
```

### **3.4 Generar claves de seguridad**
```bash
# Ir a: https://api.wordpress.org/secret-key/1.1/salt/
# Copiar las claves generadas y reemplazar en wp-config.php

# O usar curl para obtenerlas:
curl -s https://api.wordpress.org/secret-key/1.1/salt/
```

Reemplazar estas líneas:
```php
define('AUTH_KEY',         'poner aquí la clave generada');
define('SECURE_AUTH_KEY',  'poner aquí la clave generada');
define('LOGGED_IN_KEY',    'poner aquí la clave generada');
define('NONCE_KEY',        'poner aquí la clave generada');
define('AUTH_SALT',        'poner aquí la clave generada');
define('SECURE_AUTH_SALT', 'poner aquí la clave generada');
define('LOGGED_IN_SALT',   'poner aquí la clave generada');
define('NONCE_SALT',       'poner aquí la clave generada');
```

### **3.5 Configurar prefijo de tablas (opcional)**
```php
$table_prefix = 'wp_';
```

### **3.6 Habilitar debug (para desarrollo)**
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

---

## 🔒 **PASO 4: CONFIGURAR PERMISOS**

### **4.1 Permisos básicos**
```bash
# Permisos para directorios
find . -type d -exec chmod 755 {} \;

# Permisos para archivos
find . -type f -exec chmod 644 {} \;

# Permisos especiales para wp-config.php
chmod 600 wp-config.php

# Permisos para wp-content (uploads)
chmod -R 755 wp-content/
```

### **4.2 Verificar permisos**
```bash
ls -la wp-config.php
ls -la wp-content/
```

---

## 🚀 **PASO 5: INICIAR SERVIDOR**

### **5.1 Servidor PHP integrado (para desarrollo)**
```bash
php -S localhost:8080
```

### **5.2 O configurar Apache/Nginx (para producción)**
```apache
# Apache Virtual Host
<VirtualHost *:80>
    DocumentRoot /ruta/a/tu/WordPress-Tradicional
    ServerName wordpress-local.test
    
    <Directory /ruta/a/tu/WordPress-Tradicional>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

---

## 🌐 **PASO 6: EJECUTAR INSTALACIÓN WEB**

### **6.1 Abrir navegador**
```
http://localhost:8080
```

### **6.2 Seguir asistente de instalación**
1. **Seleccionar idioma:** Español
2. **Información del sitio:**
   - Título del sitio: `Mi WordPress Tradicional`
   - Nombre de usuario: `admin`
   - Contraseña: `contraseña_segura_123`
   - Tu email: `<EMAIL>`
3. **Hacer clic en "Instalar WordPress"**

### **6.3 Verificar instalación**
- Frontend: `http://localhost:8080`
- Backend: `http://localhost:8080/wp-admin`

---

## 🔍 **PASO 7: VERIFICACIÓN POST-INSTALACIÓN**

### **7.1 Verificar archivos**
```bash
ls -la
# Deberías ver wp-config.php y otros archivos de WordPress
```

### **7.2 Verificar base de datos**
```bash
mysql -u wp_user -p'wp_password_123' wordpress_tradicional -e "SHOW TABLES;"
# Deberías ver tablas como wp_posts, wp_users, etc.
```

### **7.3 Verificar acceso admin**
```
http://localhost:8080/wp-admin
Usuario: admin
Contraseña: contraseña_segura_123
```

---

## 🎨 **PASO 8: PERSONALIZACIÓN BÁSICA**

### **8.1 Cambiar tema**
1. Ir a `Apariencia > Temas`
2. Activar un tema diferente

### **8.2 Crear primer post**
1. Ir a `Entradas > Añadir nueva`
2. Escribir contenido
3. Publicar

### **8.3 Configurar permalinks**
1. Ir a `Ajustes > Enlaces permanentes`
2. Seleccionar estructura deseada

---

## ⚠️ **PROBLEMAS COMUNES Y SOLUCIONES**

### **Error de conexión a base de datos**
```bash
# Verificar que MySQL esté corriendo
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# Verificar credenciales
mysql -u wp_user -p'wp_password_123' wordpress_tradicional
```

### **Error de permisos**
```bash
# Corregir permisos
sudo chown -R www-data:www-data .  # Linux
sudo chown -R _www:_www .  # macOS

chmod -R 755 wp-content/
chmod 644 wp-config.php
```

### **Error 500**
```bash
# Verificar logs de error
tail -f wp-content/debug.log
tail -f /var/log/apache2/error.log
```

### **Problemas de memoria**
```php
// Agregar a wp-config.php
ini_set('memory_limit', '256M');
define('WP_MEMORY_LIMIT', '256M');
```

---

## 📊 **COMPARACIÓN: TRADICIONAL VS SOLOYLIBRE**

| Aspecto | WordPress Tradicional | SoloYLibre Ultimate |
|---------|----------------------|-------------------|
| **Tiempo de instalación** | 30-60 minutos | 5 minutos |
| **Pasos manuales** | 15+ pasos | 1 comando |
| **Conocimiento requerido** | Intermedio-Avanzado | Principiante |
| **Configuración** | Manual completa | Automática |
| **Personalización** | Desde cero | Preconfigurada |
| **Solución de problemas** | Manual | Scripts incluidos |
| **Tema** | Básico | Personalizado dominicano |
| **Base de datos** | MySQL manual | MySQL automático o SQLite |

---

## 🎯 **¿CUÁNDO USAR CADA MÉTODO?**

### **WordPress Tradicional - Usar cuando:**
- ✅ Tienes experiencia con servidores
- ✅ Necesitas control total de la configuración
- ✅ Vas a deployar en hosting profesional
- ✅ Quieres aprender el proceso completo
- ✅ Tienes requisitos específicos de configuración

### **SoloYLibre Ultimate - Usar cuando:**
- ✅ Eres principiante
- ✅ Quieres algo que funcione rápido
- ✅ Estás desarrollando localmente
- ✅ Necesitas un entorno de pruebas
- ✅ Quieres enfocarte en el contenido, no en la configuración

---

## 📞 **SOPORTE**

### **Para WordPress Tradicional:**
- 📚 Documentación oficial: https://wordpress.org/support/
- 🌐 Foros de WordPress: https://wordpress.org/support/forums/
- 📖 Codex: https://codex.wordpress.org/

### **Para SoloYLibre Ultimate:**
```
👨‍💻 Jose L Encarnacion (JoseTusabe)
🏔️ San José de Ocoa, República Dominicana 🇩🇴
📧 <EMAIL>
📞 718-713-5500
🌐 soloylibre.com
```

---

## 🎉 **CONCLUSIÓN**

**¡Dale paisano!** 🇩🇴

Ahora entiendes la diferencia:

- **WordPress Tradicional:** Es como construir una casa desde cero - tienes control total pero requiere mucho trabajo y conocimiento.

- **SoloYLibre Ultimate:** Es como comprar una casa ya construida y decorada - lista para vivir inmediatamente.

**Ambos métodos son válidos.** La elección depende de tu nivel de experiencia y lo que necesites.

**¡Que viva San José de Ocoa y que viva la República Dominicana!** 🇩🇴🚀
