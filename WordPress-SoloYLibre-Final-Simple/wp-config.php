<?php
/**
 * WordPress SoloYLibre Ultimate Final - Configuración Simple
 * Desarrollado por <PERSON>nacion (JoseTusabe)
 * Desde San José de <PERSON>coa, República Dominicana 🇩🇴
 */

// ** Configuración de Base de Datos ** //
define( 'DB_NAME', 'soloylibre_final' );
define( 'DB_USER', 'root' );
define( 'DB_PASSWORD', '' );
define( 'DB_HOST', 'localhost' );
define( 'DB_CHARSET', 'utf8mb4' );
define( 'DB_COLLATE', '' );

// ** Claves de Seguridad ** //
define('AUTH_KEY',         'SoloYLibre-Auth-Key-Final-JoseTusabe-San-Jose-O<PERSON>');
define('SECURE_AUTH_KEY',  'SoloYLibre-Secure-Auth-Key-Final-Republica-Dominicana');
define('LOGGED_IN_KEY',    'SoloYLibre-Logged-In-Key-Final-JoseTusabe-Developer');
define('NONCE_KEY',        'SoloYLibre-Nonce-Key-Final-WordPress-Ultimate');
define('AUTH_SALT',        'SoloYLibre-Auth-Salt-Final-admin-soloylibre-com');
define('SECURE_AUTH_SALT', 'SoloYLibre-Secure-Auth-Salt-Final-************');
define('LOGGED_IN_SALT',   'SoloYLibre-Logged-In-Salt-Final-Synology-RS3618xs');
define('NONCE_SALT',       'SoloYLibre-Nonce-Salt-Final-56GB-RAM-36TB-Storage');

// ** Prefijo de Tablas ** //
$table_prefix = 'wp_';

// ** Configuración de WordPress ** //
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('WP_MEMORY_LIMIT', '512M');
define('ALLOW_UNFILTERED_UPLOADS', true);

// ** Información del Desarrollador ** //
define('SOLOYLIBRE_DEVELOPER', 'Jose L Encarnacion (JoseTusabe)');
define('SOLOYLIBRE_LOCATION', 'San José de Ocoa, República Dominicana');
define('SOLOYLIBRE_EMAIL', '<EMAIL>');
define('SOLOYLIBRE_PHONE', '************');

// ** WordPress Core ** //
if ( ! defined( 'ABSPATH' ) ) {
    define( 'ABSPATH', __DIR__ . '/' );
}
require_once ABSPATH . 'wp-settings.php';
