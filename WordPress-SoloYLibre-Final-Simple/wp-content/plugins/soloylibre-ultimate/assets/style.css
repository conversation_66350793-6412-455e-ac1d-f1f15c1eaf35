/* SoloYLibre Ultimate Plugin Styles */
.soloylibre-widget {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin: 20px 0;
    text-align: center;
}

.dominican-flag {
    font-size: 1.2rem;
    margin: 0 5px;
    animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
}

.soloylibre-info-box {
    background: #f0f9ff;
    border-left: 5px solid #0284c7;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}

.soloylibre-developer-box {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin: 20px 0;
}

.soloylibre-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.soloylibre-stat-item {
    background: white;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-top: 3px solid #667eea;
}

.soloylibre-stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.soloylibre-stat-label {
    color: #666;
    font-size: 0.9rem;
}
