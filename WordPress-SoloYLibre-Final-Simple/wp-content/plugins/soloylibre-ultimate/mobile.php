<?php
/**
 * Funcionalidades Móviles SoloYLibre
 * Desarrollado por <PERSON>ion (JoseTusabe)
 */

class SoloYLibre_Mobile {

    public function __construct() {
        add_action('wp_head', array($this, 'add_mobile_meta'));
        add_action('wp_enqueue_scripts', array($this, 'mobile_styles'));
        add_action('init', array($this, 'mobile_optimizations'));
    }

    public function add_mobile_meta() {
        echo '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">';
        echo '<meta name="mobile-web-app-capable" content="yes">';
        echo '<meta name="apple-mobile-web-app-capable" content="yes">';
        echo '<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">';
        echo '<meta name="theme-color" content="#667eea">';

        // PWA manifest
        echo '<link rel="manifest" href="' . plugin_dir_url(__FILE__) . 'manifest.json">';
    }

    public function mobile_styles() {
        if (wp_is_mobile()) {
            wp_enqueue_style(
                'soloylibre-mobile',
                plugin_dir_url(__FILE__) . 'assets/mobile.css',
                array(),
                '1.0.0'
            );
        }
    }

    public function mobile_optimizations() {
        if (wp_is_mobile()) {
            // Reducir calidad de imágenes en móvil
            add_filter('jpeg_quality', function() { return 75; });

            // Lazy loading para imágenes
            add_filter('wp_get_attachment_image_attributes', array($this, 'add_lazy_loading'));
        }
    }

    public function add_lazy_loading($attr) {
        $attr['loading'] = 'lazy';
        return $attr;
    }
}

new SoloYLibre_Mobile();
?>
