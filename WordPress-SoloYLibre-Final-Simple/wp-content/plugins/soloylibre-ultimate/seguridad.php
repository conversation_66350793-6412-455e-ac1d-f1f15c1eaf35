<?php
/**
 * Sistema de Seguridad SoloYLibre
 * Desarrollado por <PERSON> (JoseTusabe)
 */

class SoloYLibre_Security {

    public function __construct() {
        add_action('init', array($this, 'init_security'));
        add_action('wp_login_failed', array($this, 'log_failed_login'));
        add_filter('login_headerurl', array($this, 'custom_login_url'));
        add_filter('login_headertitle', array($this, 'custom_login_title'));
    }

    public function init_security() {
        // Ocultar versión de WordPress
        remove_action('wp_head', 'wp_generator');

        // Deshabilitar XML-RPC si no se necesita
        add_filter('xmlrpc_enabled', '__return_false');

        // Agregar headers de seguridad
        add_action('send_headers', array($this, 'add_security_headers'));

        // Limitar intentos de login
        $this->limit_login_attempts();
    }

    public function add_security_headers() {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }

    public function limit_login_attempts() {
        $ip = $_SERVER['REMOTE_ADDR'];
        $attempts = get_transient('login_attempts_' . $ip);

        if ($attempts && $attempts >= 5) {
            wp_die('Demasiados intentos de login. Intenta de nuevo en 15 minutos.');
        }
    }

    public function log_failed_login($username) {
        $ip = $_SERVER['REMOTE_ADDR'];
        $attempts = get_transient('login_attempts_' . $ip) ?: 0;
        $attempts++;

        set_transient('login_attempts_' . $ip, $attempts, 15 * MINUTE_IN_SECONDS);

        // Log del intento fallido
        error_log("Login fallido para usuario: $username desde IP: $ip");
    }

    public function custom_login_url() {
        return home_url();
    }

    public function custom_login_title() {
        return 'SoloYLibre WordPress Ultimate - Desarrollado desde San José de Ocoa 🇩🇴';
    }

    public function get_security_status() {
        $status = array(
            'wp_version_hidden' => !has_action('wp_head', 'wp_generator'),
            'xmlrpc_disabled' => !apply_filters('xmlrpc_enabled', true),
            'security_headers' => true,
            'login_protection' => true,
            'developer_signature' => 'Jose L Encarnacion (JoseTusabe) - San José de Ocoa 🇩🇴'
        );

        return $status;
    }
}

new SoloYLibre_Security();
?>
