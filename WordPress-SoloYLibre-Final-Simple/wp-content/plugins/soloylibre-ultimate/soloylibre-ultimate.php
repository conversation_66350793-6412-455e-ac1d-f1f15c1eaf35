<?php
/**
 * Plugin Name: SoloYLibre Ultimate
 * Description: Plugin avanzado desarrollado por <PERSON> (JoseTusabe) desde San José de <PERSON>, República Dominicana 🇩🇴
 * Version: 1.0.0
 * Author: <PERSON> (JoseTusabe)
 * Author URI: https://soloylibre.com
 * Text Domain: soloylibre-ultimate
 */

// Evitar acceso directo
if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibreUltimate {

    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('wp_dashboard_setup', array($this, 'dashboard_widgets'));
        add_shortcode('soloylibre_info', array($this, 'info_shortcode'));
        add_shortcode('dominican_flag', array($this, 'flag_shortcode'));
    }

    public function init() {
        // Registrar post type personalizado
        $this->register_custom_post_types();

        // Agregar soporte para características adicionales
        add_theme_support('custom-background');
        add_theme_support('custom-header');
    }

    public function enqueue_scripts() {
        wp_enqueue_style(
            'soloylibre-ultimate-style',
            plugin_dir_url(__FILE__) . 'assets/style.css',
            array(),
            '1.0.0'
        );

        wp_enqueue_script(
            'soloylibre-ultimate-script',
            plugin_dir_url(__FILE__) . 'assets/script.js',
            array('jquery'),
            '1.0.0',
            true
        );
    }

    public function admin_menu() {
        add_menu_page(
            'SoloYLibre Ultimate',
            'SoloYLibre 🇩🇴',
            'manage_options',
            'soloylibre-ultimate',
            array($this, 'admin_page'),
            'dashicons-flag',
            30
        );
    }

    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>🇩🇴 SoloYLibre Ultimate</h1>
            <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h2>Desarrollado desde San José de Ocoa</h2>
                <p><strong>Jose L Encarnacion (JoseTusabe)</strong></p>
                <p>📧 <EMAIL> | 📞 ************</p>
                <p>🖥️ Synology RS3618xs - 56GB RAM - 36TB</p>
            </div>

            <div class="soloylibre-features">
                <h3>🚀 Características Avanzadas</h3>
                <ul>
                    <li>✅ Tema dominicano personalizado</li>
                    <li>✅ Widgets personalizados</li>
                    <li>✅ Shortcodes únicos</li>
                    <li>✅ Optimización automática</li>
                    <li>✅ Seguridad avanzada</li>
                </ul>
            </div>

            <div class="soloylibre-stats">
                <h3>📊 Estadísticas del Sistema</h3>
                <p><strong>Versión WordPress:</strong> <?php echo get_bloginfo('version'); ?></p>
                <p><strong>Tema Activo:</strong> <?php echo wp_get_theme()->get('Name'); ?></p>
                <p><strong>Plugins Activos:</strong> <?php echo count(get_option('active_plugins')); ?></p>
                <p><strong>Memoria PHP:</strong> <?php echo ini_get('memory_limit'); ?></p>
            </div>
        </div>

        <style>
            .soloylibre-features, .soloylibre-stats {
                background: white;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .soloylibre-features ul {
                list-style: none;
                padding: 0;
            }
            .soloylibre-features li {
                padding: 5px 0;
                border-bottom: 1px solid #eee;
            }
        </style>
        <?php
    }

    public function dashboard_widgets() {
        wp_add_dashboard_widget(
            'soloylibre_ultimate_widget',
            '🇩🇴 SoloYLibre Ultimate - Estado del Sistema',
            array($this, 'dashboard_widget_content')
        );
    }

    public function dashboard_widget_content() {
        ?>
        <div style="text-align: center; padding: 15px; background: linear-gradient(135deg, #22c55e, #16a34a); color: white; border-radius: 10px; margin: -12px;">
            <h3>🎉 ¡Sistema Funcionando Perfectamente!</h3>
            <p><strong>WordPress SoloYLibre Ultimate</strong></p>
            <p>Desarrollado desde San José de Ocoa, República Dominicana 🇩🇴</p>
            <hr style="border-color: rgba(255,255,255,0.3);">
            <p><strong>Jose L Encarnacion (JoseTusabe)</strong></p>
            <p>📧 <EMAIL> | 📞 ************</p>
        </div>
        <?php
    }

    public function register_custom_post_types() {
        // Post type para proyectos
        register_post_type('proyectos_rd', array(
            'labels' => array(
                'name' => 'Proyectos RD',
                'singular_name' => 'Proyecto RD',
                'add_new' => 'Añadir Proyecto',
                'add_new_item' => 'Añadir Nuevo Proyecto',
                'edit_item' => 'Editar Proyecto',
                'new_item' => 'Nuevo Proyecto',
                'view_item' => 'Ver Proyecto',
                'search_items' => 'Buscar Proyectos',
                'not_found' => 'No se encontraron proyectos',
                'not_found_in_trash' => 'No hay proyectos en la papelera'
            ),
            'public' => true,
            'has_archive' => true,
            'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
            'menu_icon' => 'dashicons-flag'
        ));
    }

    public function info_shortcode($atts) {
        $atts = shortcode_atts(array(
            'tipo' => 'completo'
        ), $atts);

        if ($atts['tipo'] === 'desarrollador') {
            return '<div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;">
                <h3>🇩🇴 Jose L Encarnacion (JoseTusabe)</h3>
                <p>📧 <EMAIL> | 📞 ************</p>
                <p>🏔️ San José de Ocoa, República Dominicana</p>
            </div>';
        }

        return '<div style="background: #f0f9ff; padding: 20px; border-radius: 10px; border-left: 5px solid #0284c7; margin: 20px 0;">
            <h3>🇩🇴 WordPress SoloYLibre Ultimate</h3>
            <p>Sistema desarrollado con amor dominicano desde San José de Ocoa</p>
            <p><strong>Desarrollador:</strong> Jose L Encarnacion (JoseTusabe)</p>
            <p><strong>Contacto:</strong> <EMAIL> | ************</p>
        </div>';
    }

    public function flag_shortcode($atts) {
        return '<span style="font-size: 1.5em; margin: 0 5px; animation: wave 2s ease-in-out infinite;">🇩🇴</span>';
    }
}

// Inicializar plugin
new SoloYLibreUltimate();

// Función para activación del plugin
register_activation_hook(__FILE__, 'soloylibre_ultimate_activate');
function soloylibre_ultimate_activate() {
    // Crear páginas por defecto
    $pages = array(
        'Sobre San José de Ocoa' => 'Información sobre la hermosa provincia de San José de Ocoa, República Dominicana.',
        'Contacto Desarrollador' => 'Información de contacto de Jose L Encarnacion (JoseTusabe).'
    );

    foreach ($pages as $title => $content) {
        $page = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => 'publish',
            'post_type' => 'page'
        );

        if (!get_page_by_title($title)) {
            wp_insert_post($page);
        }
    }

    // Flush rewrite rules
    flush_rewrite_rules();
}
?>

// Incluir archivos adicionales
require_once plugin_dir_path(__FILE__) . 'widget-soloylibre.php';
require_once plugin_dir_path(__FILE__) . 'estadisticas.php';
require_once plugin_dir_path(__FILE__) . 'seguridad.php';
require_once plugin_dir_path(__FILE__) . 'optimizacion.php';
require_once plugin_dir_path(__FILE__) . 'mobile.php';

// Agregar página de configuración avanzada
add_action('admin_menu', 'soloylibre_advanced_menu');
function soloylibre_advanced_menu() {
    add_submenu_page(
        'soloylibre-ultimate',
        'Configuración Avanzada',
        'Configuración Avanzada',
        'manage_options',
        'soloylibre-advanced',
        'soloylibre_advanced_page'
    );
}

function soloylibre_advanced_page() {
    $stats = new SoloYLibre_Stats();
    $security = new SoloYLibre_Security();
    $performance = new SoloYLibre_Performance();
    ?>
    <div class="wrap">
        <h1>🇩🇴 SoloYLibre Ultimate - Configuración Avanzada</h1>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">

            <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3>📊 Estadísticas del Sistema</h3>
                <?php $stats->display_stats_widget(); ?>
            </div>

            <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3>🛡️ Estado de Seguridad</h3>
                <?php
                $security_status = $security->get_security_status();
                foreach ($security_status as $key => $value) {
                    if ($key !== 'developer_signature') {
                        $icon = $value ? '✅' : '❌';
                        echo "<p>$icon " . ucfirst(str_replace('_', ' ', $key)) . "</p>";
                    }
                }
                ?>
                <p><strong>🇩🇴 <?php echo $security_status['developer_signature']; ?></strong></p>
            </div>

            <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3>🚀 Rendimiento</h3>
                <?php
                $perf_stats = $performance->get_performance_stats();
                echo "<p><strong>Memoria usada:</strong> " . size_format($perf_stats['memory_usage']) . "</p>";
                echo "<p><strong>Tiempo de ejecución:</strong> " . round($perf_stats['execution_time'], 3) . "s</p>";
                echo "<p><strong>Consultas DB:</strong> " . $perf_stats['queries_count'] . "</p>";
                echo "<p><strong>Cache:</strong> " . ($perf_stats['cache_enabled'] ? 'Habilitado' : 'Deshabilitado') . "</p>";
                ?>
                <p><strong>🇩🇴 <?php echo $perf_stats['optimized_by']; ?></strong></p>
            </div>

        </div>

        <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 30px; border-radius: 15px; text-align: center; margin: 30px 0;">
            <h2>🇩🇴 WordPress SoloYLibre Ultimate</h2>
            <p><strong>Sistema completo desarrollado desde San José de Ocoa, República Dominicana</strong></p>
            <hr style="border-color: rgba(255,255,255,0.3); margin: 20px 0;">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                <div>
                    <h4>👨‍💻 Desarrollador</h4>
                    <p>Jose L Encarnacion (JoseTusabe)</p>
                </div>
                <div>
                    <h4>📧 Contacto</h4>
                    <p><EMAIL></p>
                </div>
                <div>
                    <h4>📞 Teléfono</h4>
                    <p>************</p>
                </div>
                <div>
                    <h4>🖥️ Servidor</h4>
                    <p>Synology RS3618xs</p>
                </div>
            </div>
            <p style="margin-top: 20px; font-size: 1.1em;">
                <strong>¡Que viva San José de Ocoa y que viva la República Dominicana!</strong> 🇩🇴
            </p>
        </div>
    </div>
    <?php
}
