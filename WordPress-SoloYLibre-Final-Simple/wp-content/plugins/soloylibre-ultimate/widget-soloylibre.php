<?php
/**
 * Widget SoloYLibre Ultimate
 * Desarrollado por Jose <PERSON> Encarnacion (JoseTusabe)
 */

class SoloYLibre_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'soloylibre_widget',
            '🇩🇴 SoloYLibre Info',
            array('description' => 'Widget con información del desarrollador dominicano')
        );
    }

    public function widget($args, $instance) {
        echo $args['before_widget'];

        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        ?>
        <div class="soloylibre-widget">
            <h4>🇩🇴 Desarrollado desde San José de Ocoa</h4>
            <p><strong><PERSON> Encarnacion (JoseTusabe)</strong></p>
            <p>📧 <a href="mailto:<EMAIL>" style="color: #fbbf24;"><EMAIL></a></p>
            <p>📞 <a href="tel:************" style="color: #fbbf24;">************</a></p>
            <p>🏔️ San José de Ocoa, República Dominicana</p>
            <hr style="border-color: rgba(255,255,255,0.3);">
            <p style="font-size: 0.9em; opacity: 0.9;">
                🖥️ Synology RS3618xs - 56GB RAM - 36TB<br>
                ✅ WordPress SoloYLibre Ultimate
            </p>
        </div>
        <?php

        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'SoloYLibre Info';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>">Título:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>"
                   name="<?php echo $this->get_field_name('title'); ?>" type="text"
                   value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? strip_tags($new_instance['title']) : '';
        return $instance;
    }
}

// Registrar widget
function register_soloylibre_widget() {
    register_widget('SoloYLibre_Widget');
}
add_action('widgets_init', 'register_soloylibre_widget');
?>
