<?php
/**
 * WordPress SoloYLibre Ultimate Real
 * Desarrollado por <PERSON>carnacion (JoseTusabe)
 * Desde San José de Ocoa, República Dominicana 🇩🇴
 */

// Configuración básica
define('SOLOYLIBRE_VERSION', '1.0.0');
define('SOLOYLIBRE_DEVELOPER', '<PERSON> (JoseTusabe)');
define('SOLOYLIBRE_LOCATION', 'San José de Ocoa, República Dominicana 🇩🇴');
define('SOLOYLIBRE_EMAIL', '<EMAIL>');
define('SOLOYLIBRE_PHONE', '************');

// Detectar ruta solicitada
$request = $_SERVER['REQUEST_URI'];
$path = parse_url($request, PHP_URL_PATH);

// Enrutamiento básico
if ($path === '/wp-admin' || $path === '/wp-admin/') {
    include 'wp-admin.php';
} elseif (strpos($path, '/wp-admin/') === 0) {
    include 'wp-admin.php';
} else {
    include 'frontend.php';
}
?>
