name: 🇩🇴 SoloYLibre Ultimate CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  DEVELOPER: "<PERSON> L Encarnacion (JoseTusabe)"
  LOCATION: "San José de Ocoa, República Dominicana"
  PROJECT: "SoloYLibre Ultimate 500%"

jobs:
  test:
    name: 🧪 Tests y Calidad
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout código
      uses: actions/checkout@v4
      
    - name: 🐳 Setup Docker
      uses: docker/setup-buildx-action@v3
      
    - name: 🧪 Ejecutar tests unitarios
      run: |
        echo "🇩🇴 Ejecutando tests desde San José de Ocoa"
        docker-compose -f docker-compose.test.yml up --abort-on-container-exit
        
    - name: 📊 Análisis de código con SonarQube
      uses: sonarqube-quality-gate-action@master
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        
    - name: 🔒 Escaneo de seguridad
      run: |
        echo "🛡️ Escaneando seguridad - Desarrollado por Jose <PERSON> Encarnacion"
        docker run --rm -v $(pwd):/app securecodewarrior/docker-security-scan

  build:
    name: 🏗️ Build y Push
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout código
      uses: actions/checkout@v4
      
    - name: 🔑 Login a Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        
    - name: 🏗️ Build y Push imágenes
      run: |
        echo "🇩🇴 Building desde San José de Ocoa por Jose L Encarnacion"
        docker-compose build
        docker-compose push

  deploy-staging:
    name: 🚀 Deploy a Staging
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: 🚀 Deploy a Kubernetes Staging
      run: |
        echo "🇩🇴 Deploying a staging desde San José de Ocoa"
        kubectl apply -f k8s/staging/
        
  deploy-production:
    name: 🌟 Deploy a Producción
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: 🌟 Deploy a Kubernetes Producción
      run: |
        echo "🇩🇴 Deploying a producción - SoloYLibre Ultimate"
        kubectl apply -f k8s/production/
        
    - name: 📧 Notificación de deploy exitoso
      run: |
        echo "✅ Deploy exitoso por Jose L Encarnacion desde San José de Ocoa"
        curl -X POST ${{ secrets.SLACK_WEBHOOK }} \
          -H 'Content-type: application/json' \
          --data '{"text":"🇩🇴 ¡Deploy exitoso de SoloYLibre Ultimate! Desarrollado desde San José de Ocoa por Jose L Encarnacion (JoseTusabe)"}'
