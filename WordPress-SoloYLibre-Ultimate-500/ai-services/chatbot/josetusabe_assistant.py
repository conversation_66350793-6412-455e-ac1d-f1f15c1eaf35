#!/usr/bin/env python3
"""
JoseTusabe AI Assistant - ChatBot Dominicano
Desarrollado por Jose L Encarnacion (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import openai
import json
from datetime import datetime

class JoseTusabeAssistant:
    def __init__(self):
        self.developer = "Jose L Encarnacion (JoseTusabe)"
        self.location = "San José de Ocoa, República Dominicana 🇩🇴"
        self.email = "<EMAIL>"
        self.phone = "************"
        
    def get_response(self, user_input):
        """Respuesta del asistente dominicano"""
        
        # Respuestas específicas dominicanas
        dominican_responses = {
            "hola": "¡Dale paisano! Soy el asistente de Jose L Encarnacion desde San José de Ocoa 🇩🇴",
            "quien eres": f"Soy el asistente IA de {self.developer}, desarrollado desde {self.location}",
            "contacto": f"📧 {self.email} | 📞 {self.phone} | 🏔️ {self.location}",
            "wordpress": "¡Tenemos el WordPress más brutal del mundo! SoloYLibre Ultimate al 500% 🚀",
            "dominicana": "¡Que viva la República Dominicana! 🇩🇴 Orgullo quisqueyano desde San José de Ocoa",
        }
        
        # Buscar respuesta
        for key, response in dominican_responses.items():
            if key in user_input.lower():
                return response
                
        return f"¡Dale! Soy el asistente de {self.developer}. ¿En qué te puedo ayudar desde San José de Ocoa? 🇩🇴"

# Instancia global
assistant = JoseTusabeAssistant()

if __name__ == "__main__":
    print("🤖 JoseTusabe Assistant iniciado desde San José de Ocoa 🇩🇴")
    while True:
        user_input = input("Usuario: ")
        if user_input.lower() in ['salir', 'exit', 'quit']:
            print("¡Dale paisano! ¡Que viva RD! 🇩🇴")
            break
        response = assistant.get_response(user_input)
        print(f"JoseTusabe Assistant: {response}")
