#!/usr/bin/env python3
"""
Sistema de Recomendaciones SoloYLibre
Por Jose <PERSON>carnacion (JoseTusabe) - San José de Ocoa, RD 🇩🇴
"""

import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

class SoloYLibreRecommendations:
    def __init__(self):
        self.developer = "Jose <PERSON> Encarnacion (JoseTusabe)"
        self.location = "San José de Ocoa, RD 🇩🇴"
        
    def recommend_content(self, user_preferences):
        """Recomienda contenido basado en preferencias"""
        
        # Contenido dominicano predefinido
        dominican_content = [
            "WordPress SoloYLibre Ultimate - Tecnología dominicana",
            "San José de Ocoa - Tierra de innovación",
            "Desarrollo desde República Dominicana",
            "Jose L Encarnacion - Desarrollador dominicano",
            "Tecnología del Caribe - Orgullo nacional"
        ]
        
        # Algoritmo simple de recomendación
        recommendations = []
        for content in dominican_content:
            if any(pref.lower() in content.lower() for pref in user_preferences):
                recommendations.append(content)
                
        return recommendations if recommendations else dominican_content[:3]

# Instancia global
recommender = SoloYLibreRecommendations()
