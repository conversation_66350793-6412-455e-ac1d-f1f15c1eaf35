version: '3.8'

services:
  # API Gateway
  api-gateway:
    build: ./microservicios/api-gateway
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - DEVELOPER=Jose_L_Encarnacion_JoseTusabe
      - LOCATION=San_Jose_de_Ocoa_RD
    depends_on:
      - auth-service
      - user-service
      - content-service
    networks:
      - soloylibre-network

  # Servicio de Autenticación
  auth-service:
    build: ./microservicios/auth-service
    ports:
      - "3001:3000"
    environment:
      - DATABASE_URL=**************************************************/auth_db
      - JWT_SECRET=SoloYLibre_San_Jose_Ocoa_RD_2025
      - DEVELOPER_INFO=Jose_L_Encarnacion_JoseTusabe_718_713_5500
    depends_on:
      - postgres
      - redis
    networks:
      - soloylibre-network

  # Servicio de Usuarios
  user-service:
    build: ./microservicios/user-service
    ports:
      - "3002:3000"
    environment:
      - DATABASE_URL=**************************************************/users_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - soloylibre-network

  # Servicio de Contenido
  content-service:
    build: ./microservicios/content-service
    ports:
      - "3003:3000"
    environment:
      - DATABASE_URL=**************************************************/content_db
      - ELASTICSEARCH_URL=http://elasticsearch:9200
    depends_on:
      - postgres
      - elasticsearch
    networks:
      - soloylibre-network

  # Servicio de Medios
  media-service:
    build: ./microservicios/media-service
    ports:
      - "3004:3000"
    environment:
      - S3_BUCKET=soloylibre-media-san-jose-ocoa
      - CDN_URL=https://cdn.soloylibre.com
    networks:
      - soloylibre-network

  # Servicio de IA
  ai-service:
    build: ./microservicios/ai-service
    ports:
      - "3008:3000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_PATH=/models
      - DEVELOPER=Jose_L_Encarnacion_San_Jose_Ocoa
    volumes:
      - ./ai-models:/models
    networks:
      - soloylibre-network

  # Base de Datos Principal
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=soloylibre2025
      - POSTGRES_MULTIPLE_DATABASES=auth_db,users_db,content_db,analytics_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sh:/docker-entrypoint-initdb.d/init-databases.sh
    ports:
      - "5432:5432"
    networks:
      - soloylibre-network

  # Redis para Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - soloylibre-network

  # Elasticsearch para Búsqueda
  elasticsearch:
    image: elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - soloylibre-network

  # Kibana para Visualización
  kibana:
    image: kibana:8.11.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - soloylibre-network

  # Prometheus para Métricas
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - soloylibre-network

  # Grafana para Dashboards
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=soloylibre2025
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
    networks:
      - soloylibre-network

  # Jaeger para Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - soloylibre-network

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
  prometheus_data:
  grafana_data:

networks:
  soloylibre-network:
    driver: bridge
    labels:
      - "developer=Jose_L_Encarnacion_JoseTusabe"
      - "location=San_Jose_de_Ocoa_RD"
      - "project=SoloYLibre_Ultimate_500"
