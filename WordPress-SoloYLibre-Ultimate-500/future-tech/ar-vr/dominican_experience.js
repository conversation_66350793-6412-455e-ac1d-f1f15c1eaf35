/**
 * Dominican AR/VR Experience
 * Po<PERSON> (JoseTusabe)
 * Desde San José de Ocoa, RD 🇩🇴
 */

class DominicanARVRExperience {
    constructor() {
        this.developer = "Jose L Encarnacion (JoseTusabe)";
        this.location = "San José de Ocoa, República Dominicana 🇩🇴";
        this.experiences = [];
    }
    
    // Experiencia VR: Tour por San José de Ocoa
    createSanJoseVRTour() {
        return {
            name: "Virtual Tour: San José de Ocoa",
            description: "Recorre las hermosas montañas donde nació SoloYLibre",
            developer: this.developer,
            scenes: [
                {
                    id: 1,
                    name: "Plaza Central",
                    description: "Corazón de San José de Ocoa",
                    360_image: "san_jose_plaza_360.jpg",
                    audio: "Sonidos de la plaza dominicana"
                },
                {
                    id: 2,
                    name: "Montañas Ocoeñas", 
                    description: "Vista panorámica de las montañas",
                    360_image: "montanas_ocoa_360.jpg",
                    audio: "Sonidos de la naturaleza dominicana"
                },
                {
                    id: 3,
                    name: "SoloYLibre Tech Hub",
                    description: "Oficinas donde se desarrolla la magia",
                    360_image: "tech_hub_360.jpg",
                    audio: "Sonidos de desarrollo y innovación"
                }
            ],
            special_features: {
                dominican_flag: "Bandera RD ondeando en realidad virtual",
                merengue_background: "Música dominicana de fondo",
                developer_avatar: "Avatar de Jose L Encarnacion como guía"
            }
        };
    }
    
    // Experiencia AR: WordPress en Realidad Aumentada
    createWordPressAR() {
        return {
            name: "WordPress AR Dashboard",
            description: "Panel de WordPress en realidad aumentada",
            developer: this.developer,
            features: {
                floating_widgets: "Widgets flotando en el aire",
                gesture_control: "Control por gestos",
                voice_commands: "Comandos de voz en español",
                dominican_themes: "Temas con elementos dominicanos en 3D"
            },
            voice_commands: {
                "crear post": "Crear nueva entrada",
                "dale paisano": "Activar modo dominicano",
                "que viva rd": "Mostrar bandera dominicana",
                "san jose de ocoa": "Mostrar información del desarrollador"
            }
        };
    }
}

// Instancia global
const dominicanARVR = new DominicanARVRExperience();

module.exports = dominicanARVR;
