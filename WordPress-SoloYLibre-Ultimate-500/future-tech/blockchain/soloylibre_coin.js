/**
 * SoloYLibre Coin - Blockchain Dominicano
 * Por <PERSON> (JoseTusabe)
 * Desde San José de Ocoa, RD 🇩🇴
 */

const Web3 = require('web3');

class SoloYLibreCoin {
    constructor() {
        this.name = "SoloYLibre Coin";
        this.symbol = "SOLO";
        this.decimals = 18;
        this.totalSupply = 21000000; // 21 millones como Bitcoin
        this.developer = "Jose L Encarnacion (JoseTusabe)";
        this.origin = "San José de Ocoa, República Dominicana 🇩🇴";
    }
    
    // Smart Contract básico
    getContractCode() {
        return `
        pragma solidity ^0.8.0;
        
        contract SoloYLibreCoin {
            string public name = "SoloYLibre Coin";
            string public symbol = "SOLO";
            uint8 public decimals = 18;
            uint256 public totalSupply = 21000000 * 10**18;
            
            string public developer = "Jose L Encarnacion (JoseTusabe)";
            string public origin = "San José de Ocoa, República Dominicana";
            
            mapping(address => uint256) public balanceOf;
            mapping(address => mapping(address => uint256)) public allowance;
            
            event Transfer(address indexed from, address indexed to, uint256 value);
            event DominicanPride(string message);
            
            constructor() {
                balanceOf[msg.sender] = totalSupply;
                emit DominicanPride("¡Que viva la República Dominicana! 🇩🇴");
            }
            
            function transfer(address to, uint256 value) public returns (bool) {
                require(balanceOf[msg.sender] >= value, "Insufficient balance");
                balanceOf[msg.sender] -= value;
                balanceOf[to] += value;
                emit Transfer(msg.sender, to, value);
                return true;
            }
        }`;
    }
    
    // NFT Collection Dominicana
    getDominicanNFTCollection() {
        return {
            name: "Dominican Pride Collection",
            description: "NFTs celebrating Dominican culture from San José de Ocoa",
            creator: this.developer,
            location: this.origin,
            items: [
                {
                    id: 1,
                    name: "San José de Ocoa Mountains",
                    description: "Beautiful mountains where SoloYLibre was born",
                    rarity: "Legendary"
                },
                {
                    id: 2, 
                    name: "Dominican Flag Code",
                    description: "Code written with Dominican flag colors",
                    rarity: "Epic"
                },
                {
                    id: 3,
                    name: "JoseTusabe Developer",
                    description: "Portrait of the legendary Dominican developer",
                    rarity: "Mythic"
                }
            ]
        };
    }
}

module.exports = SoloYLibreCoin;
