/**
 * CDN Global SoloYLibre
 * Po<PERSON> (JoseTusabe)
 * Desde San José de Ocoa, RD 🇩🇴
 */

const CDN_CONFIG = {
  regions: {
    'americas': {
      primary: 'cdn-americas.soloylibre.com',
      backup: 'cdn-us-east.soloylibre.com',
      countries: ['US', 'CA', 'MX', 'BR', 'AR', 'DO'],
      developer_note: 'Región principal desde San José de Ocoa, RD 🇩🇴'
    },
    'europe': {
      primary: 'cdn-europe.soloylibre.com', 
      backup: 'cdn-eu-west.soloylibre.com',
      countries: ['DE', 'FR', 'IT', 'ES', 'UK'],
      developer_note: 'Expandiendo tecnología dominicana a Europa'
    },
    'asia': {
      primary: 'cdn-asia.soloylibre.com',
      backup: 'cdn-ap-southeast.soloylibre.com', 
      countries: ['JP', 'KR', 'CN', 'SG', 'IN'],
      developer_note: 'Llevando innovación dominicana a Asia'
    },
    'oceania': {
      primary: 'cdn-oceania.soloylibre.com',
      backup: 'cdn-au-east.soloylibre.com',
      countries: ['AU', 'NZ'],
      developer_note: 'Tecnología RD llegando a Oceanía'
    }
  },
  
  performance: {
    cache_ttl: 86400, // 24 horas
    compression: 'gzip, brotli',
    http2_enabled: true,
    ssl_enabled: true
  },
  
  developer: 'Jose L Encarnacion (JoseTusabe)',
  origin: 'San José de Ocoa, República Dominicana 🇩🇴',
  contact: '<EMAIL>'
};

module.exports = CDN_CONFIG;
