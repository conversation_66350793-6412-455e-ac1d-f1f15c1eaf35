global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    developer: "<PERSON> (JoseTusabe)"
    location: "San José de <PERSON>coa, República Dominicana"
    project: "SoloYLibre Ultimate 500%"

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:8080']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service:3000']
    metrics_path: '/metrics'

  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:3000']
    metrics_path: '/metrics'

  - job_name: 'content-service'
    static_configs:
      - targets: ['content-service:3000']
    metrics_path: '/metrics'

  - job_name: 'ai-service'
    static_configs:
      - targets: ['ai-service:3000']
    metrics_path: '/metrics'

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Reglas de alertas específicas para SoloYLibre
