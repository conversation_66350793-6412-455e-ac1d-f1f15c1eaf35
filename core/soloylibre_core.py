#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - Core System
Núcleo del sistema modular
Desarrollado por <PERSON> (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import os
import sys
import sqlite3
import json
import hashlib
import jwt
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

class SoloYLibreCore:
    """Núcleo del sistema SoloYLibre WordPress Ultimate"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.db_file = config.get('db_file')
        self.jwt_secret = secrets.token_hex(32)
        self.version = "1.0.0"
        self.build = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # Información de la empresa
        self.company_info = {
            'name': 'SoloYLibre Web Dev',
            'developer': '<PERSON> (JoseTusabe)',
            'nickname': '<PERSON><PERSON><PERSON><PERSON>',
            'location': 'San José de Ocoa, República Dominicana',
            'email': '<EMAIL>',
            'phone': '************',
            'websites': {
                'main': 'soloylibre.com',
                'personal': 'josetusabe.com',
                'photography': '1and1photo.com',
                'portfolio': 'joselencarnacion.com'
            },
            'server': 'Synology RS3618xs - 56GB RAM - 36TB Storage'
        }
        
        # Métricas del sistema
        self.metrics = {
            'page_views': 0,
            'unique_visitors': set(),
            'login_attempts': 0,
            'successful_logins': 0,
            'posts_created': 0,
            'system_uptime': datetime.now(),
            'api_calls': 0,
            'errors': 0
        }
        
    def get_db_connection(self) -> sqlite3.Connection:
        """Obtener conexión a la base de datos"""
        try:
            conn = sqlite3.connect(self.db_file)
            conn.row_factory = sqlite3.Row  # Para acceso por nombre de columna
            return conn
        except Exception as e:
            self.log_error(f"Database connection error: {e}")
            raise
            
    def execute_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """Ejecutar query SELECT y retornar resultados"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.fetchall()
        except Exception as e:
            self.log_error(f"Query execution error: {e}")
            return []
            
    def execute_update(self, query: str, params: tuple = ()) -> bool:
        """Ejecutar query INSERT/UPDATE/DELETE"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return True
        except Exception as e:
            self.log_error(f"Update execution error: {e}")
            return False
            
    def generate_jwt_token(self, user_id: int, username: str, role: str = 'user') -> str:
        """Generar token JWT para autenticación"""
        payload = {
            'user_id': user_id,
            'username': username,
            'role': role,
            'exp': datetime.utcnow() + timedelta(hours=24),
            'iat': datetime.utcnow(),
            'iss': 'soloylibre-wordpress',
            'company': self.company_info['name'],
            'developer': self.company_info['developer']
        }
        return jwt.encode(payload, self.jwt_secret, algorithm='HS256')
        
    def verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verificar y decodificar token JWT"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            self.log_error("JWT token expired")
            return None
        except jwt.InvalidTokenError:
            self.log_error("Invalid JWT token")
            return None
            
    def hash_password(self, password: str) -> str:
        """Hash seguro de contraseña"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
        
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verificar contraseña contra hash"""
        try:
            salt, hash_hex = hashed.split(':')
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash.hex() == hash_hex
        except:
            # Fallback para hashes antiguos (SHA256 simple)
            return hashlib.sha256(password.encode()).hexdigest() == hashed
            
    def log_analytics_event(self, event_type: str, user_id: Optional[int] = None, 
                          object_id: Optional[int] = None, data: Optional[Dict] = None) -> bool:
        """Registrar evento de analytics"""
        query = """
            INSERT INTO wp_analytics 
            (event_type, object_type, object_id, user_id, event_data, timestamp)
            VALUES (?, 'system', ?, ?, ?, CURRENT_TIMESTAMP)
        """
        params = (event_type, object_id, user_id, json.dumps(data) if data else None)
        return self.execute_update(query, params)
        
    def log_system_event(self, level: str, category: str, message: str, 
                        context: Optional[Dict] = None, user_id: Optional[int] = None) -> bool:
        """Registrar evento del sistema"""
        query = """
            INSERT INTO wp_system_logs 
            (log_level, log_category, message, context, user_id, timestamp)
            VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        """
        params = (level, category, message, json.dumps(context) if context else None, user_id)
        return self.execute_update(query, params)
        
    def log_error(self, message: str, context: Optional[Dict] = None) -> bool:
        """Registrar error del sistema"""
        self.metrics['errors'] += 1
        return self.log_system_event('ERROR', 'system', message, context)
        
    def log_info(self, message: str, context: Optional[Dict] = None) -> bool:
        """Registrar información del sistema"""
        return self.log_system_event('INFO', 'system', message, context)
        
    def log_warning(self, message: str, context: Optional[Dict] = None) -> bool:
        """Registrar advertencia del sistema"""
        return self.log_system_event('WARNING', 'system', message, context)
        
    def get_system_stats(self) -> Dict[str, Any]:
        """Obtener estadísticas del sistema"""
        try:
            stats = {}
            
            # Contar posts
            result = self.execute_query("SELECT COUNT(*) as count FROM wp_posts WHERE post_status = 'publish' AND post_type = 'post'")
            stats['posts'] = result[0]['count'] if result else 0
            
            # Contar páginas
            result = self.execute_query("SELECT COUNT(*) as count FROM wp_posts WHERE post_status = 'publish' AND post_type = 'page'")
            stats['pages'] = result[0]['count'] if result else 0
            
            # Contar usuarios
            result = self.execute_query("SELECT COUNT(*) as count FROM wp_users")
            stats['users'] = result[0]['count'] if result else 0
            
            # Contar comentarios
            result = self.execute_query("SELECT COUNT(*) as count FROM wp_comments WHERE comment_approved = '1'")
            stats['comments'] = result[0]['count'] if result else 0
            
            # Analytics de hoy
            result = self.execute_query("SELECT COUNT(*) as count FROM wp_analytics WHERE DATE(timestamp) = DATE('now')")
            stats['today_events'] = result[0]['count'] if result else 0
            
            # Métricas del sistema
            stats.update({
                'uptime': datetime.now() - self.metrics['system_uptime'],
                'page_views': self.metrics['page_views'],
                'unique_visitors': len(self.metrics['unique_visitors']),
                'login_attempts': self.metrics['login_attempts'],
                'successful_logins': self.metrics['successful_logins'],
                'posts_created': self.metrics['posts_created'],
                'api_calls': self.metrics['api_calls'],
                'errors': self.metrics['errors'],
                'version': self.version,
                'build': self.build
            })
            
            return stats
        except Exception as e:
            self.log_error(f"Error getting system stats: {e}")
            return {}
            
    def get_option(self, option_name: str, default: Any = None) -> Any:
        """Obtener opción del sistema"""
        result = self.execute_query("SELECT option_value FROM wp_options WHERE option_name = ?", (option_name,))
        if result:
            value = result[0]['option_value']
            # Intentar decodificar JSON
            try:
                return json.loads(value)
            except:
                return value
        return default
        
    def set_option(self, option_name: str, option_value: Any) -> bool:
        """Establecer opción del sistema"""
        # Codificar como JSON si es necesario
        if isinstance(option_value, (dict, list)):
            value = json.dumps(option_value)
        else:
            value = str(option_value)
            
        query = """
            INSERT OR REPLACE INTO wp_options (option_name, option_value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        """
        return self.execute_update(query, (option_name, value))
        
    def sanitize_input(self, input_str: str) -> str:
        """Sanitizar entrada de usuario"""
        if not isinstance(input_str, str):
            return ""
        
        # Remover caracteres peligrosos
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
        for char in dangerous_chars:
            input_str = input_str.replace(char, '')
        
        return input_str.strip()
        
    def validate_email(self, email: str) -> bool:
        """Validar formato de email"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
        
    def generate_slug(self, title: str) -> str:
        """Generar slug URL-friendly"""
        import re
        # Convertir a minúsculas
        slug = title.lower()
        # Reemplazar caracteres especiales
        slug = re.sub(r'[áàäâ]', 'a', slug)
        slug = re.sub(r'[éèëê]', 'e', slug)
        slug = re.sub(r'[íìïî]', 'i', slug)
        slug = re.sub(r'[óòöô]', 'o', slug)
        slug = re.sub(r'[úùüû]', 'u', slug)
        slug = re.sub(r'ñ', 'n', slug)
        # Remover caracteres no alfanuméricos excepto espacios y guiones
        slug = re.sub(r'[^a-z0-9\s-]', '', slug)
        # Reemplazar espacios con guiones
        slug = re.sub(r'\s+', '-', slug)
        # Remover guiones múltiples
        slug = re.sub(r'-+', '-', slug)
        # Remover guiones al inicio y final
        slug = slug.strip('-')
        
        return slug or 'untitled'
        
    def format_date(self, date_obj: datetime, format_str: str = '%d/%m/%Y %H:%M') -> str:
        """Formatear fecha"""
        if isinstance(date_obj, str):
            try:
                date_obj = datetime.fromisoformat(date_obj.replace('Z', '+00:00'))
            except:
                return date_obj
        
        if isinstance(date_obj, datetime):
            return date_obj.strftime(format_str)
        
        return str(date_obj)
        
    def get_company_info(self) -> Dict[str, Any]:
        """Obtener información de la empresa"""
        return self.company_info.copy()
        
    def get_system_info(self) -> Dict[str, Any]:
        """Obtener información del sistema"""
        return {
            'name': 'SoloYLibre WordPress Ultimate',
            'version': self.version,
            'build': self.build,
            'developer': self.company_info['developer'],
            'company': self.company_info['name'],
            'location': self.company_info['location'],
            'server': self.company_info['server'],
            'uptime': datetime.now() - self.metrics['system_uptime'],
            'python_version': sys.version,
            'platform': sys.platform
        }
        
    def increment_metric(self, metric_name: str, value: int = 1) -> None:
        """Incrementar métrica del sistema"""
        if metric_name in self.metrics:
            if isinstance(self.metrics[metric_name], (int, float)):
                self.metrics[metric_name] += value
                
    def add_unique_visitor(self, visitor_id: str) -> None:
        """Agregar visitante único"""
        self.metrics['unique_visitors'].add(visitor_id)
        
    def is_development_mode(self) -> bool:
        """Verificar si está en modo desarrollo"""
        return self.get_option('development_mode', False)
        
    def is_maintenance_mode(self) -> bool:
        """Verificar si está en modo mantenimiento"""
        return self.get_option('maintenance_mode', False)
        
    def print_system_header(self) -> None:
        """Imprimir header del sistema"""
        print("🇩🇴 " + "="*100)
        print("🚀 SoloYLibre WordPress Ultimate Complete System")
        print(f"👨‍💻 Desarrollado por {self.company_info['developer']}")
        print(f"🏔️ Desde {self.company_info['location']}")
        print(f"📧 {self.company_info['email']} | 📞 {self.company_info['phone']}")
        print("🌟 Sistema WordPress Profesional con TODOS los roles implementados")
        print("="*100)
        print("🎯 ARQUITECTURA MODULAR:")
        print("✅ Core System - Núcleo del sistema")
        print("✅ Authentication - Sistema de autenticación JWT")
        print("✅ Database - Gestión de base de datos optimizada")
        print("✅ Analytics - Métricas y estadísticas en tiempo real")
        print("✅ Security - Seguridad avanzada y encriptación")
        print("✅ API - REST API completa")
        print("✅ Frontend - Componentes modulares")
        print("✅ Backend - Microservicios")
        print("="*100)
