-- SoloYLibre WordPress Ultimate - Database Setup
-- Desarrollado por Jose <PERSON> (JoseTusabe)
-- <PERSON><PERSON>, República Dominicana 🇩🇴

-- Crear base de datos
CREATE DATABASE IF NOT EXISTS soloylibre_wp 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Crear usuario administrador
CREATE USER IF NOT EXISTS 'soloylibre_admin'@'localhost' 
IDENTIFIED BY 'SoloYLibre2025!';

-- <PERSON><PERSON><PERSON> todos los privilegios
GRANT ALL PRIVILEGES ON soloylibre_wp.* 
TO 'soloylibre_admin'@'localhost';

-- Crear usuario de solo lectura para reportes
CREATE USER IF NOT EXISTS 'soloylibre_readonly'@'localhost' 
IDENTIFIED BY 'SoloYLibreRead2025!';

-- O<PERSON>gar privilegios de solo lectura
GRANT SELECT ON soloylibre_wp.* 
TO 'soloylibre_readonly'@'localhost';

-- Crear usuario para backups
CREATE USER IF NOT EXISTS 'soloylibre_backup'@'localhost' 
IDENTIFIED BY 'SoloYLibreBackup2025!';

-- Otorgar privilegios para backups
GRANT SELECT, LOCK TABLES, SHOW VIEW ON soloylibre_wp.* 
TO 'soloylibre_backup'@'localhost';

-- Aplicar cambios
FLUSH PRIVILEGES;

-- Usar la base de datos
USE soloylibre_wp;

-- Crear tabla de información del desarrollador
CREATE TABLE IF NOT EXISTS wp_soloylibre_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    developer_name VARCHAR(255) NOT NULL DEFAULT 'Jose L Encarnacion (JoseTusabe)',
    developer_email VARCHAR(255) NOT NULL DEFAULT '<EMAIL>',
    developer_phone VARCHAR(20) NOT NULL DEFAULT '************',
    developer_location VARCHAR(255) NOT NULL DEFAULT 'San José de Ocoa, República Dominicana',
    server_info VARCHAR(255) NOT NULL DEFAULT 'Synology RS3618xs - 56GB RAM - 36TB Storage',
    websites TEXT DEFAULT 'josetusabe.com, soloylibre.com, 1and1photo.com, joselencarnacion.com',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertar información del desarrollador
INSERT INTO wp_soloylibre_info (
    developer_name,
    developer_email,
    developer_phone,
    developer_location,
    server_info,
    websites
) VALUES (
    'Jose L Encarnacion (JoseTusabe)',
    '<EMAIL>',
    '************',
    'San José de Ocoa, República Dominicana 🇩🇴',
    'Synology RS3618xs - 56GB RAM - 36TB Storage',
    'josetusabe.com, soloylibre.com, 1and1photo.com, joselencarnacion.com'
) ON DUPLICATE KEY UPDATE
    updated_at = CURRENT_TIMESTAMP;

-- Crear tabla de configuración personalizada
CREATE TABLE IF NOT EXISTS wp_soloylibre_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(255) NOT NULL UNIQUE,
    config_value TEXT,
    config_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertar configuraciones por defecto
INSERT INTO wp_soloylibre_config (config_key, config_value, config_description) VALUES
('theme_primary_color', '#667eea', 'Color primario del tema SoloYLibre'),
('theme_secondary_color', '#764ba2', 'Color secundario del tema SoloYLibre'),
('dominican_flag_emoji', '🇩🇴', 'Emoji de la bandera dominicana'),
('site_location', 'San José de Ocoa, República Dominicana', 'Ubicación del sitio web'),
('developer_signature', 'Desarrollado por Jose L Encarnacion (JoseTusabe)', 'Firma del desarrollador'),
('contact_email', '<EMAIL>', 'Email de contacto principal'),
('contact_phone', '************', 'Teléfono de contacto'),
('multisite_enabled', 'true', 'Indica si el multisite está habilitado'),
('pwa_enabled', 'true', 'Indica si PWA está habilitado'),
('security_level', 'high', 'Nivel de seguridad del sitio'),
('backup_frequency', 'daily', 'Frecuencia de backups automáticos'),
('analytics_enabled', 'true', 'Indica si analytics está habilitado'),
('cache_enabled', 'true', 'Indica si el cache está habilitado'),
('ssl_enabled', 'false', 'Indica si SSL está habilitado'),
('cdn_enabled', 'false', 'Indica si CDN está habilitado')
ON DUPLICATE KEY UPDATE
    updated_at = CURRENT_TIMESTAMP;

-- Crear tabla de logs personalizados
CREATE TABLE IF NOT EXISTS wp_soloylibre_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    log_type ENUM('info', 'warning', 'error', 'debug', 'security') NOT NULL DEFAULT 'info',
    log_message TEXT NOT NULL,
    log_data JSON,
    user_id INT DEFAULT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_log_type (log_type),
    INDEX idx_created_at (created_at),
    INDEX idx_user_id (user_id)
);

-- Insertar log de instalación
INSERT INTO wp_soloylibre_logs (log_type, log_message, log_data) VALUES
('info', 'SoloYLibre WordPress Ultimate instalado exitosamente', JSON_OBJECT(
    'developer', 'Jose L Encarnacion (JoseTusabe)',
    'location', 'San José de Ocoa, República Dominicana',
    'version', '1.0.0',
    'installation_date', NOW(),
    'database_version', VERSION()
));

-- Crear tabla de estadísticas
CREATE TABLE IF NOT EXISTS wp_soloylibre_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stat_date DATE NOT NULL,
    page_views INT DEFAULT 0,
    unique_visitors INT DEFAULT 0,
    posts_published INT DEFAULT 0,
    comments_received INT DEFAULT 0,
    users_registered INT DEFAULT 0,
    login_attempts INT DEFAULT 0,
    successful_logins INT DEFAULT 0,
    failed_logins INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_date (stat_date)
);

-- Insertar estadística inicial
INSERT INTO wp_soloylibre_stats (stat_date, page_views, unique_visitors) VALUES
(CURDATE(), 1, 1)
ON DUPLICATE KEY UPDATE
    page_views = page_views + 1,
    updated_at = CURRENT_TIMESTAMP;

-- Crear tabla de configuración multisite
CREATE TABLE IF NOT EXISTS wp_soloylibre_multisite (
    id INT AUTO_INCREMENT PRIMARY KEY,
    site_id INT NOT NULL,
    site_name VARCHAR(255) NOT NULL,
    site_domain VARCHAR(255) NOT NULL,
    site_path VARCHAR(255) NOT NULL DEFAULT '/',
    site_admin_email VARCHAR(255),
    site_status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    site_theme VARCHAR(255) DEFAULT 'soloylibre-ultimate',
    site_language VARCHAR(10) DEFAULT 'es_DO',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_site (site_domain, site_path)
);

-- Insertar sitio principal
INSERT INTO wp_soloylibre_multisite (
    site_id, 
    site_name, 
    site_domain, 
    site_path, 
    site_admin_email,
    site_theme,
    site_language
) VALUES (
    1,
    'SoloYLibre WordPress Ultimate',
    'localhost:8080',
    '/',
    '<EMAIL>',
    'soloylibre-ultimate',
    'es_DO'
) ON DUPLICATE KEY UPDATE
    updated_at = CURRENT_TIMESTAMP;

-- Crear índices para optimización
CREATE INDEX IF NOT EXISTS idx_soloylibre_config_key ON wp_soloylibre_config(config_key);
CREATE INDEX IF NOT EXISTS idx_soloylibre_logs_type_date ON wp_soloylibre_logs(log_type, created_at);
CREATE INDEX IF NOT EXISTS idx_soloylibre_stats_date ON wp_soloylibre_stats(stat_date);
CREATE INDEX IF NOT EXISTS idx_soloylibre_multisite_domain ON wp_soloylibre_multisite(site_domain);

-- Crear vistas para reportes
CREATE OR REPLACE VIEW vw_soloylibre_dashboard AS
SELECT 
    (SELECT COUNT(*) FROM wp_posts WHERE post_status = 'publish' AND post_type = 'post') as total_posts,
    (SELECT COUNT(*) FROM wp_comments WHERE comment_approved = '1') as total_comments,
    (SELECT COUNT(*) FROM wp_users) as total_users,
    (SELECT SUM(page_views) FROM wp_soloylibre_stats WHERE stat_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) as monthly_views,
    (SELECT config_value FROM wp_soloylibre_config WHERE config_key = 'developer_signature') as developer_info,
    (SELECT config_value FROM wp_soloylibre_config WHERE config_key = 'site_location') as site_location;

-- Crear procedimiento para limpiar logs antiguos
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS sp_cleanup_old_logs()
BEGIN
    -- Eliminar logs de más de 90 días (excepto errores y seguridad)
    DELETE FROM wp_soloylibre_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
    AND log_type NOT IN ('error', 'security');
    
    -- Eliminar estadísticas de más de 2 años
    DELETE FROM wp_soloylibre_stats 
    WHERE stat_date < DATE_SUB(CURDATE(), INTERVAL 2 YEAR);
    
    SELECT 'Limpieza de logs completada' as message;
END //
DELIMITER ;

-- Crear evento para limpieza automática (ejecutar semanalmente)
SET GLOBAL event_scheduler = ON;

CREATE EVENT IF NOT EXISTS ev_weekly_cleanup
ON SCHEDULE EVERY 1 WEEK
STARTS CURRENT_TIMESTAMP
DO
    CALL sp_cleanup_old_logs();

-- Mostrar información de la instalación
SELECT 
    'SoloYLibre WordPress Ultimate Database Setup Completed' as status,
    'Jose L Encarnacion (JoseTusabe)' as developer,
    'San José de Ocoa, República Dominicana 🇩🇴' as location,
    '<EMAIL>' as contact_email,
    '************' as contact_phone,
    NOW() as installation_time;

-- Mostrar usuarios creados
SELECT 
    User as username,
    Host as host,
    'Database access configured' as status
FROM mysql.user 
WHERE User LIKE 'soloylibre_%'
ORDER BY User;
