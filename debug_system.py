#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - Sistema de Debug Avanzado
Sistema de debug para detectar y corregir errores automáticamente
Desarrollado por Jose <PERSON>carnacion (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import os
import sys
import sqlite3
import json
import traceback
import requests
from datetime import datetime
from urllib.parse import urlparse
import subprocess

class SoloYLibreDebugger:
    """Sistema de debug avanzado para WordPress Ultimate"""
    
    def __init__(self):
        self.wordpress_dir = '/Users/<USER>/Desktop/SoloYLibre-WordPress'
        self.db_file = os.path.join(self.wordpress_dir, "soloylibre_ultimate.db")
        self.server_url = "http://localhost:8080"
        self.errors_found = []
        self.fixes_applied = []
        
    def print_header(self):
        """Header del sistema de debug"""
        print("🇩🇴 " + "="*80)
        print("🔧 SoloYLibre WordPress Ultimate - Sistema de Debug")
        print("👨‍💻 Desarrollado por Jose L Encarnacion (JoseTusabe)")
        print("🏔️ Desde San José de Ocoa, República Dominicana")
        print("🔍 Detectando y corrigiendo errores automáticamente...")
        print("="*80)
        
    def check_file_structure(self):
        """Verificar estructura de archivos"""
        print("\n📁 VERIFICANDO ESTRUCTURA DE ARCHIVOS...")
        
        required_files = [
            'soloylibre_ultimate_server.py',
            'soloylibre_ultimate_database.py',
            'soloylibre_design_system.py',
            'core/soloylibre_core.py',
            'modules/auth_security.py',
            'modules/content_manager.py',
            'templates/admin_login.py',
            'templates/admin_dashboard.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = os.path.join(self.wordpress_dir, file_path)
            if not os.path.exists(full_path):
                missing_files.append(file_path)
                self.errors_found.append(f"Archivo faltante: {file_path}")
        
        if missing_files:
            print(f"❌ Archivos faltantes: {len(missing_files)}")
            for file in missing_files:
                print(f"   - {file}")
        else:
            print("✅ Todos los archivos principales están presentes")
            
        return len(missing_files) == 0
    
    def check_database(self):
        """Verificar base de datos"""
        print("\n🗄️ VERIFICANDO BASE DE DATOS...")
        
        try:
            if not os.path.exists(self.db_file):
                print("❌ Base de datos no existe")
                self.errors_found.append("Base de datos no existe")
                return False
            
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # Verificar tablas principales
            required_tables = [
                'wp_users', 'wp_posts', 'wp_comments', 'wp_options',
                'wp_media', 'wp_terms', 'wp_analytics', 'wp_system_logs'
            ]
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            missing_tables = [table for table in required_tables if table not in existing_tables]
            
            if missing_tables:
                print(f"❌ Tablas faltantes: {missing_tables}")
                self.errors_found.append(f"Tablas faltantes: {missing_tables}")
                conn.close()
                return False
            
            # Verificar usuario admin
            cursor.execute("SELECT COUNT(*) FROM wp_users WHERE user_role = 'administrator'")
            admin_count = cursor.fetchone()[0]
            
            if admin_count == 0:
                print("❌ No hay usuarios administradores")
                self.errors_found.append("No hay usuarios administradores")
                conn.close()
                return False
            
            conn.close()
            print("✅ Base de datos verificada correctamente")
            return True
            
        except Exception as e:
            print(f"❌ Error verificando base de datos: {e}")
            self.errors_found.append(f"Error de base de datos: {e}")
            return False
    
    def check_server_status(self):
        """Verificar estado del servidor"""
        print("\n🌐 VERIFICANDO SERVIDOR...")
        
        try:
            # Verificar si el servidor está corriendo
            response = requests.get(f"{self.server_url}/api/health", timeout=5)
            if response.status_code == 200:
                print("✅ Servidor respondiendo correctamente")
                return True
            else:
                print(f"❌ Servidor respondió con código: {response.status_code}")
                self.errors_found.append(f"Servidor error {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ Servidor no está corriendo")
            self.errors_found.append("Servidor no está corriendo")
            return False
        except Exception as e:
            print(f"❌ Error conectando al servidor: {e}")
            self.errors_found.append(f"Error de conexión: {e}")
            return False
    
    def check_urls(self):
        """Verificar URLs principales"""
        print("\n🔗 VERIFICANDO URLS...")
        
        urls_to_check = [
            ('/', 'Página principal'),
            ('/soloylibre-admin', 'Login admin'),
            ('/api/', 'API principal'),
            ('/api/health', 'Health check'),
            ('/api/stats', 'Estadísticas'),
            ('/manifest.json', 'PWA Manifest'),
            ('/sw.js', 'Service Worker')
        ]
        
        working_urls = 0
        for url, description in urls_to_check:
            try:
                response = requests.get(f"{self.server_url}{url}", timeout=5)
                if response.status_code in [200, 302]:  # 302 para redirects
                    print(f"✅ {description}: {url}")
                    working_urls += 1
                else:
                    print(f"❌ {description}: {url} (código {response.status_code})")
                    self.errors_found.append(f"URL error {url}: {response.status_code}")
            except Exception as e:
                print(f"❌ {description}: {url} (error: {e})")
                self.errors_found.append(f"URL error {url}: {e}")
        
        print(f"\n📊 URLs funcionando: {working_urls}/{len(urls_to_check)}")
        return working_urls == len(urls_to_check)
    
    def fix_database(self):
        """Corregir problemas de base de datos"""
        print("\n🔧 CORRIGIENDO BASE DE DATOS...")
        
        try:
            # Ejecutar script de base de datos
            result = subprocess.run([
                'python3', 'soloylibre_ultimate_database.py'
            ], cwd=self.wordpress_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Base de datos corregida")
                self.fixes_applied.append("Base de datos recreada")
                return True
            else:
                print(f"❌ Error corrigiendo base de datos: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error ejecutando corrección de BD: {e}")
            return False
    
    def fix_design_system(self):
        """Corregir sistema de diseño"""
        print("\n🎨 CORRIGIENDO SISTEMA DE DISEÑO...")
        
        try:
            # Ejecutar script de diseño
            result = subprocess.run([
                'python3', 'soloylibre_design_system.py'
            ], cwd=self.wordpress_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Sistema de diseño corregido")
                self.fixes_applied.append("Sistema de diseño regenerado")
                return True
            else:
                print(f"❌ Error corrigiendo diseño: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error ejecutando corrección de diseño: {e}")
            return False
    
    def check_python_dependencies(self):
        """Verificar dependencias de Python"""
        print("\n🐍 VERIFICANDO DEPENDENCIAS PYTHON...")
        
        required_modules = [
            'sqlite3', 'json', 'hashlib', 'secrets', 'datetime',
            'http.server', 'socketserver', 'threading', 'jwt'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
                self.errors_found.append(f"Módulo faltante: {module}")
        
        if missing_modules:
            print(f"❌ Módulos faltantes: {missing_modules}")
            return False
        else:
            print("✅ Todas las dependencias están disponibles")
            return True
    
    def test_login_functionality(self):
        """Probar funcionalidad de login"""
        print("\n🔐 PROBANDO FUNCIONALIDAD DE LOGIN...")
        
        try:
            # Probar login con credenciales correctas
            login_data = {
                'log': 'josetusabe',
                'pwd': 'JoseTusabe2025!'
            }
            
            response = requests.post(
                f"{self.server_url}/soloylibre-admin",
                data=login_data,
                allow_redirects=False,
                timeout=10
            )
            
            if response.status_code == 302:  # Redirect después de login exitoso
                print("✅ Login funcionando correctamente")
                return True
            else:
                print(f"❌ Login falló con código: {response.status_code}")
                self.errors_found.append(f"Login error: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error probando login: {e}")
            self.errors_found.append(f"Login test error: {e}")
            return False
    
    def generate_debug_report(self):
        """Generar reporte de debug"""
        print("\n📋 GENERANDO REPORTE DE DEBUG...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'system': 'SoloYLibre WordPress Ultimate',
            'developer': 'Jose L Encarnacion (JoseTusabe)',
            'location': 'San José de Ocoa, República Dominicana',
            'errors_found': self.errors_found,
            'fixes_applied': self.fixes_applied,
            'total_errors': len(self.errors_found),
            'total_fixes': len(self.fixes_applied)
        }
        
        report_file = os.path.join(self.wordpress_dir, 'debug_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Reporte guardado en: {report_file}")
        return report
    
    def run_full_diagnosis(self):
        """Ejecutar diagnóstico completo"""
        self.print_header()
        
        print("🔍 INICIANDO DIAGNÓSTICO COMPLETO...")
        
        # Verificaciones
        checks = [
            ('Estructura de archivos', self.check_file_structure),
            ('Dependencias Python', self.check_python_dependencies),
            ('Base de datos', self.check_database),
            ('Estado del servidor', self.check_server_status),
            ('URLs principales', self.check_urls),
            ('Funcionalidad de login', self.test_login_functionality)
        ]
        
        passed_checks = 0
        for check_name, check_function in checks:
            try:
                if check_function():
                    passed_checks += 1
            except Exception as e:
                print(f"❌ Error en {check_name}: {e}")
                self.errors_found.append(f"{check_name}: {e}")
        
        # Aplicar correcciones si es necesario
        if self.errors_found:
            print(f"\n🔧 APLICANDO CORRECCIONES ({len(self.errors_found)} errores encontrados)...")
            
            # Corregir base de datos si hay problemas
            if any('base de datos' in error.lower() for error in self.errors_found):
                self.fix_database()
            
            # Corregir sistema de diseño si hay problemas
            if any('diseño' in error.lower() for error in self.errors_found):
                self.fix_design_system()
        
        # Generar reporte
        report = self.generate_debug_report()
        
        # Resumen final
        print("\n" + "="*80)
        print("📊 RESUMEN DEL DIAGNÓSTICO")
        print("="*80)
        print(f"✅ Verificaciones pasadas: {passed_checks}/{len(checks)}")
        print(f"❌ Errores encontrados: {len(self.errors_found)}")
        print(f"🔧 Correcciones aplicadas: {len(self.fixes_applied)}")
        
        if self.errors_found:
            print("\n❌ ERRORES ENCONTRADOS:")
            for i, error in enumerate(self.errors_found, 1):
                print(f"   {i}. {error}")
        
        if self.fixes_applied:
            print("\n✅ CORRECCIONES APLICADAS:")
            for i, fix in enumerate(self.fixes_applied, 1):
                print(f"   {i}. {fix}")
        
        if len(self.errors_found) == 0:
            print("\n🎉 ¡SISTEMA FUNCIONANDO PERFECTAMENTE!")
            print("🇩🇴 WordPress Ultimate desde San José de Ocoa está listo")
        else:
            print("\n⚠️ SISTEMA NECESITA ATENCIÓN")
            print("🔧 Revisa los errores y aplica las correcciones sugeridas")
        
        print("="*80)
        
        return report

if __name__ == '__main__':
    debugger = SoloYLibreDebugger()
    debugger.run_full_diagnosis()
