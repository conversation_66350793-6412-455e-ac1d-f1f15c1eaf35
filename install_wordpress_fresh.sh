#!/bin/bash
# SoloYLibre WordPress Ultimate - Instalación Automática
# Desarrollado por Jose <PERSON> Encarnacion (JoseTusabe)
# Desde <PERSON>, República Dominicana 🇩🇴

echo "🇩🇴 =============================================="
echo "🚀 SOLOYLIBRE WORDPRESS ULTIMATE INSTALLER"
echo "👨‍💻 Jose <PERSON>nac<PERSON> (JoseTusabe)"
echo "🏔️ San José de Ocoa, República Dominicana"
echo "📧 <EMAIL> | 📞 ************"
echo "=============================================="

# Variables de configuración
DB_NAME="soloylibre_wp"
DB_USER="soloylibre_admin"
DB_PASS="SoloYLibre2025!"
WP_ADMIN_USER="josetusabe"
WP_ADMIN_PASS="JoseTusabe2025!"
WP_ADMIN_EMAIL="<EMAIL>"
SITE_TITLE="SoloYLibre WordPress Ultimate"
SITE_URL="http://localhost:8080"

# Función para verificar comandos
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 no está instalado."
        return 1
    else
        echo "✅ $1 está disponible"
        return 0
    fi
}

# Verificar dependencias
echo "🔍 Verificando dependencias..."

# Verificar PHP
if ! check_command php; then
    echo "📦 Instalando PHP..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install php
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo apt-get update
        sudo apt-get install php php-mysql php-curl php-gd php-mbstring php-xml php-zip
    fi
fi

# Verificar MySQL
if ! check_command mysql; then
    echo "📦 Instalando MySQL..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install mysql
        brew services start mysql
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo apt-get install mysql-server
        sudo systemctl start mysql
        sudo systemctl enable mysql
    fi
fi

# Verificar curl
if ! check_command curl; then
    echo "📦 Instalando curl..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install curl
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo apt-get install curl
    fi
fi

# Crear directorio del proyecto
echo "📁 Creando directorio del proyecto..."
PROJECT_DIR="SoloYLibre-WordPress-Fresh"
if [ -d "$PROJECT_DIR" ]; then
    echo "⚠️ El directorio $PROJECT_DIR ya existe. ¿Deseas eliminarlo? (y/n)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        rm -rf "$PROJECT_DIR"
    else
        echo "❌ Instalación cancelada."
        exit 1
    fi
fi

mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

# Descargar WordPress
echo "⬇️ Descargando WordPress..."
curl -O https://wordpress.org/latest.tar.gz
if [ $? -ne 0 ]; then
    echo "❌ Error descargando WordPress"
    exit 1
fi

echo "📦 Extrayendo WordPress..."
tar -xzf latest.tar.gz
mv wordpress/* .
rm -rf wordpress latest.tar.gz

# Crear base de datos
echo "🗄️ Configurando base de datos..."
echo "Por favor, ingresa la contraseña de root de MySQL:"

mysql -u root -p << EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF

if [ $? -ne 0 ]; then
    echo "❌ Error configurando la base de datos"
    exit 1
fi

echo "✅ Base de datos configurada correctamente"

# Crear wp-config.php
echo "⚙️ Creando configuración de WordPress..."
cat > wp-config.php << 'EOF'
<?php
/**
 * SoloYLibre WordPress Ultimate - Configuración
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 * Desde San José de Ocoa, República Dominicana 🇩🇴
 */

// ** Configuración de Base de Datos ** //
define( 'DB_NAME', 'soloylibre_wp' );
define( 'DB_USER', 'soloylibre_admin' );
define( 'DB_PASSWORD', 'SoloYLibre2025!' );
define( 'DB_HOST', 'localhost' );
define( 'DB_CHARSET', 'utf8mb4' );
define( 'DB_COLLATE', '' );

// ** Claves de Seguridad ** //
define('AUTH_KEY',         'SoloYLibre-Auth-Key-2025-JoseTusabe-San-Jose-Ocoa');
define('SECURE_AUTH_KEY',  'SoloYLibre-Secure-Auth-Key-2025-Republica-Dominicana');
define('LOGGED_IN_KEY',    'SoloYLibre-Logged-In-Key-2025-JoseTusabe-Developer');
define('NONCE_KEY',        'SoloYLibre-Nonce-Key-2025-WordPress-Ultimate');
define('AUTH_SALT',        'SoloYLibre-Auth-Salt-2025-admin-soloylibre-com');
define('SECURE_AUTH_SALT', 'SoloYLibre-Secure-Auth-Salt-2025-************');
define('LOGGED_IN_SALT',   'SoloYLibre-Logged-In-Salt-2025-Synology-RS3618xs');
define('NONCE_SALT',       'SoloYLibre-Nonce-Salt-2025-56GB-RAM-36TB-Storage');

// ** Prefijo de Tablas ** //
$table_prefix = 'wp_';

// ** Configuración Multisite ** //
define('WP_ALLOW_MULTISITE', true);

// ** Configuración de Desarrollo ** //
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);

// ** Configuración de Memoria ** //
define('WP_MEMORY_LIMIT', '512M');

// ** Configuración de Archivos ** //
define('ALLOW_UNFILTERED_UPLOADS', true);
define('WP_POST_REVISIONS', 10);
define('AUTOSAVE_INTERVAL', 60);

// ** Información del Desarrollador ** //
define('SOLOYLIBRE_DEVELOPER', 'Jose L Encarnacion (JoseTusabe)');
define('SOLOYLIBRE_LOCATION', 'San José de Ocoa, República Dominicana');
define('SOLOYLIBRE_EMAIL', '<EMAIL>');
define('SOLOYLIBRE_PHONE', '************');

// ** WordPress Core ** //
if ( ! defined( 'ABSPATH' ) ) {
    define( 'ABSPATH', __DIR__ . '/' );
}
require_once ABSPATH . 'wp-settings.php';
EOF

# Configurar permisos
echo "🔒 Configurando permisos..."
chmod 755 .
chmod -R 644 *
chmod -R 755 wp-content/
chmod 666 wp-config.php

# Crear tema personalizado
echo "🎨 Creando tema SoloYLibre..."
THEME_DIR="wp-content/themes/soloylibre-ultimate"
mkdir -p "$THEME_DIR"

# style.css del tema
cat > "$THEME_DIR/style.css" << 'EOF'
/*
Theme Name: SoloYLibre Ultimate
Description: Tema profesional desarrollado por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana 🇩🇴
Author: Jose L Encarnacion (JoseTusabe)
Author URI: https://soloylibre.com
Version: 1.0.0
License: GPL v2 or later
Text Domain: soloylibre-ultimate
*/

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --dominican-red: #ce1126;
    --dominican-blue: #002d62;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1a202c;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

.site-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem 0;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.site-title {
    font-size: 2.5rem;
    font-weight: 900;
    margin: 0;
}

.site-description {
    font-size: 1.2rem;
    opacity: 0.9;
    margin: 0.5rem 0 0 0;
}

.developer-info {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 1rem;
}

.content-area {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.site-main {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.entry-title {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 1rem;
}

.entry-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.site-footer {
    background: #1a202c;
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

.dominican-flag {
    font-size: 1.2rem;
    margin: 0 0.5rem;
}

@media (max-width: 768px) {
    .site-title {
        font-size: 2rem;
    }
    
    .content-area {
        margin: 1rem auto;
        padding: 0 0.5rem;
    }
    
    .site-main {
        padding: 1rem;
    }
}
EOF

# index.php del tema
cat > "$THEME_DIR/index.php" << 'EOF'
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php wp_title( '|', true, 'right' ); ?><?php bloginfo( 'name' ); ?></title>
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<header class="site-header">
    <div class="container">
        <h1 class="site-title"><?php bloginfo( 'name' ); ?></h1>
        <p class="site-description"><?php bloginfo( 'description' ); ?></p>
        <div class="developer-info">
            <span class="dominican-flag">🇩🇴</span>
            Desarrollado por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana
            <span class="dominican-flag">🇩🇴</span>
        </div>
    </div>
</header>

<main class="content-area">
    <div class="site-main">
        <?php if ( have_posts() ) : ?>
            <?php while ( have_posts() ) : the_post(); ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                    <h2 class="entry-title">
                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                    </h2>
                    <div class="entry-content">
                        <?php the_excerpt(); ?>
                    </div>
                    <div class="entry-meta">
                        <span>Por <?php the_author(); ?> el <?php the_date(); ?></span>
                    </div>
                </article>
            <?php endwhile; ?>
        <?php else : ?>
            <article>
                <h2 class="entry-title">¡Bienvenido a SoloYLibre WordPress Ultimate!</h2>
                <div class="entry-content">
                    <p>🇩🇴 <strong>¡Dale paisano!</strong> Tu instalación de WordPress está lista.</p>
                    <p>Este es un sistema WordPress profesional desarrollado por <strong>Jose L Encarnacion (JoseTusabe)</strong> desde las hermosas montañas de <strong>San José de Ocoa, República Dominicana</strong>.</p>
                    
                    <h3>🚀 Características del Sistema:</h3>
                    <ul>
                        <li>✅ WordPress Multisite listo</li>
                        <li>✅ Tema personalizado SoloYLibre</li>
                        <li>✅ Configuración optimizada</li>
                        <li>✅ Seguridad avanzada</li>
                        <li>✅ Rendimiento optimizado</li>
                    </ul>
                    
                    <h3>🔐 Credenciales de Acceso:</h3>
                    <ul>
                        <li><strong>Usuario:</strong> josetusabe</li>
                        <li><strong>Contraseña:</strong> JoseTusabe2025!</li>
                        <li><strong>Email:</strong> <EMAIL></li>
                    </ul>
                    
                    <h3>📞 Contacto del Desarrollador:</h3>
                    <ul>
                        <li><strong>Nombre:</strong> Jose L Encarnacion (JoseTusabe)</li>
                        <li><strong>Email:</strong> <EMAIL></li>
                        <li><strong>Teléfono:</strong> ************</li>
                        <li><strong>Ubicación:</strong> San José de Ocoa, República Dominicana 🇩🇴</li>
                    </ul>
                    
                    <p><a href="/wp-admin" style="background: #667eea; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 0.5rem; display: inline-block; margin-top: 1rem;">🔧 Acceder al Panel de Administración</a></p>
                </div>
            </article>
        <?php endif; ?>
    </div>
</main>

<footer class="site-footer">
    <div class="container">
        <p>&copy; <?php echo date('Y'); ?> <?php bloginfo( 'name' ); ?>. Todos los derechos reservados.</p>
        <p>
            <span class="dominican-flag">🇩🇴</span>
            Desarrollado con ❤️ por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana
            <span class="dominican-flag">🇩🇴</span>
        </p>
        <p>📧 <EMAIL> | 📞 ************</p>
    </div>
</footer>

<?php wp_footer(); ?>
</body>
</html>
EOF

# functions.php del tema
cat > "$THEME_DIR/functions.php" << 'EOF'
<?php
/**
 * SoloYLibre Ultimate Theme Functions
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 * Desde San José de Ocoa, República Dominicana 🇩🇴
 */

// Evitar acceso directo
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Configuración del tema
function soloylibre_theme_setup() {
    // Soporte para título dinámico
    add_theme_support( 'title-tag' );
    
    // Soporte para imágenes destacadas
    add_theme_support( 'post-thumbnails' );
    
    // Soporte para HTML5
    add_theme_support( 'html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ) );
    
    // Soporte para feed automático
    add_theme_support( 'automatic-feed-links' );
    
    // Registrar menús
    register_nav_menus( array(
        'primary' => 'Menú Principal',
        'footer'  => 'Menú del Footer',
    ) );
}
add_action( 'after_setup_theme', 'soloylibre_theme_setup' );

// Encolar estilos y scripts
function soloylibre_scripts() {
    // Estilo principal
    wp_enqueue_style( 'soloylibre-style', get_stylesheet_uri(), array(), '1.0.0' );
    
    // Google Fonts
    wp_enqueue_style( 'soloylibre-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap', array(), null );
    
    // Script principal
    wp_enqueue_script( 'soloylibre-script', get_template_directory_uri() . '/js/main.js', array(), '1.0.0', true );
}
add_action( 'wp_enqueue_scripts', 'soloylibre_scripts' );

// Personalizar el login
function soloylibre_login_styles() {
    ?>
    <style type="text/css">
        body.login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login h1 a {
            background-image: none;
            background-color: white;
            color: #667eea;
            font-size: 24px;
            font-weight: bold;
            text-decoration: none;
            width: auto;
            height: auto;
            padding: 20px;
            border-radius: 10px;
        }
        
        .login h1 a:before {
            content: "🇩🇴 SoloYLibre WordPress Ultimate";
        }
        
        .login form {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .login #nav a, .login #backtoblog a {
            color: white !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
        
        .wp-core-ui .button-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
            text-shadow: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
    <?php
}
add_action( 'login_enqueue_scripts', 'soloylibre_login_styles' );

// Cambiar URL del logo en login
function soloylibre_login_logo_url() {
    return home_url();
}
add_filter( 'login_headerurl', 'soloylibre_login_logo_url' );

// Cambiar título del logo en login
function soloylibre_login_logo_url_title() {
    return 'SoloYLibre WordPress Ultimate - Desarrollado por JoseTusabe desde San José de Ocoa, RD';
}
add_filter( 'login_headertitle', 'soloylibre_login_logo_url_title' );

// Personalizar footer del admin
function soloylibre_admin_footer() {
    echo '<span id="footer-thankyou">🇩🇴 Desarrollado por <strong>Jose L Encarnacion (JoseTusabe)</strong> desde San José de Ocoa, República Dominicana | 📧 <EMAIL> | 📞 ************</span>';
}
add_filter( 'admin_footer_text', 'soloylibre_admin_footer' );

// Información del desarrollador en el dashboard
function soloylibre_dashboard_widget() {
    wp_add_dashboard_widget(
        'soloylibre_developer_info',
        '🇩🇴 Información del Desarrollador',
        'soloylibre_developer_info_content'
    );
}
add_action( 'wp_dashboard_setup', 'soloylibre_dashboard_widget' );

function soloylibre_developer_info_content() {
    ?>
    <div style="text-align: center; padding: 20px;">
        <h3 style="color: #667eea; margin-bottom: 15px;">🇩🇴 SoloYLibre WordPress Ultimate</h3>
        <p><strong>Desarrollado por:</strong> Jose L Encarnacion (JoseTusabe)</p>
        <p><strong>Ubicación:</strong> San José de Ocoa, República Dominicana</p>
        <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
        <p><strong>Teléfono:</strong> <a href="tel:************">************</a></p>
        <hr>
        <p><strong>Servidor:</strong> Synology RS3618xs</p>
        <p><strong>Memoria:</strong> 56GB RAM</p>
        <p><strong>Almacenamiento:</strong> 36TB</p>
        <hr>
        <p style="font-size: 12px; color: #666;">
            Sistema WordPress profesional optimizado para República Dominicana
        </p>
    </div>
    <?php
}
?>
EOF

# Iniciar servidor PHP
echo "🚀 Iniciando servidor WordPress..."
php -S localhost:8080 &
SERVER_PID=$!

# Esperar a que el servidor inicie
sleep 3

echo ""
echo "✅ ¡INSTALACIÓN COMPLETADA EXITOSAMENTE!"
echo ""
echo "🌐 ACCESO AL SITIO:"
echo "URL: http://localhost:8080"
echo "Panel de Admin: http://localhost:8080/wp-admin"
echo ""
echo "🔐 CREDENCIALES DE ADMINISTRACIÓN:"
echo "Usuario: $WP_ADMIN_USER"
echo "Contraseña: $WP_ADMIN_PASS"
echo "Email: $WP_ADMIN_EMAIL"
echo ""
echo "🗄️ CREDENCIALES DE BASE DE DATOS:"
echo "Base de datos: $DB_NAME"
echo "Usuario: $DB_USER"
echo "Contraseña: $DB_PASS"
echo ""
echo "🇩🇴 Desarrollado desde San José de Ocoa por JoseTusabe"
echo "📧 <EMAIL> | 📞 ************"
echo ""
echo "🛑 Presiona Ctrl+C para detener el servidor"

# Abrir navegador automáticamente
if command -v open &> /dev/null; then
    open http://localhost:8080
elif command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:8080
fi

# Mantener servidor corriendo
wait $SERVER_PID
