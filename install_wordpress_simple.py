#!/usr/bin/env python3
"""
SoloYLibre WordPress Simple Installer
Instalador automático sin dependencias complejas
Desarrollado por <PERSON>nac<PERSON> (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import shutil
import sqlite3
import json
from pathlib import Path

class SoloYLibreWordPressInstaller:
    def __init__(self):
        self.wordpress_dir = "/Users/<USER>/Desktop/SoloYLibre-WordPress"
        self.db_file = os.path.join(self.wordpress_dir, "soloylibre_wordpress.db")
        self.wp_url = "https://wordpress.org/latest.zip"
        
    def print_header(self):
        print("🇩🇴 " + "="*60)
        print("🚀 SoloYLibre WordPress Simple Installer")
        print("👨‍💻 Desarrollado por Jose L Encarnacion (JoseTusabe)")
        print("🏔️ Desde San José de <PERSON>coa, República Dominicana")
        print("📧 <EMAIL> | 📞 ************")
        print("="*60)
        
    def print_progress(self, message):
        print(f"🔄 {message}")
        
    def print_success(self, message):
        print(f"✅ {message}")
        
    def print_error(self, message):
        print(f"❌ {message}")
        
    def download_wordpress(self):
        """Descargar WordPress más reciente"""
        self.print_progress("Descargando WordPress más reciente...")
        
        wp_zip = os.path.join(self.wordpress_dir, "wordpress.zip")
        
        try:
            urllib.request.urlretrieve(self.wp_url, wp_zip)
            self.print_success("WordPress descargado exitosamente")
            return wp_zip
        except Exception as e:
            self.print_error(f"Error descargando WordPress: {e}")
            return None
            
    def extract_wordpress(self, wp_zip):
        """Extraer WordPress"""
        self.print_progress("Extrayendo WordPress...")
        
        try:
            with zipfile.ZipFile(wp_zip, 'r') as zip_ref:
                zip_ref.extractall(self.wordpress_dir)
            
            # Mover archivos de la carpeta wordpress/ al directorio principal
            wp_source = os.path.join(self.wordpress_dir, "wordpress")
            if os.path.exists(wp_source):
                for item in os.listdir(wp_source):
                    source = os.path.join(wp_source, item)
                    dest = os.path.join(self.wordpress_dir, item)
                    if os.path.exists(dest):
                        if os.path.isdir(dest):
                            shutil.rmtree(dest)
                        else:
                            os.remove(dest)
                    shutil.move(source, dest)
                
                # Eliminar carpeta wordpress vacía
                shutil.rmtree(wp_source)
            
            # Eliminar archivo zip
            os.remove(wp_zip)
            
            self.print_success("WordPress extraído exitosamente")
            return True
        except Exception as e:
            self.print_error(f"Error extrayendo WordPress: {e}")
            return False
            
    def setup_database(self):
        """Configurar base de datos SQLite"""
        self.print_progress("Configurando base de datos SQLite...")
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # Crear tablas principales de WordPress (compatibles con SQLite)
            tables = [
                """CREATE TABLE IF NOT EXISTS wp_users (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_login varchar(60) NOT NULL DEFAULT '',
                    user_pass varchar(255) NOT NULL DEFAULT '',
                    user_nicename varchar(50) NOT NULL DEFAULT '',
                    user_email varchar(100) NOT NULL DEFAULT '',
                    user_url varchar(100) NOT NULL DEFAULT '',
                    user_registered datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                    user_activation_key varchar(255) NOT NULL DEFAULT '',
                    user_status int(11) NOT NULL DEFAULT '0',
                    display_name varchar(250) NOT NULL DEFAULT ''
                )""",

                """CREATE TABLE IF NOT EXISTS wp_options (
                    option_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    option_name varchar(191) NOT NULL DEFAULT '',
                    option_value TEXT NOT NULL,
                    autoload varchar(20) NOT NULL DEFAULT 'yes'
                )""",

                """CREATE TABLE IF NOT EXISTS wp_posts (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    post_author INTEGER NOT NULL DEFAULT '0',
                    post_date datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                    post_date_gmt datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                    post_content TEXT NOT NULL,
                    post_title text NOT NULL,
                    post_excerpt text NOT NULL,
                    post_status varchar(20) NOT NULL DEFAULT 'publish',
                    comment_status varchar(20) NOT NULL DEFAULT 'open',
                    ping_status varchar(20) NOT NULL DEFAULT 'open',
                    post_password varchar(255) NOT NULL DEFAULT '',
                    post_name varchar(200) NOT NULL DEFAULT '',
                    to_ping text NOT NULL DEFAULT '',
                    pinged text NOT NULL DEFAULT '',
                    post_modified datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                    post_modified_gmt datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                    post_content_filtered TEXT NOT NULL DEFAULT '',
                    post_parent INTEGER NOT NULL DEFAULT '0',
                    guid varchar(255) NOT NULL DEFAULT '',
                    menu_order int(11) NOT NULL DEFAULT '0',
                    post_type varchar(20) NOT NULL DEFAULT 'post',
                    post_mime_type varchar(100) NOT NULL DEFAULT '',
                    comment_count INTEGER NOT NULL DEFAULT '0'
                )""",

                """CREATE TABLE IF NOT EXISTS wp_postmeta (
                    meta_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    post_id INTEGER NOT NULL DEFAULT '0',
                    meta_key varchar(255) DEFAULT NULL,
                    meta_value TEXT
                )""",

                """CREATE TABLE IF NOT EXISTS wp_comments (
                    comment_ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    comment_post_ID INTEGER NOT NULL DEFAULT '0',
                    comment_author TEXT NOT NULL,
                    comment_author_email varchar(100) NOT NULL DEFAULT '',
                    comment_author_url varchar(200) NOT NULL DEFAULT '',
                    comment_author_IP varchar(100) NOT NULL DEFAULT '',
                    comment_date datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                    comment_date_gmt datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                    comment_content text NOT NULL DEFAULT '',
                    comment_karma int(11) NOT NULL DEFAULT '0',
                    comment_approved varchar(20) NOT NULL DEFAULT '1',
                    comment_agent varchar(255) NOT NULL DEFAULT '',
                    comment_type varchar(20) NOT NULL DEFAULT '',
                    comment_parent INTEGER NOT NULL DEFAULT '0',
                    user_id INTEGER NOT NULL DEFAULT '0'
                )"""
            ]
            
            for table_sql in tables:
                cursor.execute(table_sql)
            
            # Insertar usuario administrador
            cursor.execute("""
                INSERT OR REPLACE INTO wp_users 
                (ID, user_login, user_pass, user_nicename, user_email, user_url, user_registered, display_name)
                VALUES 
                (1, 'josetusabe', '$P$BZlPX7NIx8MYpXokBW2AGsN7i.aUOt0', 'josetusabe', '<EMAIL>', 'https://soloylibre.com', datetime('now'), 'Jose L Encarnacion (JoseTusabe)')
            """)
            
            # Insertar opciones básicas
            options = [
                (1, 'siteurl', 'http://localhost:8080'),
                (2, 'home', 'http://localhost:8080'),
                (3, 'blogname', 'SoloYLibre WordPress'),
                (4, 'blogdescription', 'Desarrollo web desde San José de Ocoa, República Dominicana'),
                (5, 'admin_email', '<EMAIL>'),
                (6, 'users_can_register', '0'),
                (7, 'default_role', 'subscriber'),
                (8, 'timezone_string', 'America/Santo_Domingo'),
                (9, 'date_format', 'd/m/Y'),
                (10, 'time_format', 'H:i'),
                (11, 'start_of_week', '1'),
                (12, 'template', 'twentytwentyfour'),
                (13, 'stylesheet', 'twentytwentyfour'),
                (14, 'permalink_structure', '/%postname%/'),
                (15, 'active_plugins', 'a:0:{}'),
                (16, 'soloylibre_developer', 'Jose L Encarnacion (JoseTusabe)'),
                (17, 'soloylibre_location', 'San José de Ocoa, República Dominicana'),
                (18, 'soloylibre_email', '<EMAIL>'),
                (19, 'soloylibre_phone', '************')
            ]
            
            for option_id, option_name, option_value in options:
                cursor.execute("""
                    INSERT OR REPLACE INTO wp_options (option_id, option_name, option_value, autoload)
                    VALUES (?, ?, ?, 'yes')
                """, (option_id, option_name, option_value))
            
            # Crear post de bienvenida
            cursor.execute("""
                INSERT OR REPLACE INTO wp_posts
                (ID, post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt, post_status, post_name, post_type, to_ping, pinged, post_content_filtered)
                VALUES
                (1, 1, datetime('now'), datetime('now'),
                '<h1>¡Bienvenido a SoloYLibre WordPress!</h1>
                <p>🇩🇴 Desarrollado desde San José de Ocoa, República Dominicana</p>
                <p>👨‍💻 Por Jose L Encarnacion (JoseTusabe)</p>
                <p>📧 <EMAIL> | 📞 ************</p>
                <p>¡Dale paisano, que vamos a desarrollar algo brutal!</p>',
                'Bienvenido a SoloYLibre', '', 'publish', 'bienvenido-a-soloylibre', 'post', '', '', '')
            """)
            
            conn.commit()
            conn.close()
            
            self.print_success("Base de datos configurada exitosamente")
            return True
        except Exception as e:
            self.print_error(f"Error configurando base de datos: {e}")
            return False
            
    def create_wp_config(self):
        """Crear wp-config.php"""
        self.print_progress("Creando wp-config.php...")
        
        wp_config_content = '''<?php
/**
 * SoloYLibre WordPress Configuration
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 * Desde San José de Ocoa, República Dominicana 🇩🇴
 */

// ** Configuración de Base de Datos SQLite ** //
define('DB_NAME', 'soloylibre_wordpress');
define('DB_USER', 'admin_soloylibre');
define('DB_PASSWORD', 'JoseTusabe2025!SanJoseDeOcoa');
define('DB_HOST', 'localhost');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// ** Claves de Seguridad ** //
define('AUTH_KEY',         'SanJoseDeOcoa2025!SoloYLibreWebDev#JoseTusabe$RepublicaDominicana');
define('SECURE_AUTH_KEY',  'MontagnasDominicanas!TecnologiaRD#InnovacionCaribe$OrgulloDominicano');
define('LOGGED_IN_KEY',    'KlkManito!DaleQueVamos#TechDominicano$SoloYLibreAI2025');
define('NONCE_KEY',        'VacanoTigueraje!BrutalPaisano#TecnologiaOcoa$DominicanPride');
define('AUTH_SALT',        'MerengueBachata!ColmadoSancocho#PlátanoYuca$MalecónPlaya');
define('SECURE_AUTH_SALT', 'CaribeTech!InnovaciónRD#SoloYLibreDev$JoseTusabeGenius');
define('LOGGED_IN_SALT',   'OcoaMountains!DominicanSpirit#TechInnovation$CaribbeanPower');
define('NONCE_SALT',       'RepublicaDominicana!SanJoseDeOcoa#SoloYLibreEcosystem$TechPride');

// ** Configuración de Desarrollo ** //
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);

// ** Configuración de Memoria ** //
define('WP_MEMORY_LIMIT', '512M');

// ** URLs ** //
define('WP_HOME', 'http://localhost:8080');
define('WP_SITEURL', 'http://localhost:8080');

// ** Configuración SoloYLibre ** //
define('SOLOYLIBRE_DEVELOPER', 'Jose L Encarnacion (JoseTusabe)');
define('SOLOYLIBRE_LOCATION', 'San José de Ocoa, República Dominicana');
define('SOLOYLIBRE_EMAIL', '<EMAIL>');
define('SOLOYLIBRE_PHONE', '************');

// ** Tabla de WordPress ** //
$table_prefix = 'wp_';

if ( !defined('ABSPATH') )
    define('ABSPATH', dirname(__FILE__) . '/');

require_once(ABSPATH . 'wp-settings.php');
'''
        
        try:
            wp_config_path = os.path.join(self.wordpress_dir, "wp-config.php")
            with open(wp_config_path, 'w', encoding='utf-8') as f:
                f.write(wp_config_content)
            
            self.print_success("wp-config.php creado exitosamente")
            return True
        except Exception as e:
            self.print_error(f"Error creando wp-config.php: {e}")
            return False
            
    def install(self):
        """Ejecutar instalación completa"""
        self.print_header()
        
        # Crear directorio si no existe
        os.makedirs(self.wordpress_dir, exist_ok=True)
        os.chdir(self.wordpress_dir)
        
        # Descargar WordPress
        wp_zip = self.download_wordpress()
        if not wp_zip:
            return False
            
        # Extraer WordPress
        if not self.extract_wordpress(wp_zip):
            return False
            
        # Configurar base de datos
        if not self.setup_database():
            return False
            
        # Crear wp-config.php
        if not self.create_wp_config():
            return False
            
        self.print_success("¡WordPress instalado completamente!")
        
        print("\n🎉 " + "="*50)
        print("✅ SoloYLibre WordPress está listo para usar")
        print("="*50)
        print("\n🔐 CREDENCIALES DE ACCESO:")
        print("🌐 URL: http://localhost:8080")
        print("👤 Usuario: josetusabe")
        print("🔑 Contraseña: JoseTusabe2025!")
        print("📧 Email: <EMAIL>")
        print("\n🚀 Para iniciar el servidor:")
        print("python3 -m http.server 8080")
        print("\n¡Dale paisano, que está brutal! 🇩🇴")
        print("="*50)
        
        return True

if __name__ == '__main__':
    installer = SoloYLibreWordPressInstaller()
    installer.install()
