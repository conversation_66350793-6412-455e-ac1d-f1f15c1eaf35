#!/bin/bash
# MEJORAS AVANZADAS - WordPress SoloYLibre Ultimate
# Desarrollado por Jose L Encarnacion (JoseTusabe)
# Desde <PERSON>, República Dominicana 🇩🇴

echo "🇩🇴 =============================================="
echo "🚀 MEJORAS AVANZADAS SOLOYLIBRE ULTIMATE"
echo "👨‍💻 Jose L Encarnacion (JoseTusabe)"
echo "🏔️ San José de Ocoa, República Dominicana"
echo "📧 <EMAIL> | 📞 ************"
echo "=============================================="

# Variables
PROJECT_DIR="WordPress-SoloYLibre-Final-Simple"
ADVANCED_DIR="SoloYLibre-Advanced-Features"

echo ""
echo "🔍 Verificando instalación base..."

if [ ! -d "$PROJECT_DIR" ]; then
    echo "❌ WordPress base no encontrado"
    echo "💡 Ejecuta primero la instalación base"
    exit 1
fi

echo "✅ WordPress base encontrado"

echo ""
echo "🚀 Creando mejoras avanzadas..."

# Crear directorio para mejoras
mkdir -p "$ADVANCED_DIR"
cd "$PROJECT_DIR"

echo ""
echo "🔌 Creando plugin SoloYLibre Ultimate..."

# Crear directorio del plugin
PLUGIN_DIR="wp-content/plugins/soloylibre-ultimate"
mkdir -p "$PLUGIN_DIR"

# Plugin principal
cat > "$PLUGIN_DIR/soloylibre-ultimate.php" << 'EOF'
<?php
/**
 * Plugin Name: SoloYLibre Ultimate
 * Description: Plugin avanzado desarrollado por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana 🇩🇴
 * Version: 1.0.0
 * Author: Jose L Encarnacion (JoseTusabe)
 * Author URI: https://soloylibre.com
 * Text Domain: soloylibre-ultimate
 */

// Evitar acceso directo
if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibreUltimate {

    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('wp_dashboard_setup', array($this, 'dashboard_widgets'));
        add_shortcode('soloylibre_info', array($this, 'info_shortcode'));
        add_shortcode('dominican_flag', array($this, 'flag_shortcode'));
    }

    public function init() {
        // Registrar post type personalizado
        $this->register_custom_post_types();

        // Agregar soporte para características adicionales
        add_theme_support('custom-background');
        add_theme_support('custom-header');
    }

    public function enqueue_scripts() {
        wp_enqueue_style(
            'soloylibre-ultimate-style',
            plugin_dir_url(__FILE__) . 'assets/style.css',
            array(),
            '1.0.0'
        );

        wp_enqueue_script(
            'soloylibre-ultimate-script',
            plugin_dir_url(__FILE__) . 'assets/script.js',
            array('jquery'),
            '1.0.0',
            true
        );
    }

    public function admin_menu() {
        add_menu_page(
            'SoloYLibre Ultimate',
            'SoloYLibre 🇩🇴',
            'manage_options',
            'soloylibre-ultimate',
            array($this, 'admin_page'),
            'dashicons-flag',
            30
        );
    }

    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>🇩🇴 SoloYLibre Ultimate</h1>
            <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h2>Desarrollado desde San José de Ocoa</h2>
                <p><strong>Jose L Encarnacion (JoseTusabe)</strong></p>
                <p>📧 <EMAIL> | 📞 ************</p>
                <p>🖥️ Synology RS3618xs - 56GB RAM - 36TB</p>
            </div>

            <div class="soloylibre-features">
                <h3>🚀 Características Avanzadas</h3>
                <ul>
                    <li>✅ Tema dominicano personalizado</li>
                    <li>✅ Widgets personalizados</li>
                    <li>✅ Shortcodes únicos</li>
                    <li>✅ Optimización automática</li>
                    <li>✅ Seguridad avanzada</li>
                </ul>
            </div>

            <div class="soloylibre-stats">
                <h3>📊 Estadísticas del Sistema</h3>
                <p><strong>Versión WordPress:</strong> <?php echo get_bloginfo('version'); ?></p>
                <p><strong>Tema Activo:</strong> <?php echo wp_get_theme()->get('Name'); ?></p>
                <p><strong>Plugins Activos:</strong> <?php echo count(get_option('active_plugins')); ?></p>
                <p><strong>Memoria PHP:</strong> <?php echo ini_get('memory_limit'); ?></p>
            </div>
        </div>

        <style>
            .soloylibre-features, .soloylibre-stats {
                background: white;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .soloylibre-features ul {
                list-style: none;
                padding: 0;
            }
            .soloylibre-features li {
                padding: 5px 0;
                border-bottom: 1px solid #eee;
            }
        </style>
        <?php
    }

    public function dashboard_widgets() {
        wp_add_dashboard_widget(
            'soloylibre_ultimate_widget',
            '🇩🇴 SoloYLibre Ultimate - Estado del Sistema',
            array($this, 'dashboard_widget_content')
        );
    }

    public function dashboard_widget_content() {
        ?>
        <div style="text-align: center; padding: 15px; background: linear-gradient(135deg, #22c55e, #16a34a); color: white; border-radius: 10px; margin: -12px;">
            <h3>🎉 ¡Sistema Funcionando Perfectamente!</h3>
            <p><strong>WordPress SoloYLibre Ultimate</strong></p>
            <p>Desarrollado desde San José de Ocoa, República Dominicana 🇩🇴</p>
            <hr style="border-color: rgba(255,255,255,0.3);">
            <p><strong>Jose L Encarnacion (JoseTusabe)</strong></p>
            <p>📧 <EMAIL> | 📞 ************</p>
        </div>
        <?php
    }

    public function register_custom_post_types() {
        // Post type para proyectos
        register_post_type('proyectos_rd', array(
            'labels' => array(
                'name' => 'Proyectos RD',
                'singular_name' => 'Proyecto RD',
                'add_new' => 'Añadir Proyecto',
                'add_new_item' => 'Añadir Nuevo Proyecto',
                'edit_item' => 'Editar Proyecto',
                'new_item' => 'Nuevo Proyecto',
                'view_item' => 'Ver Proyecto',
                'search_items' => 'Buscar Proyectos',
                'not_found' => 'No se encontraron proyectos',
                'not_found_in_trash' => 'No hay proyectos en la papelera'
            ),
            'public' => true,
            'has_archive' => true,
            'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
            'menu_icon' => 'dashicons-flag'
        ));
    }

    public function info_shortcode($atts) {
        $atts = shortcode_atts(array(
            'tipo' => 'completo'
        ), $atts);

        if ($atts['tipo'] === 'desarrollador') {
            return '<div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;">
                <h3>🇩🇴 Jose L Encarnacion (JoseTusabe)</h3>
                <p>📧 <EMAIL> | 📞 ************</p>
                <p>🏔️ San José de Ocoa, República Dominicana</p>
            </div>';
        }

        return '<div style="background: #f0f9ff; padding: 20px; border-radius: 10px; border-left: 5px solid #0284c7; margin: 20px 0;">
            <h3>🇩🇴 WordPress SoloYLibre Ultimate</h3>
            <p>Sistema desarrollado con amor dominicano desde San José de Ocoa</p>
            <p><strong>Desarrollador:</strong> Jose L Encarnacion (JoseTusabe)</p>
            <p><strong>Contacto:</strong> <EMAIL> | ************</p>
        </div>';
    }

    public function flag_shortcode($atts) {
        return '<span style="font-size: 1.5em; margin: 0 5px; animation: wave 2s ease-in-out infinite;">🇩🇴</span>';
    }
}

// Inicializar plugin
new SoloYLibreUltimate();

// Función para activación del plugin
register_activation_hook(__FILE__, 'soloylibre_ultimate_activate');
function soloylibre_ultimate_activate() {
    // Crear páginas por defecto
    $pages = array(
        'Sobre San José de Ocoa' => 'Información sobre la hermosa provincia de San José de Ocoa, República Dominicana.',
        'Contacto Desarrollador' => 'Información de contacto de Jose L Encarnacion (JoseTusabe).'
    );

    foreach ($pages as $title => $content) {
        $page = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => 'publish',
            'post_type' => 'page'
        );

        if (!get_page_by_title($title)) {
            wp_insert_post($page);
        }
    }

    // Flush rewrite rules
    flush_rewrite_rules();
}
?>
EOF

echo "✅ Plugin SoloYLibre Ultimate creado"

echo ""
echo "🎨 Creando assets del plugin..."

# Crear directorio de assets
mkdir -p "$PLUGIN_DIR/assets"

# CSS del plugin
cat > "$PLUGIN_DIR/assets/style.css" << 'EOF'
/* SoloYLibre Ultimate Plugin Styles */
.soloylibre-widget {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin: 20px 0;
    text-align: center;
}

.dominican-flag {
    font-size: 1.2rem;
    margin: 0 5px;
    animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
}

.soloylibre-info-box {
    background: #f0f9ff;
    border-left: 5px solid #0284c7;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}

.soloylibre-developer-box {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin: 20px 0;
}

.soloylibre-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.soloylibre-stat-item {
    background: white;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-top: 3px solid #667eea;
}

.soloylibre-stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.soloylibre-stat-label {
    color: #666;
    font-size: 0.9rem;
}
EOF

# JavaScript del plugin
cat > "$PLUGIN_DIR/assets/script.js" << 'EOF'
// SoloYLibre Ultimate Plugin JavaScript
jQuery(document).ready(function($) {

    // Animación de la bandera dominicana
    $('.dominican-flag').hover(
        function() {
            $(this).css('animation-duration', '0.5s');
        },
        function() {
            $(this).css('animation-duration', '2s');
        }
    );

    // Mensaje de bienvenida
    if (typeof pagenow !== 'undefined' && pagenow === 'dashboard') {
        setTimeout(function() {
            if ($('#soloylibre_ultimate_widget').length) {
                $('#soloylibre_ultimate_widget').effect('highlight', {color: '#22c55e'}, 2000);
            }
        }, 1000);
    }

    // Contador de visitas (demo)
    var visitCount = localStorage.getItem('soloylibre_visits') || 0;
    visitCount++;
    localStorage.setItem('soloylibre_visits', visitCount);

    // Mostrar estadísticas si existe el elemento
    if ($('.soloylibre-visit-count').length) {
        $('.soloylibre-visit-count').text(visitCount);
    }

    // Efecto de typing para el título
    function typeWriter(element, text, speed) {
        var i = 0;
        element.innerHTML = '';

        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }

        type();
    }

    // Aplicar efecto typing al título principal
    var mainTitle = $('.soloylibre-typing-title')[0];
    if (mainTitle) {
        var originalText = mainTitle.innerHTML;
        typeWriter(mainTitle, originalText, 100);
    }
});

// Función para mostrar información del desarrollador
function mostrarInfoDesarrollador() {
    alert('🇩🇴 Jose L Encarnacion (JoseTusabe)\n' +
          '📧 <EMAIL>\n' +
          '📞 ************\n' +
          '🏔️ San José de Ocoa, República Dominicana\n' +
          '🖥️ Synology RS3618xs - 56GB RAM - 36TB');
}

// Función para copiar información de contacto
function copiarContacto() {
    var contacto = 'Jose L Encarnacion (JoseTusabe)\n' +
                   'Email: <EMAIL>\n' +
                   'Teléfono: ************\n' +
                   'Ubicación: San José de Ocoa, República Dominicana';

    navigator.clipboard.writeText(contacto).then(function() {
        alert('📋 Información de contacto copiada al portapapeles');
    });
}
EOF

echo "✅ Assets del plugin creados"

echo ""
echo "🔧 Creando widget personalizado..."

# Widget personalizado
cat > "$PLUGIN_DIR/widget-soloylibre.php" << 'EOF'
<?php
/**
 * Widget SoloYLibre Ultimate
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 */

class SoloYLibre_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'soloylibre_widget',
            '🇩🇴 SoloYLibre Info',
            array('description' => 'Widget con información del desarrollador dominicano')
        );
    }

    public function widget($args, $instance) {
        echo $args['before_widget'];

        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        ?>
        <div class="soloylibre-widget">
            <h4>🇩🇴 Desarrollado desde San José de Ocoa</h4>
            <p><strong>Jose L Encarnacion (JoseTusabe)</strong></p>
            <p>📧 <a href="mailto:<EMAIL>" style="color: #fbbf24;"><EMAIL></a></p>
            <p>📞 <a href="tel:************" style="color: #fbbf24;">************</a></p>
            <p>🏔️ San José de Ocoa, República Dominicana</p>
            <hr style="border-color: rgba(255,255,255,0.3);">
            <p style="font-size: 0.9em; opacity: 0.9;">
                🖥️ Synology RS3618xs - 56GB RAM - 36TB<br>
                ✅ WordPress SoloYLibre Ultimate
            </p>
        </div>
        <?php

        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'SoloYLibre Info';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>">Título:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>"
                   name="<?php echo $this->get_field_name('title'); ?>" type="text"
                   value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? strip_tags($new_instance['title']) : '';
        return $instance;
    }
}

// Registrar widget
function register_soloylibre_widget() {
    register_widget('SoloYLibre_Widget');
}
add_action('widgets_init', 'register_soloylibre_widget');
?>
EOF

echo "✅ Widget personalizado creado"

echo ""
echo "📊 Creando sistema de estadísticas..."

# Sistema de estadísticas
cat > "$PLUGIN_DIR/estadisticas.php" << 'EOF'
<?php
/**
 * Sistema de Estadísticas SoloYLibre
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 */

class SoloYLibre_Stats {

    public function __construct() {
        add_action('wp_footer', array($this, 'track_visit'));
        add_action('wp_ajax_get_stats', array($this, 'get_stats'));
        add_action('wp_ajax_nopriv_get_stats', array($this, 'get_stats'));
    }

    public function track_visit() {
        if (!is_admin()) {
            $visits = get_option('soloylibre_visits', 0);
            $visits++;
            update_option('soloylibre_visits', $visits);

            // Registrar visita por día
            $today = date('Y-m-d');
            $daily_visits = get_option('soloylibre_daily_visits', array());

            if (!isset($daily_visits[$today])) {
                $daily_visits[$today] = 0;
            }

            $daily_visits[$today]++;
            update_option('soloylibre_daily_visits', $daily_visits);
        }
    }

    public function get_stats() {
        $stats = array(
            'total_visits' => get_option('soloylibre_visits', 0),
            'posts_count' => wp_count_posts()->publish,
            'pages_count' => wp_count_posts('page')->publish,
            'comments_count' => wp_count_comments()->approved,
            'users_count' => count_users()['total_users'],
            'plugins_count' => count(get_option('active_plugins', array())),
            'theme_name' => wp_get_theme()->get('Name'),
            'wp_version' => get_bloginfo('version'),
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'developer_info' => array(
                'name' => 'Jose L Encarnacion (JoseTusabe)',
                'email' => '<EMAIL>',
                'phone' => '************',
                'location' => 'San José de Ocoa, República Dominicana 🇩🇴',
                'server' => 'Synology RS3618xs - 56GB RAM - 36TB'
            )
        );

        wp_send_json_success($stats);
    }

    public function display_stats_widget() {
        $stats = array(
            'Visitas Totales' => get_option('soloylibre_visits', 0),
            'Posts Publicados' => wp_count_posts()->publish,
            'Páginas Creadas' => wp_count_posts('page')->publish,
            'Comentarios' => wp_count_comments()->approved,
            'Usuarios' => count_users()['total_users'],
            'Plugins Activos' => count(get_option('active_plugins', array()))
        );

        echo '<div class="soloylibre-stats">';
        foreach ($stats as $label => $value) {
            echo '<div class="soloylibre-stat-item">';
            echo '<div class="soloylibre-stat-number">' . $value . '</div>';
            echo '<div class="soloylibre-stat-label">' . $label . '</div>';
            echo '</div>';
        }
        echo '</div>';
    }
}

new SoloYLibre_Stats();
?>
EOF

echo "✅ Sistema de estadísticas creado"

echo ""
echo "🛡️ Creando sistema de seguridad avanzado..."

# Sistema de seguridad
cat > "$PLUGIN_DIR/seguridad.php" << 'EOF'
<?php
/**
 * Sistema de Seguridad SoloYLibre
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 */

class SoloYLibre_Security {

    public function __construct() {
        add_action('init', array($this, 'init_security'));
        add_action('wp_login_failed', array($this, 'log_failed_login'));
        add_filter('login_headerurl', array($this, 'custom_login_url'));
        add_filter('login_headertitle', array($this, 'custom_login_title'));
    }

    public function init_security() {
        // Ocultar versión de WordPress
        remove_action('wp_head', 'wp_generator');

        // Deshabilitar XML-RPC si no se necesita
        add_filter('xmlrpc_enabled', '__return_false');

        // Agregar headers de seguridad
        add_action('send_headers', array($this, 'add_security_headers'));

        // Limitar intentos de login
        $this->limit_login_attempts();
    }

    public function add_security_headers() {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }

    public function limit_login_attempts() {
        $ip = $_SERVER['REMOTE_ADDR'];
        $attempts = get_transient('login_attempts_' . $ip);

        if ($attempts && $attempts >= 5) {
            wp_die('Demasiados intentos de login. Intenta de nuevo en 15 minutos.');
        }
    }

    public function log_failed_login($username) {
        $ip = $_SERVER['REMOTE_ADDR'];
        $attempts = get_transient('login_attempts_' . $ip) ?: 0;
        $attempts++;

        set_transient('login_attempts_' . $ip, $attempts, 15 * MINUTE_IN_SECONDS);

        // Log del intento fallido
        error_log("Login fallido para usuario: $username desde IP: $ip");
    }

    public function custom_login_url() {
        return home_url();
    }

    public function custom_login_title() {
        return 'SoloYLibre WordPress Ultimate - Desarrollado desde San José de Ocoa 🇩🇴';
    }

    public function get_security_status() {
        $status = array(
            'wp_version_hidden' => !has_action('wp_head', 'wp_generator'),
            'xmlrpc_disabled' => !apply_filters('xmlrpc_enabled', true),
            'security_headers' => true,
            'login_protection' => true,
            'developer_signature' => 'Jose L Encarnacion (JoseTusabe) - San José de Ocoa 🇩🇴'
        );

        return $status;
    }
}

new SoloYLibre_Security();
?>
EOF

echo "✅ Sistema de seguridad avanzado creado"

echo ""
echo "🚀 Creando optimizaciones de rendimiento..."

# Optimizaciones de rendimiento
cat > "$PLUGIN_DIR/optimizacion.php" << 'EOF'
<?php
/**
 * Optimizaciones de Rendimiento SoloYLibre
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 */

class SoloYLibre_Performance {

    public function __construct() {
        add_action('init', array($this, 'init_optimizations'));
        add_action('wp_enqueue_scripts', array($this, 'optimize_scripts'));
        add_filter('script_loader_tag', array($this, 'add_async_defer'), 10, 2);
    }

    public function init_optimizations() {
        // Habilitar compresión GZIP
        if (!ob_get_level()) {
            ob_start('ob_gzhandler');
        }

        // Optimizar consultas de base de datos
        add_action('pre_get_posts', array($this, 'optimize_queries'));

        // Limpiar head de WordPress
        $this->clean_wp_head();

        // Habilitar cache de objetos
        if (!wp_using_ext_object_cache()) {
            wp_cache_init();
        }
    }

    public function clean_wp_head() {
        // Remover enlaces innecesarios del head
        remove_action('wp_head', 'rsd_link');
        remove_action('wp_head', 'wlwmanifest_link');
        remove_action('wp_head', 'wp_shortlink_wp_head');
        remove_action('wp_head', 'adjacent_posts_rel_link_wp_head');

        // Remover emoji scripts
        remove_action('wp_head', 'print_emoji_detection_script', 7);
        remove_action('wp_print_styles', 'print_emoji_styles');
    }

    public function optimize_scripts() {
        // Mover jQuery al footer
        if (!is_admin()) {
            wp_deregister_script('jquery');
            wp_register_script('jquery', includes_url('/js/jquery/jquery.min.js'), false, null, true);
            wp_enqueue_script('jquery');
        }
    }

    public function add_async_defer($tag, $handle) {
        // Scripts que deben cargarse de forma asíncrona
        $async_scripts = array('soloylibre-ultimate-script');

        if (in_array($handle, $async_scripts)) {
            return str_replace('<script ', '<script async defer ', $tag);
        }

        return $tag;
    }

    public function optimize_queries($query) {
        if (!is_admin() && $query->is_main_query()) {
            // Limitar posts en home
            if ($query->is_home()) {
                $query->set('posts_per_page', 10);
            }

            // Optimizar búsquedas
            if ($query->is_search()) {
                $query->set('posts_per_page', 5);
            }
        }
    }

    public function get_performance_stats() {
        $stats = array(
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => ini_get('memory_limit'),
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
            'queries_count' => get_num_queries(),
            'cache_enabled' => wp_using_ext_object_cache(),
            'gzip_enabled' => extension_loaded('zlib'),
            'optimized_by' => 'Jose L Encarnacion (JoseTusabe) - San José de Ocoa 🇩🇴'
        );

        return $stats;
    }
}

new SoloYLibre_Performance();
?>
EOF

echo "✅ Optimizaciones de rendimiento creadas"

echo ""
echo "📱 Creando funcionalidades móviles..."

# Funcionalidades móviles
cat > "$PLUGIN_DIR/mobile.php" << 'EOF'
<?php
/**
 * Funcionalidades Móviles SoloYLibre
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 */

class SoloYLibre_Mobile {

    public function __construct() {
        add_action('wp_head', array($this, 'add_mobile_meta'));
        add_action('wp_enqueue_scripts', array($this, 'mobile_styles'));
        add_action('init', array($this, 'mobile_optimizations'));
    }

    public function add_mobile_meta() {
        echo '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">';
        echo '<meta name="mobile-web-app-capable" content="yes">';
        echo '<meta name="apple-mobile-web-app-capable" content="yes">';
        echo '<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">';
        echo '<meta name="theme-color" content="#667eea">';

        // PWA manifest
        echo '<link rel="manifest" href="' . plugin_dir_url(__FILE__) . 'manifest.json">';
    }

    public function mobile_styles() {
        if (wp_is_mobile()) {
            wp_enqueue_style(
                'soloylibre-mobile',
                plugin_dir_url(__FILE__) . 'assets/mobile.css',
                array(),
                '1.0.0'
            );
        }
    }

    public function mobile_optimizations() {
        if (wp_is_mobile()) {
            // Reducir calidad de imágenes en móvil
            add_filter('jpeg_quality', function() { return 75; });

            // Lazy loading para imágenes
            add_filter('wp_get_attachment_image_attributes', array($this, 'add_lazy_loading'));
        }
    }

    public function add_lazy_loading($attr) {
        $attr['loading'] = 'lazy';
        return $attr;
    }
}

new SoloYLibre_Mobile();
?>
EOF

# Crear manifest.json para PWA
cat > "$PLUGIN_DIR/manifest.json" << 'EOF'
{
    "name": "SoloYLibre WordPress Ultimate",
    "short_name": "SoloYLibre",
    "description": "WordPress desarrollado desde San José de Ocoa, República Dominicana",
    "start_url": "/",
    "display": "standalone",
    "background_color": "#667eea",
    "theme_color": "#667eea",
    "orientation": "portrait-primary",
    "icons": [
        {
            "src": "assets/icon-192.png",
            "sizes": "192x192",
            "type": "image/png"
        },
        {
            "src": "assets/icon-512.png",
            "sizes": "512x512",
            "type": "image/png"
        }
    ],
    "developer": {
        "name": "Jose L Encarnacion (JoseTusabe)",
        "email": "<EMAIL>",
        "location": "San José de Ocoa, República Dominicana"
    }
}
EOF

# CSS móvil
cat > "$PLUGIN_DIR/assets/mobile.css" << 'EOF'
/* Estilos móviles SoloYLibre */
@media (max-width: 768px) {
    .site-header {
        padding: 1rem 0;
    }

    .site-title {
        font-size: 1.8rem !important;
    }

    .site-main {
        padding: 1rem;
        margin: 1rem;
    }

    .soloylibre-widget {
        padding: 15px;
        margin: 15px 0;
    }

    .soloylibre-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .dominican-flag {
        font-size: 1rem;
    }

    /* Botones táctiles */
    .btn {
        min-height: 44px;
        padding: 12px 20px;
        font-size: 16px;
    }

    /* Navegación móvil */
    .mobile-menu-toggle {
        display: block;
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
    }

    /* Optimizaciones de rendimiento móvil */
    img {
        max-width: 100%;
        height: auto;
    }

    .lazy {
        opacity: 0;
        transition: opacity 0.3s;
    }

    .lazy.loaded {
        opacity: 1;
    }
}

/* Modo oscuro para móviles */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
    body {
        background: #1a202c;
        color: #e2e8f0;
    }

    .site-main {
        background: #2d3748;
    }
}
EOF

echo "✅ Funcionalidades móviles creadas"

echo ""
echo "🔗 Incluyendo archivos en el plugin principal..."

# Incluir todos los archivos en el plugin principal
cat >> "$PLUGIN_DIR/soloylibre-ultimate.php" << 'EOF'

// Incluir archivos adicionales
require_once plugin_dir_path(__FILE__) . 'widget-soloylibre.php';
require_once plugin_dir_path(__FILE__) . 'estadisticas.php';
require_once plugin_dir_path(__FILE__) . 'seguridad.php';
require_once plugin_dir_path(__FILE__) . 'optimizacion.php';
require_once plugin_dir_path(__FILE__) . 'mobile.php';

// Agregar página de configuración avanzada
add_action('admin_menu', 'soloylibre_advanced_menu');
function soloylibre_advanced_menu() {
    add_submenu_page(
        'soloylibre-ultimate',
        'Configuración Avanzada',
        'Configuración Avanzada',
        'manage_options',
        'soloylibre-advanced',
        'soloylibre_advanced_page'
    );
}

function soloylibre_advanced_page() {
    $stats = new SoloYLibre_Stats();
    $security = new SoloYLibre_Security();
    $performance = new SoloYLibre_Performance();
    ?>
    <div class="wrap">
        <h1>🇩🇴 SoloYLibre Ultimate - Configuración Avanzada</h1>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">

            <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3>📊 Estadísticas del Sistema</h3>
                <?php $stats->display_stats_widget(); ?>
            </div>

            <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3>🛡️ Estado de Seguridad</h3>
                <?php
                $security_status = $security->get_security_status();
                foreach ($security_status as $key => $value) {
                    if ($key !== 'developer_signature') {
                        $icon = $value ? '✅' : '❌';
                        echo "<p>$icon " . ucfirst(str_replace('_', ' ', $key)) . "</p>";
                    }
                }
                ?>
                <p><strong>🇩🇴 <?php echo $security_status['developer_signature']; ?></strong></p>
            </div>

            <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3>🚀 Rendimiento</h3>
                <?php
                $perf_stats = $performance->get_performance_stats();
                echo "<p><strong>Memoria usada:</strong> " . size_format($perf_stats['memory_usage']) . "</p>";
                echo "<p><strong>Tiempo de ejecución:</strong> " . round($perf_stats['execution_time'], 3) . "s</p>";
                echo "<p><strong>Consultas DB:</strong> " . $perf_stats['queries_count'] . "</p>";
                echo "<p><strong>Cache:</strong> " . ($perf_stats['cache_enabled'] ? 'Habilitado' : 'Deshabilitado') . "</p>";
                ?>
                <p><strong>🇩🇴 <?php echo $perf_stats['optimized_by']; ?></strong></p>
            </div>

        </div>

        <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 30px; border-radius: 15px; text-align: center; margin: 30px 0;">
            <h2>🇩🇴 WordPress SoloYLibre Ultimate</h2>
            <p><strong>Sistema completo desarrollado desde San José de Ocoa, República Dominicana</strong></p>
            <hr style="border-color: rgba(255,255,255,0.3); margin: 20px 0;">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                <div>
                    <h4>👨‍💻 Desarrollador</h4>
                    <p>Jose L Encarnacion (JoseTusabe)</p>
                </div>
                <div>
                    <h4>📧 Contacto</h4>
                    <p><EMAIL></p>
                </div>
                <div>
                    <h4>📞 Teléfono</h4>
                    <p>************</p>
                </div>
                <div>
                    <h4>🖥️ Servidor</h4>
                    <p>Synology RS3618xs</p>
                </div>
            </div>
            <p style="margin-top: 20px; font-size: 1.1em;">
                <strong>¡Que viva San José de Ocoa y que viva la República Dominicana!</strong> 🇩🇴
            </p>
        </div>
    </div>
    <?php
}
EOF

echo "✅ Plugin completo integrado"

echo ""
echo "🔒 Configurando permisos finales..."

# Configurar permisos
chmod -R 755 "$PLUGIN_DIR"
chmod 644 "$PLUGIN_DIR"/*.php
chmod 644 "$PLUGIN_DIR"/assets/*

echo "✅ Permisos configurados"

echo ""
echo "🇩🇴 =============================================="
echo "🎉 ¡MEJORAS AVANZADAS COMPLETADAS!"
echo "=============================================="
echo ""
echo "✅ NUEVAS CARACTERÍSTICAS AÑADIDAS:"
echo "   🔌 Plugin SoloYLibre Ultimate completo"
echo "   📊 Sistema de estadísticas avanzado"
echo "   🛡️ Seguridad mejorada"
echo "   🚀 Optimizaciones de rendimiento"
echo "   📱 Funcionalidades móviles y PWA"
echo "   🎨 Widget personalizado dominicano"
echo "   📋 Panel de configuración avanzada"
echo ""
echo "🌐 ACCESO A NUEVAS FUNCIONES:"
echo "   Plugin: wp-admin/admin.php?page=soloylibre-ultimate"
echo "   Configuración: wp-admin/admin.php?page=soloylibre-advanced"
echo "   Widgets: wp-admin/widgets.php"
echo ""
echo "🇩🇴 DESARROLLADO DESDE SAN JOSÉ DE OCOA:"
echo "   👨‍💻 Jose L Encarnacion (JoseTusabe)"
echo "   📧 <EMAIL>"
echo "   📞 ************"
echo ""
echo "🎯 TU WORDPRESS AHORA ES NIVEL EMPRESARIAL!"
echo "=============================================="