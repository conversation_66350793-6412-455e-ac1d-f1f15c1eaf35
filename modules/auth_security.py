#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - Authentication & Security Module
Módulo de autenticación y seguridad avanzada
Desarrollado por Jose <PERSON>nacion (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import hashlib
import secrets
import time
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import re

# Simulación de pyotp para evitar dependencias externas
class MockTOTP:
    def __init__(self, secret):
        self.secret = secret

    def verify(self, token, valid_window=1):
        # Simulación simple - en producción usar pyotp real
        return token == "123456"

    def provisioning_uri(self, name, issuer_name):
        return f"otpauth://totp/{issuer_name}:{name}?secret={self.secret}&issuer={issuer_name}"

def random_base32():
    return secrets.token_hex(16)

class AuthenticationManager:
    """Gestor de autenticación avanzada"""
    
    def __init__(self, core_system):
        self.core = core_system
        self.max_login_attempts = 5
        self.lockout_duration = 30  # minutos
        self.password_min_length = 8
        self.session_timeout = 24  # horas
        
    def authenticate_user(self, username: str, password: str, ip_address: str = None) -> Optional[Dict[str, Any]]:
        """Autenticar usuario con seguridad avanzada"""
        try:
            # Verificar si el usuario está bloqueado
            if self.is_user_locked(username, ip_address):
                self.core.log_warning(f"Login attempt on locked account: {username}", {'ip': ip_address})
                return None
            
            # Incrementar intentos de login
            self.increment_login_attempts(username, ip_address)
            
            # Buscar usuario en la base de datos
            user_data = self.get_user_by_username(username)
            if not user_data:
                self.core.log_warning(f"Login attempt with non-existent user: {username}", {'ip': ip_address})
                return None
            
            # Verificar contraseña
            if not self.verify_password(password, user_data['user_pass']):
                self.core.log_warning(f"Failed login attempt for user: {username}", {'ip': ip_address})
                return None
            
            # Login exitoso - resetear intentos
            self.reset_login_attempts(username, ip_address)
            
            # Actualizar último login
            self.update_last_login(user_data['ID'], ip_address)
            
            # Crear sesión
            session_token = self.create_user_session(user_data['ID'], ip_address)
            
            # Log evento exitoso
            self.core.log_analytics_event('user_login_success', user_data['ID'], data={'ip': ip_address})
            self.core.increment_metric('successful_logins')
            
            return {
                'id': user_data['ID'],
                'username': user_data['user_login'],
                'display_name': user_data['display_name'],
                'email': user_data['user_email'],
                'role': user_data['user_role'],
                'session_token': session_token,
                'last_login': user_data.get('last_login'),
                'two_factor_enabled': bool(user_data.get('two_factor_secret'))
            }
            
        except Exception as e:
            self.core.log_error(f"Authentication error: {e}", {'username': username, 'ip': ip_address})
            return None
    
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Obtener usuario por nombre de usuario"""
        result = self.core.execute_query(
            "SELECT * FROM wp_users WHERE user_login = ?", 
            (username,)
        )
        return dict(result[0]) if result else None
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Obtener usuario por ID"""
        result = self.core.execute_query(
            "SELECT * FROM wp_users WHERE ID = ?", 
            (user_id,)
        )
        return dict(result[0]) if result else None
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verificar contraseña"""
        return self.core.verify_password(password, hashed)
    
    def hash_password(self, password: str) -> str:
        """Hash de contraseña"""
        return self.core.hash_password(password)
    
    def is_user_locked(self, username: str, ip_address: str = None) -> bool:
        """Verificar si el usuario está bloqueado"""
        try:
            # Verificar bloqueo por usuario
            user_data = self.get_user_by_username(username)
            if user_data and user_data.get('locked_until'):
                locked_until = datetime.fromisoformat(user_data['locked_until'])
                if datetime.now() < locked_until:
                    return True
            
            # Verificar bloqueo por IP (si se proporciona)
            if ip_address:
                # Contar intentos recientes por IP
                recent_attempts = self.core.execute_query("""
                    SELECT COUNT(*) as count FROM wp_system_logs 
                    WHERE log_category = 'auth' 
                    AND message LIKE '%failed login%' 
                    AND context LIKE ?
                    AND timestamp > datetime('now', '-30 minutes')
                """, (f'%{ip_address}%',))
                
                if recent_attempts and recent_attempts[0]['count'] >= self.max_login_attempts:
                    return True
            
            return False
        except Exception as e:
            self.core.log_error(f"Error checking user lock status: {e}")
            return False
    
    def increment_login_attempts(self, username: str, ip_address: str = None) -> None:
        """Incrementar intentos de login"""
        try:
            user_data = self.get_user_by_username(username)
            if user_data:
                attempts = user_data.get('login_attempts', 0) + 1
                
                # Si excede el máximo, bloquear usuario
                if attempts >= self.max_login_attempts:
                    locked_until = datetime.now() + timedelta(minutes=self.lockout_duration)
                    self.core.execute_update("""
                        UPDATE wp_users 
                        SET login_attempts = ?, locked_until = ?
                        WHERE user_login = ?
                    """, (attempts, locked_until.isoformat(), username))
                    
                    self.core.log_warning(f"User locked due to excessive login attempts: {username}", 
                                        {'ip': ip_address, 'attempts': attempts})
                else:
                    self.core.execute_update("""
                        UPDATE wp_users 
                        SET login_attempts = ?
                        WHERE user_login = ?
                    """, (attempts, username))
            
            self.core.increment_metric('login_attempts')
            
        except Exception as e:
            self.core.log_error(f"Error incrementing login attempts: {e}")
    
    def reset_login_attempts(self, username: str, ip_address: str = None) -> None:
        """Resetear intentos de login"""
        try:
            self.core.execute_update("""
                UPDATE wp_users 
                SET login_attempts = 0, locked_until = NULL
                WHERE user_login = ?
            """, (username,))
        except Exception as e:
            self.core.log_error(f"Error resetting login attempts: {e}")
    
    def update_last_login(self, user_id: int, ip_address: str = None) -> None:
        """Actualizar último login"""
        try:
            self.core.execute_update("""
                UPDATE wp_users 
                SET last_login = CURRENT_TIMESTAMP
                WHERE ID = ?
            """, (user_id,))
        except Exception as e:
            self.core.log_error(f"Error updating last login: {e}")
    
    def create_user_session(self, user_id: int, ip_address: str = None, user_agent: str = None) -> str:
        """Crear sesión de usuario"""
        try:
            session_id = secrets.token_urlsafe(32)
            expires_at = datetime.now() + timedelta(hours=self.session_timeout)
            
            self.core.execute_update("""
                INSERT INTO wp_user_sessions 
                (session_id, user_id, ip_address, user_agent, expires_at)
                VALUES (?, ?, ?, ?, ?)
            """, (session_id, user_id, ip_address, user_agent, expires_at.isoformat()))
            
            return session_id
        except Exception as e:
            self.core.log_error(f"Error creating user session: {e}")
            return ""
    
    def validate_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Validar sesión de usuario"""
        try:
            result = self.core.execute_query("""
                SELECT s.*, u.user_login, u.display_name, u.user_role
                FROM wp_user_sessions s
                JOIN wp_users u ON s.user_id = u.ID
                WHERE s.session_id = ? AND s.is_active = 1 AND s.expires_at > datetime('now')
            """, (session_id,))
            
            if result:
                # Actualizar última actividad
                self.core.execute_update("""
                    UPDATE wp_user_sessions 
                    SET last_activity = CURRENT_TIMESTAMP
                    WHERE session_id = ?
                """, (session_id,))
                
                return dict(result[0])
            
            return None
        except Exception as e:
            self.core.log_error(f"Error validating session: {e}")
            return None
    
    def destroy_session(self, session_id: str) -> bool:
        """Destruir sesión de usuario"""
        try:
            return self.core.execute_update("""
                UPDATE wp_user_sessions 
                SET is_active = 0
                WHERE session_id = ?
            """, (session_id,))
        except Exception as e:
            self.core.log_error(f"Error destroying session: {e}")
            return False
    
    def cleanup_expired_sessions(self) -> int:
        """Limpiar sesiones expiradas"""
        try:
            result = self.core.execute_query("""
                SELECT COUNT(*) as count FROM wp_user_sessions 
                WHERE expires_at < datetime('now') OR is_active = 0
            """)
            count = result[0]['count'] if result else 0
            
            self.core.execute_update("""
                DELETE FROM wp_user_sessions 
                WHERE expires_at < datetime('now') OR is_active = 0
            """)
            
            return count
        except Exception as e:
            self.core.log_error(f"Error cleaning up sessions: {e}")
            return 0
    
    def validate_password_strength(self, password: str) -> Tuple[bool, List[str]]:
        """Validar fortaleza de contraseña"""
        errors = []
        
        if len(password) < self.password_min_length:
            errors.append(f"La contraseña debe tener al menos {self.password_min_length} caracteres")
        
        if not re.search(r'[A-Z]', password):
            errors.append("La contraseña debe contener al menos una letra mayúscula")
        
        if not re.search(r'[a-z]', password):
            errors.append("La contraseña debe contener al menos una letra minúscula")
        
        if not re.search(r'\d', password):
            errors.append("La contraseña debe contener al menos un número")
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("La contraseña debe contener al menos un carácter especial")
        
        return len(errors) == 0, errors
    
    def generate_2fa_secret(self, user_id: int) -> str:
        """Generar secreto para 2FA"""
        try:
            secret = random_base32()

            self.core.execute_update("""
                UPDATE wp_users
                SET two_factor_secret = ?
                WHERE ID = ?
            """, (secret, user_id))

            return secret
        except Exception as e:
            self.core.log_error(f"Error generating 2FA secret: {e}")
            return ""

    def generate_2fa_qr_code(self, user_email: str, secret: str) -> str:
        """Generar código QR para 2FA (simulado)"""
        try:
            # En producción, usar qrcode real
            totp_uri = f"otpauth://totp/SoloYLibre WordPress:{user_email}?secret={secret}&issuer=SoloYLibre WordPress"

            # Retornar URI directamente (en producción generar QR real)
            return totp_uri
        except Exception as e:
            self.core.log_error(f"Error generating 2FA QR code: {e}")
            return ""

    def verify_2fa_token(self, user_id: int, token: str) -> bool:
        """Verificar token 2FA"""
        try:
            user_data = self.get_user_by_id(user_id)
            if not user_data or not user_data.get('two_factor_secret'):
                return False

            totp = MockTOTP(user_data['two_factor_secret'])
            return totp.verify(token, valid_window=1)
        except Exception as e:
            self.core.log_error(f"Error verifying 2FA token: {e}")
            return False
    
    def create_user(self, username: str, email: str, password: str, 
                   display_name: str = "", role: str = "subscriber") -> Optional[int]:
        """Crear nuevo usuario"""
        try:
            # Validar datos
            if not username or not email or not password:
                return None
            
            if not self.core.validate_email(email):
                return None
            
            is_strong, errors = self.validate_password_strength(password)
            if not is_strong:
                self.core.log_warning(f"Weak password attempt for user: {username}")
                return None
            
            # Verificar si el usuario ya existe
            if self.get_user_by_username(username):
                return None
            
            # Hash de la contraseña
            password_hash = self.hash_password(password)
            
            # Crear usuario
            query = """
                INSERT INTO wp_users 
                (user_login, user_pass, user_nicename, user_email, 
                 display_name, user_role, user_registered)
                VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
            
            if self.core.execute_update(query, (
                username, password_hash, username.lower(), email,
                display_name or username, role
            )):
                # Obtener ID del usuario creado
                result = self.core.execute_query(
                    "SELECT ID FROM wp_users WHERE user_login = ?", 
                    (username,)
                )
                
                if result:
                    user_id = result[0]['ID']
                    self.core.log_analytics_event('user_created', user_id)
                    return user_id
            
            return None
        except Exception as e:
            self.core.log_error(f"Error creating user: {e}")
            return None
    
    def get_user_permissions(self, user_id: int) -> List[str]:
        """Obtener permisos del usuario"""
        try:
            user_data = self.get_user_by_id(user_id)
            if not user_data:
                return []
            
            role = user_data.get('user_role', 'subscriber')
            
            # Definir permisos por rol
            permissions_map = {
                'administrator': [
                    'manage_options', 'manage_users', 'edit_posts', 'edit_pages',
                    'edit_others_posts', 'publish_posts', 'manage_categories',
                    'moderate_comments', 'manage_links', 'upload_files',
                    'import', 'unfiltered_html', 'edit_themes', 'install_plugins',
                    'edit_plugins', 'edit_files', 'manage_analytics', 'manage_security'
                ],
                'editor': [
                    'edit_posts', 'edit_pages', 'edit_others_posts', 'publish_posts',
                    'manage_categories', 'moderate_comments', 'manage_links',
                    'upload_files', 'unfiltered_html'
                ],
                'author': [
                    'edit_posts', 'publish_posts', 'upload_files'
                ],
                'contributor': [
                    'edit_posts'
                ],
                'subscriber': []
            }
            
            return permissions_map.get(role, [])
        except Exception as e:
            self.core.log_error(f"Error getting user permissions: {e}")
            return []
    
    def user_can(self, user_id: int, permission: str) -> bool:
        """Verificar si el usuario tiene un permiso específico"""
        permissions = self.get_user_permissions(user_id)
        return permission in permissions
