#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - Content Management Module
Módulo de gestión de contenido avanzado
Desarrollado por Jose L Encarnacion (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import re
import html
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import quote

class ContentManager:
    """Gestor de contenido avanzado"""
    
    def __init__(self, core_system):
        self.core = core_system
        self.allowed_html_tags = [
            'p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li', 'a', 'img', 'blockquote', 'code', 'pre',
            'table', 'tr', 'td', 'th', 'thead', 'tbody', 'div', 'span'
        ]
        
    def create_post(self, title: str, content: str, author_id: int, 
                   post_type: str = 'post', status: str = 'publish',
                   excerpt: str = "", featured_image: str = "",
                   categories: List[str] = None, tags: List[str] = None,
                   seo_data: Dict[str, str] = None) -> Optional[int]:
        """Crear nuevo post o página"""
        try:
            # Sanitizar y validar datos
            title = self.sanitize_title(title)
            content = self.sanitize_content(content)
            excerpt = self.sanitize_content(excerpt)
            
            if not title or not content:
                return None
            
            # Generar slug único
            slug = self.generate_unique_slug(title, post_type)
            
            # Datos SEO
            seo_title = seo_data.get('title', title) if seo_data else title
            seo_description = seo_data.get('description', excerpt) if seo_data else excerpt
            seo_keywords = seo_data.get('keywords', '') if seo_data else ''
            
            # Crear post
            query = """
                INSERT INTO wp_posts 
                (post_author, post_date, post_date_gmt, post_content, post_title, 
                 post_excerpt, post_status, post_name, post_type, featured_image,
                 seo_title, seo_description, seo_keywords, post_modified, post_modified_gmt)
                VALUES (?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """
            
            if self.core.execute_update(query, (
                author_id, content, title, excerpt, status, slug, post_type,
                featured_image, seo_title, seo_description, seo_keywords
            )):
                # Obtener ID del post creado
                result = self.core.execute_query(
                    "SELECT ID FROM wp_posts WHERE post_name = ? AND post_type = ? ORDER BY ID DESC LIMIT 1",
                    (slug, post_type)
                )
                
                if result:
                    post_id = result[0]['ID']
                    
                    # Asignar categorías y tags
                    if categories:
                        self.assign_categories(post_id, categories)
                    if tags:
                        self.assign_tags(post_id, tags)
                    
                    # Log analytics
                    self.core.log_analytics_event('post_created', author_id, post_id, {
                        'title': title,
                        'type': post_type,
                        'status': status
                    })
                    
                    self.core.increment_metric('posts_created')
                    
                    return post_id
            
            return None
        except Exception as e:
            self.core.log_error(f"Error creating post: {e}")
            return None
    
    def get_posts(self, post_type: str = 'post', status: str = 'publish', 
                 limit: int = 10, offset: int = 0, author_id: int = None,
                 search: str = None, category: str = None) -> List[Dict[str, Any]]:
        """Obtener posts con filtros"""
        try:
            query = """
                SELECT p.*, u.display_name as author_name, u.user_email as author_email
                FROM wp_posts p
                LEFT JOIN wp_users u ON p.post_author = u.ID
                WHERE p.post_type = ? AND p.post_status = ?
            """
            params = [post_type, status]
            
            # Filtros adicionales
            if author_id:
                query += " AND p.post_author = ?"
                params.append(author_id)
            
            if search:
                query += " AND (p.post_title LIKE ? OR p.post_content LIKE ?)"
                search_term = f"%{search}%"
                params.extend([search_term, search_term])
            
            if category:
                query += """
                    AND p.ID IN (
                        SELECT tr.object_id FROM wp_term_relationships tr
                        JOIN wp_terms t ON tr.term_taxonomy_id = t.term_id
                        WHERE t.slug = ? AND t.taxonomy = 'category'
                    )
                """
                params.append(category)
            
            query += " ORDER BY p.post_date DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            result = self.core.execute_query(query, tuple(params))
            
            posts = []
            for row in result:
                post_data = dict(row)
                
                # Agregar metadatos adicionales
                post_data['categories'] = self.get_post_categories(post_data['ID'])
                post_data['tags'] = self.get_post_tags(post_data['ID'])
                post_data['formatted_date'] = self.core.format_date(post_data['post_date'])
                post_data['excerpt_clean'] = self.generate_excerpt(post_data['post_content'], 150)
                post_data['reading_time'] = self.calculate_reading_time(post_data['post_content'])
                
                posts.append(post_data)
            
            return posts
        except Exception as e:
            self.core.log_error(f"Error getting posts: {e}")
            return []
    
    def get_post_by_id(self, post_id: int) -> Optional[Dict[str, Any]]:
        """Obtener post por ID"""
        try:
            result = self.core.execute_query("""
                SELECT p.*, u.display_name as author_name, u.user_email as author_email
                FROM wp_posts p
                LEFT JOIN wp_users u ON p.post_author = u.ID
                WHERE p.ID = ?
            """, (post_id,))
            
            if result:
                post_data = dict(result[0])
                
                # Incrementar contador de vistas
                self.increment_post_views(post_id)
                
                # Agregar metadatos
                post_data['categories'] = self.get_post_categories(post_id)
                post_data['tags'] = self.get_post_tags(post_id)
                post_data['formatted_date'] = self.core.format_date(post_data['post_date'])
                post_data['reading_time'] = self.calculate_reading_time(post_data['post_content'])
                
                return post_data
            
            return None
        except Exception as e:
            self.core.log_error(f"Error getting post by ID: {e}")
            return None
    
    def get_post_by_slug(self, slug: str, post_type: str = 'post') -> Optional[Dict[str, Any]]:
        """Obtener post por slug"""
        try:
            result = self.core.execute_query("""
                SELECT p.*, u.display_name as author_name, u.user_email as author_email
                FROM wp_posts p
                LEFT JOIN wp_users u ON p.post_author = u.ID
                WHERE p.post_name = ? AND p.post_type = ? AND p.post_status = 'publish'
            """, (slug, post_type))
            
            if result:
                post_data = dict(result[0])
                
                # Incrementar contador de vistas
                self.increment_post_views(post_data['ID'])
                
                # Agregar metadatos
                post_data['categories'] = self.get_post_categories(post_data['ID'])
                post_data['tags'] = self.get_post_tags(post_data['ID'])
                post_data['formatted_date'] = self.core.format_date(post_data['post_date'])
                post_data['reading_time'] = self.calculate_reading_time(post_data['post_content'])
                
                return post_data
            
            return None
        except Exception as e:
            self.core.log_error(f"Error getting post by slug: {e}")
            return None
    
    def update_post(self, post_id: int, title: str = None, content: str = None,
                   excerpt: str = None, status: str = None, featured_image: str = None,
                   categories: List[str] = None, tags: List[str] = None,
                   seo_data: Dict[str, str] = None) -> bool:
        """Actualizar post existente"""
        try:
            # Construir query dinámicamente
            updates = []
            params = []
            
            if title is not None:
                title = self.sanitize_title(title)
                updates.append("post_title = ?")
                params.append(title)
                
                # Actualizar slug si cambia el título
                new_slug = self.generate_unique_slug(title, exclude_id=post_id)
                updates.append("post_name = ?")
                params.append(new_slug)
            
            if content is not None:
                content = self.sanitize_content(content)
                updates.append("post_content = ?")
                params.append(content)
            
            if excerpt is not None:
                excerpt = self.sanitize_content(excerpt)
                updates.append("post_excerpt = ?")
                params.append(excerpt)
            
            if status is not None:
                updates.append("post_status = ?")
                params.append(status)
            
            if featured_image is not None:
                updates.append("featured_image = ?")
                params.append(featured_image)
            
            # Datos SEO
            if seo_data:
                if 'title' in seo_data:
                    updates.append("seo_title = ?")
                    params.append(seo_data['title'])
                if 'description' in seo_data:
                    updates.append("seo_description = ?")
                    params.append(seo_data['description'])
                if 'keywords' in seo_data:
                    updates.append("seo_keywords = ?")
                    params.append(seo_data['keywords'])
            
            if updates:
                updates.append("post_modified = CURRENT_TIMESTAMP")
                updates.append("post_modified_gmt = CURRENT_TIMESTAMP")
                
                query = f"UPDATE wp_posts SET {', '.join(updates)} WHERE ID = ?"
                params.append(post_id)
                
                if self.core.execute_update(query, tuple(params)):
                    # Actualizar categorías y tags si se proporcionan
                    if categories is not None:
                        self.assign_categories(post_id, categories)
                    if tags is not None:
                        self.assign_tags(post_id, tags)
                    
                    # Log analytics
                    self.core.log_analytics_event('post_updated', None, post_id)
                    
                    return True
            
            return False
        except Exception as e:
            self.core.log_error(f"Error updating post: {e}")
            return False
    
    def delete_post(self, post_id: int, permanent: bool = False) -> bool:
        """Eliminar post (mover a papelera o eliminar permanentemente)"""
        try:
            if permanent:
                # Eliminar permanentemente
                success = self.core.execute_update("DELETE FROM wp_posts WHERE ID = ?", (post_id,))
                if success:
                    # Limpiar relaciones
                    self.core.execute_update("DELETE FROM wp_term_relationships WHERE object_id = ?", (post_id,))
                    self.core.execute_update("DELETE FROM wp_postmeta WHERE post_id = ?", (post_id,))
                    self.core.execute_update("DELETE FROM wp_comments WHERE comment_post_ID = ?", (post_id,))
            else:
                # Mover a papelera
                success = self.core.execute_update(
                    "UPDATE wp_posts SET post_status = 'trash' WHERE ID = ?", 
                    (post_id,)
                )
            
            if success:
                self.core.log_analytics_event('post_deleted', None, post_id, {'permanent': permanent})
            
            return success
        except Exception as e:
            self.core.log_error(f"Error deleting post: {e}")
            return False
    
    def sanitize_title(self, title: str) -> str:
        """Sanitizar título"""
        if not title:
            return ""
        
        # Remover HTML
        title = re.sub(r'<[^>]+>', '', title)
        # Decodificar entidades HTML
        title = html.unescape(title)
        # Limpiar espacios
        title = re.sub(r'\s+', ' ', title).strip()
        
        return title[:255]  # Limitar longitud
    
    def sanitize_content(self, content: str) -> str:
        """Sanitizar contenido HTML"""
        if not content:
            return ""
        
        # Permitir solo tags seguros
        allowed_pattern = '|'.join(self.allowed_html_tags)
        
        # Remover tags no permitidos
        content = re.sub(rf'<(?!/?(?:{allowed_pattern})\b)[^>]*>', '', content)
        
        # Limpiar atributos peligrosos
        content = re.sub(r'on\w+="[^"]*"', '', content)  # Eventos JavaScript
        content = re.sub(r'javascript:', '', content)     # URLs JavaScript
        
        return content
    
    def generate_unique_slug(self, title: str, post_type: str = 'post', exclude_id: int = None) -> str:
        """Generar slug único"""
        base_slug = self.core.generate_slug(title)
        slug = base_slug
        counter = 1
        
        while True:
            # Verificar si el slug existe
            query = "SELECT ID FROM wp_posts WHERE post_name = ? AND post_type = ?"
            params = [slug, post_type]
            
            if exclude_id:
                query += " AND ID != ?"
                params.append(exclude_id)
            
            result = self.core.execute_query(query, tuple(params))
            
            if not result:
                break
            
            # Si existe, agregar número
            slug = f"{base_slug}-{counter}"
            counter += 1
        
        return slug
    
    def generate_excerpt(self, content: str, length: int = 150) -> str:
        """Generar excerpt automático"""
        if not content:
            return ""
        
        # Remover HTML
        text = re.sub(r'<[^>]+>', '', content)
        # Decodificar entidades
        text = html.unescape(text)
        # Limpiar espacios
        text = re.sub(r'\s+', ' ', text).strip()
        
        if len(text) <= length:
            return text
        
        # Cortar en la palabra más cercana
        truncated = text[:length]
        last_space = truncated.rfind(' ')
        if last_space > 0:
            truncated = truncated[:last_space]
        
        return truncated + "..."
    
    def calculate_reading_time(self, content: str) -> int:
        """Calcular tiempo de lectura en minutos"""
        if not content:
            return 0
        
        # Remover HTML y contar palabras
        text = re.sub(r'<[^>]+>', '', content)
        words = len(text.split())
        
        # Promedio de 200 palabras por minuto
        reading_time = max(1, round(words / 200))
        
        return reading_time
    
    def increment_post_views(self, post_id: int) -> bool:
        """Incrementar contador de vistas"""
        try:
            return self.core.execute_update(
                "UPDATE wp_posts SET view_count = view_count + 1 WHERE ID = ?",
                (post_id,)
            )
        except Exception as e:
            self.core.log_error(f"Error incrementing post views: {e}")
            return False
    
    def get_post_categories(self, post_id: int) -> List[Dict[str, Any]]:
        """Obtener categorías del post"""
        try:
            result = self.core.execute_query("""
                SELECT t.* FROM wp_terms t
                JOIN wp_term_relationships tr ON t.term_id = tr.term_taxonomy_id
                WHERE tr.object_id = ? AND t.taxonomy = 'category'
            """, (post_id,))
            
            return [dict(row) for row in result]
        except Exception as e:
            self.core.log_error(f"Error getting post categories: {e}")
            return []
    
    def get_post_tags(self, post_id: int) -> List[Dict[str, Any]]:
        """Obtener tags del post"""
        try:
            result = self.core.execute_query("""
                SELECT t.* FROM wp_terms t
                JOIN wp_term_relationships tr ON t.term_id = tr.term_taxonomy_id
                WHERE tr.object_id = ? AND t.taxonomy = 'post_tag'
            """, (post_id,))
            
            return [dict(row) for row in result]
        except Exception as e:
            self.core.log_error(f"Error getting post tags: {e}")
            return []
    
    def assign_categories(self, post_id: int, categories: List[str]) -> bool:
        """Asignar categorías al post"""
        try:
            # Remover categorías existentes
            self.core.execute_update("""
                DELETE FROM wp_term_relationships 
                WHERE object_id = ? AND term_taxonomy_id IN (
                    SELECT term_id FROM wp_terms WHERE taxonomy = 'category'
                )
            """, (post_id,))
            
            # Asignar nuevas categorías
            for category in categories:
                # Crear categoría si no existe
                category_id = self.get_or_create_term(category, 'category')
                if category_id:
                    self.core.execute_update("""
                        INSERT OR IGNORE INTO wp_term_relationships (object_id, term_taxonomy_id)
                        VALUES (?, ?)
                    """, (post_id, category_id))
            
            return True
        except Exception as e:
            self.core.log_error(f"Error assigning categories: {e}")
            return False
    
    def assign_tags(self, post_id: int, tags: List[str]) -> bool:
        """Asignar tags al post"""
        try:
            # Remover tags existentes
            self.core.execute_update("""
                DELETE FROM wp_term_relationships 
                WHERE object_id = ? AND term_taxonomy_id IN (
                    SELECT term_id FROM wp_terms WHERE taxonomy = 'post_tag'
                )
            """, (post_id,))
            
            # Asignar nuevos tags
            for tag in tags:
                # Crear tag si no existe
                tag_id = self.get_or_create_term(tag, 'post_tag')
                if tag_id:
                    self.core.execute_update("""
                        INSERT OR IGNORE INTO wp_term_relationships (object_id, term_taxonomy_id)
                        VALUES (?, ?)
                    """, (post_id, tag_id))
            
            return True
        except Exception as e:
            self.core.log_error(f"Error assigning tags: {e}")
            return False
    
    def get_or_create_term(self, name: str, taxonomy: str) -> Optional[int]:
        """Obtener o crear término (categoría/tag)"""
        try:
            # Buscar término existente
            result = self.core.execute_query(
                "SELECT term_id FROM wp_terms WHERE name = ? AND taxonomy = ?",
                (name, taxonomy)
            )
            
            if result:
                return result[0]['term_id']
            
            # Crear nuevo término
            slug = self.core.generate_slug(name)
            if self.core.execute_update("""
                INSERT INTO wp_terms (name, slug, taxonomy)
                VALUES (?, ?, ?)
            """, (name, slug, taxonomy)):
                result = self.core.execute_query(
                    "SELECT term_id FROM wp_terms WHERE name = ? AND taxonomy = ?",
                    (name, taxonomy)
                )
                return result[0]['term_id'] if result else None
            
            return None
        except Exception as e:
            self.core.log_error(f"Error getting or creating term: {e}")
            return None
