#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - WordPress Core Functions
Implementación completa de todas las funcionalidades de WordPress wp-admin
Desarrollado por <PERSON> (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import os
import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Optional, Any
from urllib.parse import quote, unquote

class WordPressCore:
    """Núcleo completo de funcionalidades WordPress"""
    
    def __init__(self, core_system):
        self.core = core_system
        self.wp_version = "6.4.2"
        self.theme_active = "soloylibre-ultimate"
        self.plugins_active = []
        
        # Configuraciones por defecto de WordPress
        self.wp_options = {
            'blogname': 'SoloYLibre WordPress Ultimate',
            'blogdescription': 'Sistema WordPress profesional desde San José de Ocoa, RD',
            'admin_email': '<EMAIL>',
            'users_can_register': 0,
            'default_role': 'subscriber',
            'timezone_string': 'America/Santo_Domingo',
            'date_format': 'd/m/Y',
            'time_format': 'H:i',
            'start_of_week': 1,
            'use_balanceTags': 0,
            'use_smilies': 1,
            'require_name_email': 1,
            'comments_notify': 1,
            'posts_per_page': 10,
            'posts_per_rss': 10,
            'rss_use_excerpt': 0,
            'mailserver_url': 'mail.example.com',
            'mailserver_login': '<EMAIL>',
            'mailserver_pass': 'password',
            'mailserver_port': 110,
            'default_category': 1,
            'default_comment_status': 'open',
            'default_ping_status': 'open',
            'default_pingback_flag': 1,
            'posts_per_page': 10,
            'thread_comments': 1,
            'thread_comments_depth': 5,
            'page_comments': 0,
            'comments_per_page': 50,
            'default_comments_page': 'newest',
            'comment_order': 'asc',
            'sticky_posts': [],
            'widget_categories': {},
            'widget_text': {},
            'widget_rss': {},
            'uninstall_plugins': {},
            'timezone_string': 'America/Santo_Domingo',
            'permalink_structure': '/%postname%/',
            'rewrite_rules': {},
            'hack_file': 0,
            'blog_charset': 'UTF-8',
            'moderation_keys': '',
            'active_plugins': [],
            'category_base': '',
            'tag_base': '',
            'show_on_front': 'posts',
            'page_on_front': 0,
            'page_for_posts': 0,
            'default_post_format': '0',
            'link_manager_enabled': 0,
            'finished_splitting_shared_terms': 1,
            'site_icon': 0,
            'medium_large_size_w': 768,
            'medium_large_size_h': 0,
            'wp_page_for_privacy_policy': 0,
            'show_comments_cookies_opt_in': 1,
            'admin_email_lifespan': 15552000,
            'disallowed_keys': '',
            'comment_previously_approved': 1,
            'auto_plugin_theme_update_emails': [],
            'auto_update_core_dev': 'enabled',
            'auto_update_core_minor': 'enabled',
            'auto_update_core_major': 'enabled'
        }
    
    # ==================== POSTS MANAGEMENT ====================
    
    def get_posts_admin(self, post_type='post', status='any', limit=20, offset=0, 
                       search='', author_id=None, category_id=None, tag_id=None,
                       date_query=None, orderby='date', order='DESC'):
        """Obtener posts para administración (equivalente a wp-admin/edit.php)"""
        try:
            query = """
                SELECT p.*, u.display_name as author_name, u.user_email as author_email,
                       COUNT(c.comment_ID) as comment_count
                FROM wp_posts p
                LEFT JOIN wp_users u ON p.post_author = u.ID
                LEFT JOIN wp_comments c ON p.ID = c.comment_post_ID AND c.comment_approved = '1'
                WHERE p.post_type = ?
            """
            params = [post_type]
            
            # Filtros de estado
            if status != 'any':
                if status == 'trash':
                    query += " AND p.post_status = 'trash'"
                elif status == 'draft':
                    query += " AND p.post_status = 'draft'"
                elif status == 'pending':
                    query += " AND p.post_status = 'pending'"
                elif status == 'private':
                    query += " AND p.post_status = 'private'"
                elif status == 'publish':
                    query += " AND p.post_status = 'publish'"
                else:
                    query += " AND p.post_status != 'trash'"
            
            # Filtro de búsqueda
            if search:
                query += " AND (p.post_title LIKE ? OR p.post_content LIKE ?)"
                search_term = f"%{search}%"
                params.extend([search_term, search_term])
            
            # Filtro de autor
            if author_id:
                query += " AND p.post_author = ?"
                params.append(author_id)
            
            # Filtro de categoría
            if category_id:
                query += """
                    AND p.ID IN (
                        SELECT tr.object_id FROM wp_term_relationships tr
                        JOIN wp_terms t ON tr.term_taxonomy_id = t.term_id
                        WHERE t.term_id = ? AND t.taxonomy = 'category'
                    )
                """
                params.append(category_id)
            
            # Filtro de tag
            if tag_id:
                query += """
                    AND p.ID IN (
                        SELECT tr.object_id FROM wp_term_relationships tr
                        JOIN wp_terms t ON tr.term_taxonomy_id = t.term_id
                        WHERE t.term_id = ? AND t.taxonomy = 'post_tag'
                    )
                """
                params.append(tag_id)
            
            # Agrupar y ordenar
            query += " GROUP BY p.ID"
            
            # Ordenamiento
            if orderby == 'title':
                query += f" ORDER BY p.post_title {order}"
            elif orderby == 'author':
                query += f" ORDER BY u.display_name {order}"
            elif orderby == 'comments':
                query += f" ORDER BY comment_count {order}"
            elif orderby == 'date':
                query += f" ORDER BY p.post_date {order}"
            elif orderby == 'modified':
                query += f" ORDER BY p.post_modified {order}"
            
            # Paginación
            query += " LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            result = self.core.execute_query(query, tuple(params))
            
            posts = []
            for row in result:
                post_data = dict(row)
                
                # Agregar metadatos adicionales
                post_data['categories'] = self.get_post_categories(post_data['ID'])
                post_data['tags'] = self.get_post_tags(post_data['ID'])
                post_data['featured_image'] = self.get_post_featured_image(post_data['ID'])
                post_data['post_meta'] = self.get_post_meta(post_data['ID'])
                post_data['edit_url'] = f"/soloylibre-admin/post.php?action=edit&post={post_data['ID']}"
                post_data['view_url'] = f"/post/{post_data['post_name']}"
                post_data['formatted_date'] = self.core.format_date(post_data['post_date'])
                post_data['word_count'] = len(post_data['post_content'].split()) if post_data['post_content'] else 0
                
                posts.append(post_data)
            
            return posts
            
        except Exception as e:
            self.core.log_error(f"Error getting posts admin: {e}")
            return []
    
    def get_post_counts_by_status(self, post_type='post'):
        """Obtener conteos de posts por estado (para filtros de wp-admin)"""
        try:
            query = """
                SELECT post_status, COUNT(*) as count
                FROM wp_posts
                WHERE post_type = ?
                GROUP BY post_status
            """
            result = self.core.execute_query(query, (post_type,))
            
            counts = {
                'all': 0,
                'publish': 0,
                'draft': 0,
                'pending': 0,
                'private': 0,
                'trash': 0
            }
            
            for row in result:
                status = row['post_status']
                count = row['count']
                counts[status] = count
                if status != 'trash':
                    counts['all'] += count
            
            return counts
            
        except Exception as e:
            self.core.log_error(f"Error getting post counts: {e}")
            return {}
    
    def bulk_edit_posts(self, post_ids: List[int], action: str, data: Dict = None):
        """Edición masiva de posts (equivalente a bulk actions de wp-admin)"""
        try:
            if not post_ids or not action:
                return False
            
            post_ids_str = ','.join(map(str, post_ids))
            
            if action == 'trash':
                query = f"UPDATE wp_posts SET post_status = 'trash' WHERE ID IN ({post_ids_str})"
                success = self.core.execute_update(query)
                
            elif action == 'untrash':
                query = f"UPDATE wp_posts SET post_status = 'publish' WHERE ID IN ({post_ids_str})"
                success = self.core.execute_update(query)
                
            elif action == 'delete':
                # Eliminar permanentemente
                query = f"DELETE FROM wp_posts WHERE ID IN ({post_ids_str})"
                success = self.core.execute_update(query)
                if success:
                    # Limpiar metadatos relacionados
                    self.core.execute_update(f"DELETE FROM wp_postmeta WHERE post_id IN ({post_ids_str})")
                    self.core.execute_update(f"DELETE FROM wp_term_relationships WHERE object_id IN ({post_ids_str})")
                    self.core.execute_update(f"DELETE FROM wp_comments WHERE comment_post_ID IN ({post_ids_str})")
                
            elif action == 'edit' and data:
                # Edición masiva con datos específicos
                updates = []
                params = []
                
                if 'post_status' in data:
                    updates.append("post_status = ?")
                    params.append(data['post_status'])
                
                if 'post_author' in data:
                    updates.append("post_author = ?")
                    params.append(data['post_author'])
                
                if 'comment_status' in data:
                    updates.append("comment_status = ?")
                    params.append(data['comment_status'])
                
                if 'ping_status' in data:
                    updates.append("ping_status = ?")
                    params.append(data['ping_status'])
                
                if updates:
                    query = f"UPDATE wp_posts SET {', '.join(updates)} WHERE ID IN ({post_ids_str})"
                    success = self.core.execute_update(query, tuple(params))
                else:
                    success = True
            
            else:
                return False
            
            if success:
                # Log de la acción
                self.core.log_analytics_event('bulk_edit_posts', data={
                    'action': action,
                    'post_count': len(post_ids),
                    'post_ids': post_ids
                })
            
            return success
            
        except Exception as e:
            self.core.log_error(f"Error in bulk edit posts: {e}")
            return False
    
    # ==================== CATEGORIES & TAGS ====================
    
    def get_categories_admin(self, search='', orderby='name', order='ASC', limit=50, offset=0):
        """Obtener categorías para administración"""
        try:
            query = """
                SELECT t.*, COUNT(tr.object_id) as post_count
                FROM wp_terms t
                LEFT JOIN wp_term_relationships tr ON t.term_id = tr.term_taxonomy_id
                LEFT JOIN wp_posts p ON tr.object_id = p.ID AND p.post_status = 'publish' AND p.post_type = 'post'
                WHERE t.taxonomy = 'category'
            """
            params = []
            
            if search:
                query += " AND t.name LIKE ?"
                params.append(f"%{search}%")
            
            query += " GROUP BY t.term_id"
            
            if orderby == 'name':
                query += f" ORDER BY t.name {order}"
            elif orderby == 'count':
                query += f" ORDER BY post_count {order}"
            elif orderby == 'slug':
                query += f" ORDER BY t.slug {order}"
            
            query += " LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            result = self.core.execute_query(query, tuple(params))
            
            categories = []
            for row in result:
                cat_data = dict(row)
                cat_data['edit_url'] = f"/soloylibre-admin/term.php?taxonomy=category&tag_ID={cat_data['term_id']}&action=edit"
                cat_data['view_url'] = f"/category/{cat_data['slug']}"
                categories.append(cat_data)
            
            return categories
            
        except Exception as e:
            self.core.log_error(f"Error getting categories admin: {e}")
            return []
    
    def get_tags_admin(self, search='', orderby='name', order='ASC', limit=50, offset=0):
        """Obtener tags para administración"""
        try:
            query = """
                SELECT t.*, COUNT(tr.object_id) as post_count
                FROM wp_terms t
                LEFT JOIN wp_term_relationships tr ON t.term_id = tr.term_taxonomy_id
                LEFT JOIN wp_posts p ON tr.object_id = p.ID AND p.post_status = 'publish' AND p.post_type = 'post'
                WHERE t.taxonomy = 'post_tag'
            """
            params = []
            
            if search:
                query += " AND t.name LIKE ?"
                params.append(f"%{search}%")
            
            query += " GROUP BY t.term_id"
            
            if orderby == 'name':
                query += f" ORDER BY t.name {order}"
            elif orderby == 'count':
                query += f" ORDER BY post_count {order}"
            elif orderby == 'slug':
                query += f" ORDER BY t.slug {order}"
            
            query += " LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            result = self.core.execute_query(query, tuple(params))
            
            tags = []
            for row in result:
                tag_data = dict(row)
                tag_data['edit_url'] = f"/soloylibre-admin/term.php?taxonomy=post_tag&tag_ID={tag_data['term_id']}&action=edit"
                tag_data['view_url'] = f"/tag/{tag_data['slug']}"
                tags.append(tag_data)
            
            return tags
            
        except Exception as e:
            self.core.log_error(f"Error getting tags admin: {e}")
            return []
    
    # ==================== COMMENTS MANAGEMENT ====================
    
    def get_comments_admin(self, status='any', post_id=None, search='', limit=20, offset=0,
                          orderby='date', order='DESC'):
        """Obtener comentarios para administración"""
        try:
            query = """
                SELECT c.*, p.post_title, p.post_name, u.display_name as author_name
                FROM wp_comments c
                LEFT JOIN wp_posts p ON c.comment_post_ID = p.ID
                LEFT JOIN wp_users u ON c.user_id = u.ID
                WHERE 1=1
            """
            params = []
            
            # Filtro de estado
            if status != 'any':
                if status == 'approved':
                    query += " AND c.comment_approved = '1'"
                elif status == 'pending':
                    query += " AND c.comment_approved = '0'"
                elif status == 'spam':
                    query += " AND c.comment_approved = 'spam'"
                elif status == 'trash':
                    query += " AND c.comment_approved = 'trash'"
            
            # Filtro de post
            if post_id:
                query += " AND c.comment_post_ID = ?"
                params.append(post_id)
            
            # Filtro de búsqueda
            if search:
                query += " AND (c.comment_content LIKE ? OR c.comment_author LIKE ? OR c.comment_author_email LIKE ?)"
                search_term = f"%{search}%"
                params.extend([search_term, search_term, search_term])
            
            # Ordenamiento
            if orderby == 'author':
                query += f" ORDER BY c.comment_author {order}"
            elif orderby == 'email':
                query += f" ORDER BY c.comment_author_email {order}"
            elif orderby == 'response':
                query += f" ORDER BY p.post_title {order}"
            else:  # date
                query += f" ORDER BY c.comment_date {order}"
            
            # Paginación
            query += " LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            result = self.core.execute_query(query, tuple(params))
            
            comments = []
            for row in result:
                comment_data = dict(row)
                comment_data['formatted_date'] = self.core.format_date(comment_data['comment_date'])
                comment_data['edit_url'] = f"/soloylibre-admin/comment.php?action=editcomment&c={comment_data['comment_ID']}"
                comment_data['post_url'] = f"/post/{comment_data['post_name']}" if comment_data['post_name'] else "#"
                comment_data['avatar_url'] = self.get_avatar_url(comment_data['comment_author_email'])
                comments.append(comment_data)
            
            return comments
            
        except Exception as e:
            self.core.log_error(f"Error getting comments admin: {e}")
            return []
    
    def get_comment_counts_by_status(self):
        """Obtener conteos de comentarios por estado"""
        try:
            query = """
                SELECT comment_approved, COUNT(*) as count
                FROM wp_comments
                GROUP BY comment_approved
            """
            result = self.core.execute_query(query)
            
            counts = {
                'all': 0,
                'approved': 0,
                'pending': 0,
                'spam': 0,
                'trash': 0
            }
            
            for row in result:
                status = row['comment_approved']
                count = row['count']
                
                if status == '1':
                    counts['approved'] = count
                elif status == '0':
                    counts['pending'] = count
                elif status == 'spam':
                    counts['spam'] = count
                elif status == 'trash':
                    counts['trash'] = count
                
                if status != 'trash':
                    counts['all'] += count
            
            return counts
            
        except Exception as e:
            self.core.log_error(f"Error getting comment counts: {e}")
            return {}
    
    # ==================== HELPER FUNCTIONS ====================
    
    def get_post_categories(self, post_id: int):
        """Obtener categorías de un post"""
        try:
            result = self.core.execute_query("""
                SELECT t.* FROM wp_terms t
                JOIN wp_term_relationships tr ON t.term_id = tr.term_taxonomy_id
                WHERE tr.object_id = ? AND t.taxonomy = 'category'
            """, (post_id,))
            return [dict(row) for row in result]
        except:
            return []
    
    def get_post_tags(self, post_id: int):
        """Obtener tags de un post"""
        try:
            result = self.core.execute_query("""
                SELECT t.* FROM wp_terms t
                JOIN wp_term_relationships tr ON t.term_id = tr.term_taxonomy_id
                WHERE tr.object_id = ? AND t.taxonomy = 'post_tag'
            """, (post_id,))
            return [dict(row) for row in result]
        except:
            return []
    
    def get_post_featured_image(self, post_id: int):
        """Obtener imagen destacada de un post"""
        try:
            result = self.core.execute_query("""
                SELECT meta_value FROM wp_postmeta
                WHERE post_id = ? AND meta_key = '_thumbnail_id'
            """, (post_id,))
            if result:
                attachment_id = result[0]['meta_value']
                # Obtener URL de la imagen
                attachment = self.core.execute_query("""
                    SELECT guid FROM wp_posts WHERE ID = ? AND post_type = 'attachment'
                """, (attachment_id,))
                if attachment:
                    return attachment[0]['guid']
            return None
        except:
            return None
    
    def get_post_meta(self, post_id: int):
        """Obtener metadatos de un post"""
        try:
            result = self.core.execute_query("""
                SELECT meta_key, meta_value FROM wp_postmeta WHERE post_id = ?
            """, (post_id,))
            return {row['meta_key']: row['meta_value'] for row in result}
        except:
            return {}
    
    def get_avatar_url(self, email: str, size: int = 32):
        """Generar URL de avatar (Gravatar)"""
        import hashlib
        email_hash = hashlib.md5(email.lower().encode()).hexdigest()
        return f"https://www.gravatar.com/avatar/{email_hash}?s={size}&d=identicon"
    
    def get_wp_option(self, option_name: str, default=None):
        """Obtener opción de WordPress"""
        return self.wp_options.get(option_name, default)
    
    def update_wp_option(self, option_name: str, option_value):
        """Actualizar opción de WordPress"""
        self.wp_options[option_name] = option_value
        return self.core.set_option(f"wp_{option_name}", option_value)
