#!/usr/bin/env python3
"""
SoloYLibre WordPress Simple Server
Servidor de desarrollo para WordPress
Desarrollado por <PERSON> (JoseTusabe)
Desde San José de O<PERSON>, República Dominicana 🇩🇴
"""

import http.server
import socketserver
import webbrowser
import threading
import time

class SoloYLibreWordPressHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/wp-admin' or self.path.startswith('/wp-'):
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html = """
            <!DOCTYPE html>
            <html lang="es">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>🇩🇴 SoloYLibre WordPress Multisite</title>
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }
                    
                    body {
                        font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
                        background: linear-gradient(135deg, #CE1126 0%, #002D62 50%, #4ECDC4 100%);
                        color: white;
                        min-height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    
                    .container {
                        max-width: 900px;
                        margin: 0 auto;
                        background: rgba(255, 255, 255, 0.1);
                        backdrop-filter: blur(20px);
                        padding: 40px;
                        border-radius: 25px;
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                        text-align: center;
                    }
                    
                    h1 {
                        font-size: 3.5rem;
                        margin-bottom: 20px;
                        background: linear-gradient(45deg, #fff, #CE1126);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                        animation: titleGlow 3s ease-in-out infinite;
                    }
                    
                    @keyframes titleGlow {
                        0%, 100% { text-shadow: 0 0 20px rgba(206, 17, 38, 0.5); }
                        50% { text-shadow: 0 0 30px rgba(206, 17, 38, 0.8); }
                    }
                    
                    .subtitle {
                        font-size: 1.3rem;
                        margin-bottom: 30px;
                        opacity: 0.9;
                    }
                    
                    .credentials-section {
                        background: rgba(0, 0, 0, 0.3);
                        padding: 30px;
                        border-radius: 15px;
                        margin: 30px 0;
                        border-left: 5px solid #CE1126;
                        text-align: left;
                    }
                    
                    .credentials-section h3 {
                        color: #4ECDC4;
                        margin-bottom: 20px;
                        font-size: 1.5rem;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }
                    
                    .credential-item {
                        margin: 15px 0;
                        padding: 10px;
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 8px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    
                    .credential-label {
                        font-weight: bold;
                        color: #4ECDC4;
                    }
                    
                    .credential-value {
                        font-family: 'Courier New', monospace;
                        background: rgba(0, 0, 0, 0.3);
                        padding: 5px 10px;
                        border-radius: 5px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    
                    .credential-value:hover {
                        background: rgba(206, 17, 38, 0.3);
                        transform: scale(1.05);
                    }
                    
                    .action-buttons {
                        display: flex;
                        gap: 20px;
                        justify-content: center;
                        margin: 30px 0;
                        flex-wrap: wrap;
                    }
                    
                    .btn {
                        padding: 15px 30px;
                        border: none;
                        border-radius: 25px;
                        font-size: 1rem;
                        font-weight: bold;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        text-decoration: none;
                        display: inline-flex;
                        align-items: center;
                        gap: 10px;
                    }
                    
                    .btn-primary {
                        background: linear-gradient(45deg, #CE1126, #002D62);
                        color: white;
                    }
                    
                    .btn-secondary {
                        background: rgba(255, 255, 255, 0.2);
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                    }
                    
                    .btn:hover {
                        transform: translateY(-3px);
                        box-shadow: 0 10px 30px rgba(206, 17, 38, 0.3);
                    }
                    
                    .status-indicator {
                        display: inline-flex;
                        align-items: center;
                        gap: 10px;
                        background: rgba(76, 205, 196, 0.2);
                        padding: 10px 20px;
                        border-radius: 25px;
                        margin: 20px 0;
                        border: 1px solid #4ECDC4;
                    }
                    
                    .pulse {
                        width: 12px;
                        height: 12px;
                        background: #4ECDC4;
                        border-radius: 50%;
                        animation: pulse 2s infinite;
                    }
                    
                    @keyframes pulse {
                        0% { opacity: 1; transform: scale(1); }
                        50% { opacity: 0.7; transform: scale(1.2); }
                        100% { opacity: 1; transform: scale(1); }
                    }
                    
                    .developer-info {
                        background: rgba(206, 17, 38, 0.2);
                        padding: 20px;
                        border-radius: 15px;
                        margin-top: 30px;
                        border: 1px solid rgba(206, 17, 38, 0.3);
                    }
                    
                    .flag {
                        font-size: 2rem;
                        margin: 0 10px;
                    }
                    
                    @media (max-width: 768px) {
                        .container {
                            margin: 20px;
                            padding: 20px;
                        }
                        
                        h1 {
                            font-size: 2.5rem;
                        }
                        
                        .action-buttons {
                            flex-direction: column;
                            align-items: center;
                        }
                        
                        .credential-item {
                            flex-direction: column;
                            gap: 10px;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🇩🇴 SoloYLibre WordPress Multisite</h1>
                    <p class="subtitle">
                        Plataforma de desarrollo web desde San José de Ocoa, República Dominicana
                    </p>
                    
                    <div class="status-indicator">
                        <div class="pulse"></div>
                        <span>Servidor de desarrollo activo</span>
                    </div>
                    
                    <div class="credentials-section">
                        <h3>🔐 Credenciales de Acceso WordPress</h3>
                        
                        <div class="credential-item">
                            <span class="credential-label">🌐 URL de Administración:</span>
                            <span class="credential-value" onclick="copyToClipboard(this)">http://localhost:8090/wp-admin</span>
                        </div>
                        
                        <div class="credential-item">
                            <span class="credential-label">👤 Usuario:</span>
                            <span class="credential-value" onclick="copyToClipboard(this)">josetusabe</span>
                        </div>
                        
                        <div class="credential-item">
                            <span class="credential-label">🔑 Contraseña:</span>
                            <span class="credential-value" onclick="copyToClipboard(this)">JoseTusabe2025!</span>
                        </div>
                        
                        <div class="credential-item">
                            <span class="credential-label">📧 Email:</span>
                            <span class="credential-value" onclick="copyToClipboard(this)"><EMAIL></span>
                        </div>
                        
                        <div class="credential-item">
                            <span class="credential-label">🗄️ Base de Datos:</span>
                            <span class="credential-value">SQLite (Desarrollo)</span>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="/wp-admin" class="btn btn-primary">
                            🚀 Acceder a WordPress
                        </a>
                        <a href="/wp-admin/network" class="btn btn-secondary">
                            🌐 Configurar Multisite
                        </a>
                        <button class="btn btn-secondary" onclick="installFullWordPress()">
                            💻 Instalar WordPress Completo
                        </button>
                    </div>
                    
                    <div class="credentials-section">
                        <h3>🛠️ Configuración del Servidor</h3>
                        
                        <div class="credential-item">
                            <span class="credential-label">🖥️ Servidor:</span>
                            <span class="credential-value">Python HTTP Server</span>
                        </div>
                        
                        <div class="credential-item">
                            <span class="credential-label">🌐 Puerto:</span>
                            <span class="credential-value">8090</span>
                        </div>
                        
                        <div class="credential-item">
                            <span class="credential-label">📁 Directorio:</span>
                            <span class="credential-value">/Users/<USER>/Desktop/SoloYLibre-WordPress</span>
                        </div>
                        
                        <div class="credential-item">
                            <span class="credential-label">🔧 Estado:</span>
                            <span class="credential-value" style="color: #4ECDC4;">✅ Activo</span>
                        </div>
                    </div>
                    
                    <div class="credentials-section">
                        <h3>🌐 Servicios del Ecosistema SoloYLibre</h3>
                        
                        <div class="action-buttons">
                            <a href="http://localhost:8084" target="_blank" class="btn btn-secondary">🎓 Learning Platform</a>
                            <a href="http://localhost:8085" target="_blank" class="btn btn-secondary">🎤 Transcription App</a>
                            <a href="http://localhost:8086" target="_blank" class="btn btn-secondary">🎨 Content Generator</a>
                            <a href="http://localhost:8088" target="_blank" class="btn btn-secondary">📱 Auto Publisher</a>
                            <a href="http://localhost:8089" target="_blank" class="btn btn-secondary">🔧 Debug Console</a>
                        </div>
                    </div>
                    
                    <div class="developer-info">
                        <h3>👨‍💻 Información del Desarrollador</h3>
                        <p><strong>Desarrollador:</strong> Jose L Encarnacion (JoseTusabe)</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Teléfono:</strong> ************</p>
                        <p><strong>Ubicación:</strong> <span class="flag">🇩🇴</span> San José de Ocoa, República Dominicana</p>
                        <p><strong>Empresa:</strong> SoloYLibre Web Dev</p>
                        <p><strong>Especialidad:</strong> Desarrollo de ecosistemas web con IA</p>
                    </div>
                    
                    <div style="margin-top: 30px; opacity: 0.8;">
                        <p>🚀 Para WordPress completo con PHP y MySQL, ejecuta el instalador automático</p>
                        <p>💻 Este servidor de desarrollo te permite probar la configuración básica</p>
                        <p><span class="flag">🇩🇴</span> ¡Dale paisano, que vamos a desarrollar algo brutal!</p>
                    </div>
                </div>
                
                <script>
                    function copyToClipboard(element) {
                        const text = element.textContent;
                        navigator.clipboard.writeText(text).then(() => {
                            element.style.background = 'rgba(76, 205, 196, 0.3)';
                            element.textContent = '✅ Copiado!';
                            setTimeout(() => {
                                element.style.background = 'rgba(0, 0, 0, 0.3)';
                                element.textContent = text;
                            }, 2000);
                        });
                    }
                    
                    function installFullWordPress() {
                        alert('🚀 Para instalar WordPress completo:\\n\\n1. Instala XAMPP o MAMP\\n2. Configura MySQL\\n3. Ejecuta el instalador de WordPress\\n\\n¡Dale paisano, que vamos a configurar todo!');
                    }
                    
                    // Auto-refresh status
                    setInterval(() => {
                        const pulse = document.querySelector('.pulse');
                        pulse.style.background = pulse.style.background === 'rgb(206, 17, 38)' ? '#4ECDC4' : '#CE1126';
                    }, 3000);
                </script>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode('utf-8'))
        else:
            super().do_GET()

def open_browser():
    time.sleep(2)
    webbrowser.open('http://localhost:8090')

def start_server():
    port = 8090
    
    print("🚀 Iniciando SoloYLibre WordPress Server...")
    print("🇩🇴 Desarrollado por Jose L Encarnacion (JoseTusabe)")
    print("🏔️ Desde San José de Ocoa, República Dominicana")
    print(f"🌐 URL: http://localhost:{port}")
    print("=" * 70)
    
    # Open browser in background
    threading.Thread(target=open_browser, daemon=True).start()
    
    with socketserver.TCPServer(("", port), SoloYLibreWordPressHandler) as httpd:
        print(f"✅ Servidor corriendo en http://localhost:{port}")
        print("🔐 Credenciales:")
        print("   Usuario: josetusabe")
        print("   Contraseña: JoseTusabe2025!")
        print("   Email: <EMAIL>")
        print("")
        print("🛑 Presiona Ctrl+C para detener el servidor")
        print("=" * 70)
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Servidor detenido")
            print("¡Dale paisano, que estuvo brutal! 🇩🇴")

if __name__ == '__main__':
    start_server()
