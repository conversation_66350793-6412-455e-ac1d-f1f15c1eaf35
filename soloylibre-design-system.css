:root {
  --color-primary: #667eea;
  --color-secondary: #764ba2;
  --color-accent: #f093fb;
  --color-dominican-red: #CE1126;
  --color-dominican-blue: #002D62;
  --color-mountain-green: #4ECDC4;
  --color-dark: #0f172a;
  --color-gray-900: #1e293b;
  --color-gray-800: #334155;
  --color-gray-700: #475569;
  --color-gray-600: #64748b;
  --color-gray-500: #94a3b8;
  --color-gray-400: #cbd5e1;
  --color-gray-300: #e2e8f0;
  --color-gray-200: #f1f5f9;
  --color-gray-100: #f8fafc;
  --color-white: #ffffff;
  --color-success: #4ade80;
  --color-warning: #fbbf24;
  --color-error: #ef4444;
  --color-info: #0ea5e9;
  --color-glass: rgba(255, 255, 255, 0.1);
  --color-overlay: rgba(0, 0, 0, 0.5);
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --text-7xl: 4.5rem;
  --text-8xl: 6rem;
  --text-9xl: 8rem;
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;
  --space-40: 10rem;
  --space-48: 12rem;
  --space-56: 14rem;
  --space-64: 16rem;
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-none: none;
}

        /* Componentes Base */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-6);
            border: none;
            border-radius: var(--radius-lg);
            font-family: var(--font-family);
            font-size: var(--text-base);
            font-weight: var(--font-semibold);
            text-decoration: none;
            cursor: pointer;
            transition: all 300ms var(--timing-smooth);
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 500ms;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-white);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, var(--color-accent), var(--color-dominican-red));
            color: var(--color-white);
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid var(--color-primary);
            color: var(--color-primary);
        }
        
        .btn-outline:hover {
            background: var(--color-primary);
            color: var(--color-white);
        }
        
        /* Cards */
        .card {
            background: var(--color-white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-lg);
            padding: var(--space-8);
            transition: all 300ms var(--timing-smooth);
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02), rgba(240, 147, 251, 0.02));
            opacity: 0;
            transition: opacity 300ms;
        }
        
        .card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-2xl);
        }
        
        .card:hover::before {
            opacity: 1;
        }
        
        /* Inputs */
        .input {
            width: 100%;
            padding: var(--space-4) var(--space-5);
            border: 2px solid var(--color-gray-300);
            border-radius: var(--radius-xl);
            font-family: var(--font-family);
            font-size: var(--text-base);
            font-weight: var(--font-medium);
            transition: all 300ms var(--timing-smooth);
            background: rgba(255, 255, 255, 0.9);
        }
        
        .input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
            background: var(--color-white);
        }
        
        /* Badges */
        .badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-full);
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
        }
        
        .badge-primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-white);
        }
        
        .badge-success {
            background: var(--color-success);
            color: var(--color-white);
        }
        
        .badge-warning {
            background: var(--color-warning);
            color: var(--color-white);
        }
        
        .badge-error {
            background: var(--color-error);
            color: var(--color-white);
        }
        
        /* Alerts */
        .alert {
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-xl);
            margin-bottom: var(--space-6);
            font-weight: var(--font-medium);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        
        .alert-success {
            background: linear-gradient(135deg, var(--color-success), #22c55e);
            color: var(--color-white);
        }
        
        .alert-warning {
            background: linear-gradient(135deg, var(--color-warning), #f59e0b);
            color: var(--color-white);
        }
        
        .alert-error {
            background: linear-gradient(135deg, var(--color-error), #dc2626);
            color: var(--color-white);
        }
        
        .alert-info {
            background: linear-gradient(135deg, var(--color-info), #0284c7);
            color: var(--color-white);
        }
        
        /* Loading Spinner */
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--color-gray-300);
            border-top: 4px solid var(--color-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Gradients */
        .gradient-primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
        }
        
        .gradient-dominican {
            background: linear-gradient(135deg, var(--color-dominican-red), var(--color-dominican-blue));
        }
        
        .gradient-mountain {
            background: linear-gradient(135deg, var(--color-mountain-green), var(--color-primary));
        }
        
        /* Animations */
        .animate-fade-in {
            animation: fadeIn 500ms var(--timing-smooth);
        }
        
        .animate-slide-up {
            animation: slideUp 600ms var(--timing-smooth);
        }
        
        .animate-bounce {
            animation: bounce 1s var(--timing-bounce) infinite;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0, -30px, 0);
            }
            70% {
                transform: translate3d(0, -15px, 0);
            }
            90% {
                transform: translate3d(0, -4px, 0);
            }
        }
        
        /* Responsive Utilities */
        @media (max-width: 768px) {
            .mobile-hidden { display: none; }
            .mobile-full { width: 100%; }
            .mobile-stack { flex-direction: column; }
        }
        
        @media (min-width: 769px) {
            .desktop-hidden { display: none; }
        }
        
        /* Dominican Flag Animation */
        .flag-wave {
            animation: wave 3s ease-in-out infinite;
        }
        
        @keyframes wave {
            0%, 100% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(-10deg) scale(1.1); }
            75% { transform: rotate(10deg) scale(1.1); }
        }
        