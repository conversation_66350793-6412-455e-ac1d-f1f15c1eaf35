#!/usr/bin/env python3
"""
SoloYLibre Design System & UX/UI Components
Sistema de diseño completo y componentes reutilizables
Desarrollado por Jose L Encarnacion (JoseTusabe)
Desde San José de <PERSON>, República Dominicana 🇩🇴
"""

class SoloYLibreDesignSystem:
    def __init__(self):
        self.colors = {
            # Paleta principal inspirada en la bandera dominicana
            'primary': '#667eea',      # Azul tecnológico
            'secondary': '#764ba2',    # Púrpura elegante
            'accent': '#f093fb',       # Rosa vibrante
            'dominican_red': '#CE1126', # Rojo de la bandera
            'dominican_blue': '#002D62', # Azul de la bandera
            'mountain_green': '#4ECDC4', # Verde de las montañas
            
            # Paleta de grises
            'dark': '#0f172a',
            'gray_900': '#1e293b',
            'gray_800': '#334155',
            'gray_700': '#475569',
            'gray_600': '#64748b',
            'gray_500': '#94a3b8',
            'gray_400': '#cbd5e1',
            'gray_300': '#e2e8f0',
            'gray_200': '#f1f5f9',
            'gray_100': '#f8fafc',
            'white': '#ffffff',
            
            # Estados
            'success': '#4ade80',
            'warning': '#fbbf24',
            'error': '#ef4444',
            'info': '#0ea5e9',
            
            # Transparencias
            'glass': 'rgba(255, 255, 255, 0.1)',
            'overlay': 'rgba(0, 0, 0, 0.5)',
        }
        
        self.typography = {
            'font_family': "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
            'font_sizes': {
                'xs': '0.75rem',    # 12px
                'sm': '0.875rem',   # 14px
                'base': '1rem',     # 16px
                'lg': '1.125rem',   # 18px
                'xl': '1.25rem',    # 20px
                '2xl': '1.5rem',    # 24px
                '3xl': '1.875rem',  # 30px
                '4xl': '2.25rem',   # 36px
                '5xl': '3rem',      # 48px
                '6xl': '3.75rem',   # 60px
                '7xl': '4.5rem',    # 72px
                '8xl': '6rem',      # 96px
                '9xl': '8rem',      # 128px
            },
            'font_weights': {
                'thin': '100',
                'extralight': '200',
                'light': '300',
                'normal': '400',
                'medium': '500',
                'semibold': '600',
                'bold': '700',
                'extrabold': '800',
                'black': '900',
            },
            'line_heights': {
                'none': '1',
                'tight': '1.25',
                'snug': '1.375',
                'normal': '1.5',
                'relaxed': '1.625',
                'loose': '2',
            }
        }
        
        self.spacing = {
            '0': '0',
            '1': '0.25rem',   # 4px
            '2': '0.5rem',    # 8px
            '3': '0.75rem',   # 12px
            '4': '1rem',      # 16px
            '5': '1.25rem',   # 20px
            '6': '1.5rem',    # 24px
            '8': '2rem',      # 32px
            '10': '2.5rem',   # 40px
            '12': '3rem',     # 48px
            '16': '4rem',     # 64px
            '20': '5rem',     # 80px
            '24': '6rem',     # 96px
            '32': '8rem',     # 128px
            '40': '10rem',    # 160px
            '48': '12rem',    # 192px
            '56': '14rem',    # 224px
            '64': '16rem',    # 256px
        }
        
        self.border_radius = {
            'none': '0',
            'sm': '0.125rem',   # 2px
            'base': '0.25rem',  # 4px
            'md': '0.375rem',   # 6px
            'lg': '0.5rem',     # 8px
            'xl': '0.75rem',    # 12px
            '2xl': '1rem',      # 16px
            '3xl': '1.5rem',    # 24px
            'full': '9999px',
        }
        
        self.shadows = {
            'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            'base': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
            'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
            'inner': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
            'none': 'none',
        }
        
        self.animations = {
            'duration': {
                '75': '75ms',
                '100': '100ms',
                '150': '150ms',
                '200': '200ms',
                '300': '300ms',
                '500': '500ms',
                '700': '700ms',
                '1000': '1000ms',
            },
            'timing': {
                'linear': 'linear',
                'ease': 'ease',
                'ease_in': 'ease-in',
                'ease_out': 'ease-out',
                'ease_in_out': 'ease-in-out',
                'bounce': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
                'smooth': 'cubic-bezier(0.4, 0, 0.2, 1)',
            }
        }
        
    def get_css_variables(self):
        """Generar variables CSS del sistema de diseño"""
        css_vars = ":root {\n"
        
        # Colores
        for name, value in self.colors.items():
            css_vars += f"  --color-{name.replace('_', '-')}: {value};\n"
        
        # Tipografía
        css_vars += f"  --font-family: {self.typography['font_family']};\n"
        
        for size_name, size_value in self.typography['font_sizes'].items():
            css_vars += f"  --text-{size_name}: {size_value};\n"
        
        for weight_name, weight_value in self.typography['font_weights'].items():
            css_vars += f"  --font-{weight_name}: {weight_value};\n"
        
        # Espaciado
        for space_name, space_value in self.spacing.items():
            css_vars += f"  --space-{space_name}: {space_value};\n"
        
        # Border radius
        for radius_name, radius_value in self.border_radius.items():
            css_vars += f"  --radius-{radius_name}: {radius_value};\n"
        
        # Sombras
        for shadow_name, shadow_value in self.shadows.items():
            css_vars += f"  --shadow-{shadow_name}: {shadow_value};\n"
        
        css_vars += "}\n"
        return css_vars
        
    def get_component_styles(self):
        """Generar estilos de componentes reutilizables"""
        return """
        /* Componentes Base */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-6);
            border: none;
            border-radius: var(--radius-lg);
            font-family: var(--font-family);
            font-size: var(--text-base);
            font-weight: var(--font-semibold);
            text-decoration: none;
            cursor: pointer;
            transition: all 300ms var(--timing-smooth);
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 500ms;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-white);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, var(--color-accent), var(--color-dominican-red));
            color: var(--color-white);
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid var(--color-primary);
            color: var(--color-primary);
        }
        
        .btn-outline:hover {
            background: var(--color-primary);
            color: var(--color-white);
        }
        
        /* Cards */
        .card {
            background: var(--color-white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-lg);
            padding: var(--space-8);
            transition: all 300ms var(--timing-smooth);
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02), rgba(240, 147, 251, 0.02));
            opacity: 0;
            transition: opacity 300ms;
        }
        
        .card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-2xl);
        }
        
        .card:hover::before {
            opacity: 1;
        }
        
        /* Inputs */
        .input {
            width: 100%;
            padding: var(--space-4) var(--space-5);
            border: 2px solid var(--color-gray-300);
            border-radius: var(--radius-xl);
            font-family: var(--font-family);
            font-size: var(--text-base);
            font-weight: var(--font-medium);
            transition: all 300ms var(--timing-smooth);
            background: rgba(255, 255, 255, 0.9);
        }
        
        .input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
            background: var(--color-white);
        }
        
        /* Badges */
        .badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-full);
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
        }
        
        .badge-primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-white);
        }
        
        .badge-success {
            background: var(--color-success);
            color: var(--color-white);
        }
        
        .badge-warning {
            background: var(--color-warning);
            color: var(--color-white);
        }
        
        .badge-error {
            background: var(--color-error);
            color: var(--color-white);
        }
        
        /* Alerts */
        .alert {
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-xl);
            margin-bottom: var(--space-6);
            font-weight: var(--font-medium);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        
        .alert-success {
            background: linear-gradient(135deg, var(--color-success), #22c55e);
            color: var(--color-white);
        }
        
        .alert-warning {
            background: linear-gradient(135deg, var(--color-warning), #f59e0b);
            color: var(--color-white);
        }
        
        .alert-error {
            background: linear-gradient(135deg, var(--color-error), #dc2626);
            color: var(--color-white);
        }
        
        .alert-info {
            background: linear-gradient(135deg, var(--color-info), #0284c7);
            color: var(--color-white);
        }
        
        /* Loading Spinner */
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--color-gray-300);
            border-top: 4px solid var(--color-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Gradients */
        .gradient-primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
        }
        
        .gradient-dominican {
            background: linear-gradient(135deg, var(--color-dominican-red), var(--color-dominican-blue));
        }
        
        .gradient-mountain {
            background: linear-gradient(135deg, var(--color-mountain-green), var(--color-primary));
        }
        
        /* Animations */
        .animate-fade-in {
            animation: fadeIn 500ms var(--timing-smooth);
        }
        
        .animate-slide-up {
            animation: slideUp 600ms var(--timing-smooth);
        }
        
        .animate-bounce {
            animation: bounce 1s var(--timing-bounce) infinite;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0, -30px, 0);
            }
            70% {
                transform: translate3d(0, -15px, 0);
            }
            90% {
                transform: translate3d(0, -4px, 0);
            }
        }
        
        /* Responsive Utilities */
        @media (max-width: 768px) {
            .mobile-hidden { display: none; }
            .mobile-full { width: 100%; }
            .mobile-stack { flex-direction: column; }
        }
        
        @media (min-width: 769px) {
            .desktop-hidden { display: none; }
        }
        
        /* Dominican Flag Animation */
        .flag-wave {
            animation: wave 3s ease-in-out infinite;
        }
        
        @keyframes wave {
            0%, 100% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(-10deg) scale(1.1); }
            75% { transform: rotate(10deg) scale(1.1); }
        }
        """
        
    def get_complete_css(self):
        """Obtener CSS completo del sistema de diseño"""
        return self.get_css_variables() + self.get_component_styles()

# Crear instancia del sistema de diseño
design_system = SoloYLibreDesignSystem()

if __name__ == '__main__':
    # Generar archivo CSS del sistema de diseño
    css_content = design_system.get_complete_css()
    
    with open('/Users/<USER>/Desktop/SoloYLibre-WordPress/soloylibre-design-system.css', 'w') as f:
        f.write(css_content)
    
    print("🎨 Sistema de diseño SoloYLibre generado exitosamente!")
    print("📁 Archivo: soloylibre-design-system.css")
