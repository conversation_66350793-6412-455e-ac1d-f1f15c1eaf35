#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate Database System
Sistema de base de datos completo y optimizado
Desarrollado por <PERSON>nacion (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import sqlite3
import json
import hashlib
import uuid
from datetime import datetime, timedelta
import os

class SoloYLibreDatabase:
    def __init__(self, db_path):
        self.db_path = db_path
        self.init_database()
        
    def init_database(self):
        """Inicializar base de datos con todas las tablas necesarias"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tabla de usuarios con roles y permisos
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_users (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                user_login VARCHAR(60) NOT NULL UNIQUE,
                user_pass VARCHAR(255) NOT NULL,
                user_nicename VARCHAR(50) NOT NULL,
                user_email VARCHAR(100) NOT NULL UNIQUE,
                user_url VARCHAR(100) DEFAULT '',
                user_registered DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_activation_key VARCHAR(255) DEFAULT '',
                user_status INTEGER DEFAULT 0,
                display_name VARCHAR(250) NOT NULL,
                user_role VARCHAR(50) DEFAULT 'subscriber',
                two_factor_secret VARCHAR(32) DEFAULT '',
                last_login DATETIME,
                login_attempts INTEGER DEFAULT 0,
                locked_until DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Tabla de posts y páginas mejorada
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_posts (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                post_author INTEGER NOT NULL,
                post_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                post_date_gmt DATETIME DEFAULT CURRENT_TIMESTAMP,
                post_content TEXT NOT NULL DEFAULT '',
                post_title TEXT NOT NULL DEFAULT '',
                post_excerpt TEXT DEFAULT '',
                post_status VARCHAR(20) DEFAULT 'draft',
                comment_status VARCHAR(20) DEFAULT 'open',
                ping_status VARCHAR(20) DEFAULT 'open',
                post_password VARCHAR(255) DEFAULT '',
                post_name VARCHAR(200) NOT NULL DEFAULT '',
                to_ping TEXT DEFAULT '',
                pinged TEXT DEFAULT '',
                post_modified DATETIME DEFAULT CURRENT_TIMESTAMP,
                post_modified_gmt DATETIME DEFAULT CURRENT_TIMESTAMP,
                post_content_filtered TEXT DEFAULT '',
                post_parent INTEGER DEFAULT 0,
                guid VARCHAR(255) DEFAULT '',
                menu_order INTEGER DEFAULT 0,
                post_type VARCHAR(20) DEFAULT 'post',
                post_mime_type VARCHAR(100) DEFAULT '',
                comment_count INTEGER DEFAULT 0,
                featured_image VARCHAR(255) DEFAULT '',
                seo_title VARCHAR(255) DEFAULT '',
                seo_description TEXT DEFAULT '',
                seo_keywords TEXT DEFAULT '',
                view_count INTEGER DEFAULT 0,
                likes_count INTEGER DEFAULT 0,
                shares_count INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (post_author) REFERENCES wp_users(ID)
            )
        """)
        
        # Tabla de metadatos de posts
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_postmeta (
                meta_id INTEGER PRIMARY KEY AUTOINCREMENT,
                post_id INTEGER NOT NULL,
                meta_key VARCHAR(255),
                meta_value TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (post_id) REFERENCES wp_posts(ID) ON DELETE CASCADE
            )
        """)
        
        # Tabla de comentarios mejorada
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_comments (
                comment_ID INTEGER PRIMARY KEY AUTOINCREMENT,
                comment_post_ID INTEGER NOT NULL,
                comment_author TEXT NOT NULL DEFAULT '',
                comment_author_email VARCHAR(100) DEFAULT '',
                comment_author_url VARCHAR(200) DEFAULT '',
                comment_author_IP VARCHAR(100) DEFAULT '',
                comment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                comment_date_gmt DATETIME DEFAULT CURRENT_TIMESTAMP,
                comment_content TEXT NOT NULL DEFAULT '',
                comment_karma INTEGER DEFAULT 0,
                comment_approved VARCHAR(20) DEFAULT '1',
                comment_agent VARCHAR(255) DEFAULT '',
                comment_type VARCHAR(20) DEFAULT 'comment',
                comment_parent INTEGER DEFAULT 0,
                user_id INTEGER DEFAULT 0,
                is_spam BOOLEAN DEFAULT FALSE,
                sentiment_score REAL DEFAULT 0.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (comment_post_ID) REFERENCES wp_posts(ID) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES wp_users(ID)
            )
        """)
        
        # Tabla de opciones del sistema
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_options (
                option_id INTEGER PRIMARY KEY AUTOINCREMENT,
                option_name VARCHAR(191) NOT NULL UNIQUE,
                option_value TEXT,
                autoload VARCHAR(20) DEFAULT 'yes',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Tabla de medios y archivos
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_media (
                media_id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_url VARCHAR(500) NOT NULL,
                file_type VARCHAR(100) NOT NULL,
                file_size INTEGER NOT NULL,
                mime_type VARCHAR(100) NOT NULL,
                width INTEGER DEFAULT 0,
                height INTEGER DEFAULT 0,
                alt_text TEXT DEFAULT '',
                caption TEXT DEFAULT '',
                description TEXT DEFAULT '',
                uploaded_by INTEGER NOT NULL,
                upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_featured BOOLEAN DEFAULT FALSE,
                download_count INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (uploaded_by) REFERENCES wp_users(ID)
            )
        """)
        
        # Tabla de categorías y taxonomías
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_terms (
                term_id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(200) NOT NULL,
                slug VARCHAR(200) NOT NULL UNIQUE,
                term_group INTEGER DEFAULT 0,
                description TEXT DEFAULT '',
                parent_id INTEGER DEFAULT 0,
                taxonomy VARCHAR(32) NOT NULL,
                count INTEGER DEFAULT 0,
                color VARCHAR(7) DEFAULT '#667eea',
                icon VARCHAR(50) DEFAULT '',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Tabla de relaciones entre posts y términos
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_term_relationships (
                object_id INTEGER NOT NULL,
                term_taxonomy_id INTEGER NOT NULL,
                term_order INTEGER DEFAULT 0,
                PRIMARY KEY (object_id, term_taxonomy_id),
                FOREIGN KEY (object_id) REFERENCES wp_posts(ID) ON DELETE CASCADE,
                FOREIGN KEY (term_taxonomy_id) REFERENCES wp_terms(term_id) ON DELETE CASCADE
            )
        """)
        
        # Tabla de menús de navegación
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_nav_menu_items (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                menu_item_parent INTEGER DEFAULT 0,
                object_id INTEGER DEFAULT 0,
                object VARCHAR(100) DEFAULT '',
                type VARCHAR(20) DEFAULT '',
                type_label VARCHAR(200) DEFAULT '',
                url VARCHAR(255) DEFAULT '',
                title TEXT DEFAULT '',
                target VARCHAR(25) DEFAULT '',
                attr_title TEXT DEFAULT '',
                description TEXT DEFAULT '',
                classes TEXT DEFAULT '',
                xfn TEXT DEFAULT '',
                menu_order INTEGER DEFAULT 0,
                menu_location VARCHAR(50) DEFAULT '',
                icon VARCHAR(50) DEFAULT '',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Tabla de configuraciones de tema
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_theme_settings (
                setting_id INTEGER PRIMARY KEY AUTOINCREMENT,
                theme_name VARCHAR(100) NOT NULL,
                setting_key VARCHAR(100) NOT NULL,
                setting_value TEXT,
                setting_type VARCHAR(50) DEFAULT 'text',
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(theme_name, setting_key)
            )
        """)
        
        # Tabla de analytics y métricas
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_analytics (
                analytics_id INTEGER PRIMARY KEY AUTOINCREMENT,
                event_type VARCHAR(50) NOT NULL,
                object_type VARCHAR(50) NOT NULL,
                object_id INTEGER,
                user_id INTEGER,
                session_id VARCHAR(100),
                ip_address VARCHAR(45),
                user_agent TEXT,
                referrer VARCHAR(500),
                page_url VARCHAR(500),
                event_data TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES wp_users(ID),
                FOREIGN KEY (object_id) REFERENCES wp_posts(ID)
            )
        """)
        
        # Tabla de logs del sistema
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_system_logs (
                log_id INTEGER PRIMARY KEY AUTOINCREMENT,
                log_level VARCHAR(20) NOT NULL,
                log_category VARCHAR(50) NOT NULL,
                message TEXT NOT NULL,
                context TEXT,
                user_id INTEGER,
                ip_address VARCHAR(45),
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES wp_users(ID)
            )
        """)
        
        # Tabla de sesiones de usuario
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_user_sessions (
                session_id VARCHAR(100) PRIMARY KEY,
                user_id INTEGER NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                login_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                FOREIGN KEY (user_id) REFERENCES wp_users(ID) ON DELETE CASCADE
            )
        """)
        
        # Tabla de notificaciones
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wp_notifications (
                notification_id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                type VARCHAR(50) DEFAULT 'info',
                is_read BOOLEAN DEFAULT FALSE,
                action_url VARCHAR(500),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                read_at DATETIME,
                FOREIGN KEY (user_id) REFERENCES wp_users(ID) ON DELETE CASCADE
            )
        """)
        
        # Crear índices para optimización
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_posts_status ON wp_posts(post_status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_posts_type ON wp_posts(post_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_posts_date ON wp_posts(post_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_posts_author ON wp_posts(post_author)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_comments_post ON wp_comments(comment_post_ID)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_analytics_type ON wp_analytics(event_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_analytics_timestamp ON wp_analytics(timestamp)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_user ON wp_user_sessions(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_active ON wp_user_sessions(is_active)")
        
        conn.commit()
        conn.close()
        
    def create_admin_user(self):
        """Crear usuario administrador principal"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Hash de la contraseña
        password_hash = hashlib.sha256("JoseTusabe2025!".encode()).hexdigest()
        
        cursor.execute("""
            INSERT OR REPLACE INTO wp_users 
            (ID, user_login, user_pass, user_nicename, user_email, user_url, 
             display_name, user_role, created_at, updated_at)
            VALUES (1, 'josetusabe', ?, 'josetusabe', '<EMAIL>', 
                    'https://soloylibre.com', 'Jose L Encarnacion (JoseTusabe)', 
                    'administrator', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """, (password_hash,))
        
        conn.commit()
        conn.close()
        
    def populate_default_content(self):
        """Poblar contenido por defecto"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Opciones del sistema
        default_options = [
            ('siteurl', 'http://localhost:8080'),
            ('home', 'http://localhost:8080'),
            ('blogname', 'SoloYLibre WordPress Ultimate'),
            ('blogdescription', 'Desarrollo web profesional desde San José de Ocoa, República Dominicana'),
            ('admin_email', '<EMAIL>'),
            ('users_can_register', '0'),
            ('default_role', 'subscriber'),
            ('timezone_string', 'America/Santo_Domingo'),
            ('date_format', 'd/m/Y'),
            ('time_format', 'H:i'),
            ('start_of_week', '1'),
            ('template', 'soloylibre-ultimate'),
            ('stylesheet', 'soloylibre-ultimate'),
            ('permalink_structure', '/%postname%/'),
            ('active_plugins', '[]'),
            ('company_name', 'SoloYLibre Web Dev'),
            ('company_location', 'San José de Ocoa, República Dominicana'),
            ('company_phone', '************'),
            ('company_email', '<EMAIL>'),
            ('developer_name', 'Jose L Encarnacion (JoseTusabe)'),
            ('developer_nickname', 'JoseTusabe'),
            ('website_soloylibre', 'soloylibre.com'),
            ('website_josetusabe', 'josetusabe.com'),
            ('website_photography', '1and1photo.com'),
            ('website_portfolio', 'joselencarnacion.com'),
            ('server_info', 'Synology RS3618xs - 56GB RAM - 36TB Storage'),
            ('seo_enabled', '1'),
            ('analytics_enabled', '1'),
            ('security_2fa_enabled', '1'),
            ('maintenance_mode', '0'),
            ('cache_enabled', '1'),
            ('cdn_enabled', '0'),
            ('backup_enabled', '1'),
            ('ssl_enabled', '1'),
            ('compression_enabled', '1')
        ]
        
        for option_name, option_value in default_options:
            cursor.execute("""
                INSERT OR REPLACE INTO wp_options (option_name, option_value, autoload)
                VALUES (?, ?, 'yes')
            """, (option_name, option_value))
        
        # Configuraciones de tema por defecto
        theme_settings = [
            ('soloylibre-ultimate', 'primary_color', '#667eea', 'color'),
            ('soloylibre-ultimate', 'secondary_color', '#764ba2', 'color'),
            ('soloylibre-ultimate', 'accent_color', '#f093fb', 'color'),
            ('soloylibre-ultimate', 'background_color', '#f8f9fa', 'color'),
            ('soloylibre-ultimate', 'text_color', '#333333', 'color'),
            ('soloylibre-ultimate', 'header_style', 'gradient', 'select'),
            ('soloylibre-ultimate', 'layout_style', 'modern', 'select'),
            ('soloylibre-ultimate', 'animations_enabled', '1', 'boolean'),
            ('soloylibre-ultimate', 'dark_mode_enabled', '0', 'boolean'),
            ('soloylibre-ultimate', 'custom_css', '', 'textarea'),
            ('soloylibre-ultimate', 'custom_js', '', 'textarea'),
            ('soloylibre-ultimate', 'logo_url', '', 'url'),
            ('soloylibre-ultimate', 'favicon_url', '', 'url'),
            ('soloylibre-ultimate', 'social_facebook', '', 'url'),
            ('soloylibre-ultimate', 'social_twitter', '', 'url'),
            ('soloylibre-ultimate', 'social_instagram', '', 'url'),
            ('soloylibre-ultimate', 'social_linkedin', '', 'url'),
            ('soloylibre-ultimate', 'social_youtube', '', 'url'),
            ('soloylibre-ultimate', 'contact_form_enabled', '1', 'boolean'),
            ('soloylibre-ultimate', 'newsletter_enabled', '1', 'boolean')
        ]
        
        for theme_name, setting_key, setting_value, setting_type in theme_settings:
            cursor.execute("""
                INSERT OR REPLACE INTO wp_theme_settings 
                (theme_name, setting_key, setting_value, setting_type)
                VALUES (?, ?, ?, ?)
            """, (theme_name, setting_key, setting_value, setting_type))
        
        # Categorías por defecto
        default_categories = [
            ('General', 'general', 'Artículos generales', 0, 'category'),
            ('Tecnología', 'tecnologia', 'Artículos sobre tecnología y desarrollo', 0, 'category'),
            ('San José de Ocoa', 'san-jose-de-ocoa', 'Contenido sobre nuestra hermosa provincia', 0, 'category'),
            ('República Dominicana', 'republica-dominicana', 'Contenido sobre nuestro país', 0, 'category'),
            ('Desarrollo Web', 'desarrollo-web', 'Artículos sobre desarrollo web', 0, 'category'),
            ('SoloYLibre', 'soloylibre', 'Contenido de la empresa SoloYLibre', 0, 'category')
        ]
        
        for name, slug, description, parent_id, taxonomy in default_categories:
            cursor.execute("""
                INSERT OR REPLACE INTO wp_terms 
                (name, slug, description, parent_id, taxonomy)
                VALUES (?, ?, ?, ?, ?)
            """, (name, slug, description, parent_id, taxonomy))
        
        conn.commit()
        conn.close()
        
    def setup_complete_database(self):
        """Configurar base de datos completa"""
        self.create_admin_user()
        self.populate_default_content()
        print("✅ Base de datos configurada completamente")

if __name__ == '__main__':
    db_path = "/Users/<USER>/Desktop/SoloYLibre-WordPress/soloylibre_ultimate.db"
    db = SoloYLibreDatabase(db_path)
    db.setup_complete_database()
    print("🇩🇴 Base de datos SoloYLibre Ultimate lista!")
