#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate Complete Server
Servidor principal que integra todos los módulos
Desarrollado por <PERSON> (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import os
import sys
import time
import threading
import webbrowser
import http.server
import socketserver
import json
from datetime import datetime
from urllib.parse import urlparse, parse_qs, unquote

# Agregar el directorio actual al path para importar módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar módulos del sistema
from core.soloylibre_core import SoloYLibreCore
from modules.auth_security import AuthenticationManager
from modules.content_manager import ContentManager
from templates.admin_login import render_admin_login
from templates.admin_dashboard import render_admin_dashboard
from templates.backend_page import render_backend_page
from templates.frontend_page import render_frontend_page
from templates.admin_posts import render_admin_posts
from modules.wordpress_core import WordPressCore

class SoloYLibreUltimateServer:
    """Servidor principal del sistema WordPress Ultimate"""
    
    def __init__(self):
        self.port = 8080
        self.host = 'localhost'
        self.wordpress_dir = '/Users/<USER>/Desktop/SoloYLibre-WordPress'
        
        # Configuración del sistema
        config = {
            'db_file': os.path.join(self.wordpress_dir, "soloylibre_ultimate.db"),
            'design_css_file': os.path.join(self.wordpress_dir, "soloylibre-design-system.css"),
            'upload_dir': os.path.join(self.wordpress_dir, "uploads"),
            'cache_dir': os.path.join(self.wordpress_dir, "cache"),
            'logs_dir': os.path.join(self.wordpress_dir, "logs")
        }
        
        # Inicializar módulos del sistema
        self.core = SoloYLibreCore(config)
        self.auth = AuthenticationManager(self.core)
        self.content = ContentManager(self.core)
        self.wordpress = WordPressCore(self.core)
        
        # Crear directorios necesarios
        self.create_directories()
        
        # Configurar sistema
        self.setup_system()
        
    def create_directories(self):
        """Crear directorios necesarios"""
        directories = [
            self.wordpress_dir,
            os.path.join(self.wordpress_dir, "uploads"),
            os.path.join(self.wordpress_dir, "cache"),
            os.path.join(self.wordpress_dir, "logs"),
            os.path.join(self.wordpress_dir, "core"),
            os.path.join(self.wordpress_dir, "modules"),
            os.path.join(self.wordpress_dir, "templates"),
            os.path.join(self.wordpress_dir, "assets")
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def setup_system(self):
        """Configurar sistema inicial"""
        try:
            # Configurar opciones por defecto
            default_options = {
                'site_title': 'SoloYLibre WordPress Ultimate',
                'site_description': 'Sistema WordPress profesional desde San José de Ocoa, República Dominicana',
                'admin_email': '<EMAIL>',
                'timezone': 'America/Santo_Domingo',
                'date_format': 'd/m/Y',
                'time_format': 'H:i',
                'language': 'es_DO',
                'theme': 'soloylibre-ultimate',
                'posts_per_page': 10,
                'comments_enabled': True,
                'registration_enabled': False,
                'default_role': 'subscriber',
                'permalink_structure': '/%postname%/',
                'maintenance_mode': False,
                'cache_enabled': True,
                'analytics_enabled': True,
                'security_enabled': True,
                'backup_enabled': True,
                'ssl_enabled': True,
                'compression_enabled': True,
                'cdn_enabled': False
            }
            
            for option, value in default_options.items():
                if self.core.get_option(option) is None:
                    self.core.set_option(option, value)
            
            # Log inicio del sistema
            self.core.log_info("Sistema SoloYLibre WordPress Ultimate iniciado", {
                'version': self.core.version,
                'build': self.core.build,
                'port': self.port
            })
            
        except Exception as e:
            self.core.log_error(f"Error setting up system: {e}")
    
    def load_design_system_css(self):
        """Cargar CSS del sistema de diseño"""
        try:
            css_file = os.path.join(self.wordpress_dir, "soloylibre-design-system.css")
            if os.path.exists(css_file):
                with open(css_file, 'r', encoding='utf-8') as f:
                    return f.read()
        except Exception as e:
            self.core.log_error(f"Error loading design CSS: {e}")
        
        return ""
    
    def start_server(self):
        """Iniciar servidor principal"""
        self.core.print_system_header()
        
        class WordPressUltimateHandler(http.server.SimpleHTTPRequestHandler):
            def __init__(self, *args, **kwargs):
                self.server_instance = kwargs.pop('server_instance', None)
                super().__init__(*args, directory=self.server_instance.wordpress_dir, **kwargs)
            
            def log_request(self, code='-', size='-'):
                """Log personalizado de requests"""
                self.server_instance.core.increment_metric('page_views')
                client_ip = self.client_address[0]
                self.server_instance.core.add_unique_visitor(client_ip)
                
                # Log analytics
                self.server_instance.core.log_analytics_event('page_view', data={
                    'path': self.path,
                    'ip': client_ip,
                    'user_agent': self.headers.get('User-Agent', ''),
                    'referer': self.headers.get('Referer', ''),
                    'status_code': code
                })
            
            def do_GET(self):
                """Manejar requests GET"""
                try:
                    # Rutas principales del sistema
                    if self.path == '/' or self.path == '/index.php':
                        self.serve_frontend_page()
                    elif self.path == '/backend':
                        self.serve_backend_page()
                    elif self.path == '/soloylibre-admin' or self.path == '/soloylibre-admin/':
                        self.handle_admin_access()
                    elif self.path == '/dashboard':
                        self.serve_admin_dashboard()
                    elif self.path.startswith('/soloylibre-admin/'):
                        self.handle_admin_page()
                    elif self.path.startswith('/api/'):
                        self.handle_api_request()
                    elif self.path == '/manifest.json':
                        self.serve_pwa_manifest()
                    elif self.path == '/sw.js':
                        self.serve_service_worker()
                    elif self.path == '/logout':
                        self.handle_logout()
                    elif self.path.startswith('/post/'):
                        self.serve_single_post()
                    elif self.path.startswith('/page/'):
                        self.serve_single_page()
                    elif self.path.startswith('/category/'):
                        self.serve_category()
                    elif self.path.startswith('/tag/'):
                        self.serve_tag()
                    else:
                        # Intentar servir archivo estático
                        super().do_GET()
                        
                except Exception as e:
                    self.server_instance.core.log_error(f"Error handling GET request: {e}")
                    self.send_error(500, "Internal Server Error")
            
            def do_POST(self):
                """Manejar requests POST"""
                try:
                    if self.path == '/soloylibre-admin' or self.path == '/api/auth/login':
                        self.handle_login()
                    elif self.path.startswith('/api/'):
                        self.handle_api_request()
                    elif self.path.startswith('/soloylibre-admin/'):
                        self.handle_admin_post()
                    else:
                        self.send_error(404, "Not Found")
                        
                except Exception as e:
                    self.server_instance.core.log_error(f"Error handling POST request: {e}")
                    self.send_error(500, "Internal Server Error")
            
            def get_current_user(self):
                """Obtener usuario actual de la sesión"""
                try:
                    # Buscar token en cookies
                    cookie_header = self.headers.get('Cookie', '')
                    if 'soloylibre_token=' in cookie_header:
                        for cookie in cookie_header.split(';'):
                            if 'soloylibre_token=' in cookie:
                                token = cookie.split('soloylibre_token=')[1].strip()
                                payload = self.server_instance.core.verify_jwt_token(token)
                                if payload:
                                    return self.server_instance.auth.get_user_by_id(payload['user_id'])
                    
                    # Buscar en header Authorization
                    auth_header = self.headers.get('Authorization', '')
                    if auth_header.startswith('Bearer '):
                        token = auth_header[7:]
                        payload = self.server_instance.core.verify_jwt_token(token)
                        if payload:
                            return self.server_instance.auth.get_user_by_id(payload['user_id'])
                    
                    return None
                except Exception as e:
                    self.server_instance.core.log_error(f"Error getting current user: {e}")
                    return None
            
            def handle_admin_access(self):
                """Manejar acceso al admin"""
                user = self.get_current_user()
                if user:
                    self.serve_admin_dashboard()
                else:
                    self.serve_admin_login()
            
            def handle_login(self):
                """Manejar proceso de login"""
                try:
                    content_length = int(self.headers.get('Content-Length', 0))
                    if content_length > 0:
                        post_data = self.rfile.read(content_length).decode('utf-8')
                        params = parse_qs(post_data)
                        
                        username = params.get('log', [''])[0]
                        password = params.get('pwd', [''])[0]
                        
                        client_ip = self.client_address[0]
                        user_agent = self.headers.get('User-Agent', '')
                        
                        # Autenticar usuario
                        user_data = self.server_instance.auth.authenticate_user(
                            username, password, client_ip
                        )
                        
                        if user_data:
                            # Generar JWT token
                            token = self.server_instance.core.generate_jwt_token(
                                user_data['id'], user_data['username'], user_data['role']
                            )
                            
                            # Respuesta exitosa
                            self.send_response(302)
                            self.send_header('Location', '/dashboard')
                            self.send_header('Set-Cookie', 
                                f'soloylibre_token={token}; Path=/; HttpOnly; SameSite=Strict; Max-Age=86400')
                            self.end_headers()
                        else:
                            self.serve_admin_login(error="❌ Credenciales incorrectas")
                    else:
                        self.serve_admin_login(error="❌ Datos de login requeridos")
                        
                except Exception as e:
                    self.server_instance.core.log_error(f"Login error: {e}")
                    self.serve_admin_login(error="❌ Error del sistema")
            
            def handle_logout(self):
                """Manejar logout"""
                self.send_response(302)
                self.send_header('Location', '/')
                self.send_header('Set-Cookie', 
                    'soloylibre_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT')
                self.end_headers()
            
            def handle_api_request(self):
                """Manejar requests de API REST"""
                try:
                    # Parsear ruta de API
                    path_parts = self.path.strip('/').split('/')

                    # Si solo es /api/ sin endpoint específico, mostrar documentación
                    if len(path_parts) <= 2 or (len(path_parts) == 2 and path_parts[1] == 'api'):
                        endpoint = ''
                    else:
                        endpoint = '/'.join(path_parts[2:]) if len(path_parts) > 2 else ''
                    
                    # Verificar autenticación para endpoints protegidos
                    user = self.get_current_user()
                    
                    # Manejar diferentes endpoints
                    if endpoint == 'stats':
                        self.serve_api_stats()
                    elif endpoint == 'posts':
                        self.serve_api_posts()
                    elif endpoint == 'health':
                        self.serve_api_health()
                    elif endpoint == '' or endpoint == '/':
                        # API principal sin endpoint específico
                        self.send_api_response({
                            'status': 'success',
                            'message': 'SoloYLibre WordPress Ultimate API',
                            'version': self.server_instance.core.version,
                            'endpoints': [
                                '/api/health',
                                '/api/stats',
                                '/api/posts',
                                '/api/auth/login'
                            ],
                            'developer': self.server_instance.core.company_info['developer'],
                            'company': self.server_instance.core.company_info['name'],
                            'location': self.server_instance.core.company_info['location']
                        })
                    else:
                        self.send_api_response({
                            'status': 'success',
                            'message': 'SoloYLibre WordPress Ultimate API',
                            'version': self.server_instance.core.version,
                            'endpoints': [
                                '/api/health',
                                '/api/stats',
                                '/api/posts',
                                '/api/auth/login'
                            ],
                            'developer': self.server_instance.core.company_info['developer'],
                            'company': self.server_instance.core.company_info['name'],
                            'location': self.server_instance.core.company_info['location']
                        })
                        
                except Exception as e:
                    self.server_instance.core.log_error(f"API error: {e}")
                    self.send_api_error(500, "Internal Server Error")
            
            def send_api_response(self, data, status_code=200):
                """Enviar respuesta de API"""
                self.send_response(status_code)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
                self.end_headers()
                
                response = {
                    'timestamp': datetime.now().isoformat(),
                    'server': 'SoloYLibre WordPress Ultimate',
                    **data
                }
                
                self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
            
            def send_api_error(self, status_code, message):
                """Enviar error de API"""
                self.send_api_response({
                    'status': 'error',
                    'error': {
                        'code': status_code,
                        'message': message
                    }
                }, status_code)
            
            def serve_api_health(self):
                """Endpoint de health check"""
                stats = self.server_instance.core.get_system_stats()
                self.send_api_response({
                    'status': 'healthy',
                    'uptime': str(stats.get('uptime', 'Unknown')),
                    'version': self.server_instance.core.version,
                    'build': self.server_instance.core.build,
                    'database': 'connected',
                    'memory': 'ok',
                    'disk': 'ok'
                })
            
            def serve_api_stats(self):
                """Endpoint de estadísticas"""
                stats = self.server_instance.core.get_system_stats()
                self.send_api_response({
                    'status': 'success',
                    'data': stats
                })
            
            def serve_api_posts(self):
                """Endpoint de posts"""
                try:
                    # Parsear parámetros de query
                    parsed_url = urlparse(self.path)
                    query_params = parse_qs(parsed_url.query)

                    limit = int(query_params.get('limit', [10])[0])
                    offset = int(query_params.get('offset', [0])[0])
                    post_type = query_params.get('type', ['post'])[0]

                    posts = self.server_instance.content.get_posts(
                        post_type=post_type,
                        limit=limit,
                        offset=offset
                    )

                    self.send_api_response({
                        'status': 'success',
                        'data': {
                            'posts': posts,
                            'total': len(posts),
                            'limit': limit,
                            'offset': offset
                        }
                    })
                except Exception as e:
                    self.send_api_error(500, f"Error fetching posts: {str(e)}")

            def serve_admin_login(self, error=""):
                """Servir página de login administrativo"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                html = render_admin_login(self.server_instance, error)
                self.wfile.write(html.encode('utf-8'))

            def serve_admin_dashboard(self):
                """Servir dashboard administrativo súper avanzado"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                # Obtener datos del usuario actual
                user = self.get_current_user()

                # Si no hay usuario logueado, crear usuario demo para mostrar el dashboard
                if not user:
                    user = {
                        'id': 1,
                        'username': 'demo',
                        'display_name': 'Usuario Demo',
                        'email': '<EMAIL>',
                        'role': 'administrator'
                    }

                html = render_admin_dashboard(self.server_instance, user)
                self.wfile.write(html.encode('utf-8'))

            def serve_frontend_page(self):
                """Servir página frontend profesional"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                html = render_frontend_page(self.server_instance)
                self.wfile.write(html.encode('utf-8'))

            def serve_backend_page(self):
                """Servir página de backend"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                html = render_backend_page(self.server_instance)
                self.wfile.write(html.encode('utf-8'))

            def handle_admin_page(self):
                """Manejar páginas de administración específicas"""
                try:
                    # Parsear la ruta y remover parámetros de query
                    from urllib.parse import urlparse
                    parsed_url = urlparse(self.path)
                    path_parts = parsed_url.path.split('/')

                    if len(path_parts) < 3:
                        self.send_error(404, "Page not found")
                        return

                    page = path_parts[2]  # soloylibre-admin/[page]

                    # Obtener usuario actual (crear demo si no existe)
                    user = self.get_current_user()
                    if not user:
                        user = {
                            'id': 1,
                            'username': 'demo',
                            'display_name': 'Usuario Demo',
                            'email': '<EMAIL>',
                            'role': 'administrator'
                        }

                    # Manejar diferentes páginas
                    if page == 'posts' or page == 'edit.php':
                        self.serve_admin_posts(user)
                    elif page == 'pages':
                        self.serve_admin_pages(user)
                    elif page == 'media' or page == 'upload.php':
                        self.serve_admin_media(user)
                    elif page == 'comments' or page == 'edit-comments.php':
                        self.serve_admin_comments(user)
                    elif page == 'users' or page == 'users.php':
                        self.serve_admin_users(user)
                    elif page == 'appearance' or page == 'themes.php':
                        self.serve_admin_appearance(user)
                    elif page == 'plugins' or page == 'plugins.php':
                        self.serve_admin_plugins(user)
                    elif page == 'settings' or page == 'options-general.php':
                        self.serve_admin_settings(user)
                    else:
                        self.send_error(404, f"Admin page '{page}' not found")

                except Exception as e:
                    self.server_instance.core.log_error(f"Error handling admin page: {e}")
                    self.send_error(500, "Internal Server Error")

            def serve_admin_posts(self, user):
                """Servir página de administración de posts"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                # Obtener parámetros de filtro
                from urllib.parse import parse_qs, urlparse
                parsed_url = urlparse(self.path)
                query_params = parse_qs(parsed_url.query)

                status = query_params.get('status', ['all'])[0]
                search = query_params.get('search', [''])[0]
                page_num = int(query_params.get('page', [1])[0])

                # Obtener posts usando WordPress Core
                posts_data = self.server_instance.wordpress.get_posts_admin(
                    post_type='post',
                    status=status,
                    search=search,
                    limit=20,
                    offset=(page_num - 1) * 20
                )

                # Obtener conteos por estado
                post_counts = self.server_instance.wordpress.get_post_counts_by_status('post')

                # Preparar filtros
                filters = {
                    'status': status,
                    'search': search,
                    'counts': post_counts,
                    'page': page_num
                }

                html = render_admin_posts(self.server_instance, user, posts_data, filters)
                self.wfile.write(html.encode('utf-8'))

            def serve_admin_pages(self, user):
                """Servir página de administración de páginas"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                # Por ahora, usar la misma plantilla que posts pero con tipo 'page'
                pages_data = self.server_instance.wordpress.get_posts_admin(post_type='page')
                post_counts = self.server_instance.wordpress.get_post_counts_by_status('page')

                filters = {
                    'status': 'all',
                    'search': '',
                    'counts': post_counts,
                    'page': 1
                }

                # Modificar el HTML para páginas
                html = render_admin_posts(self.server_instance, user, pages_data, filters)
                html = html.replace('Posts', 'Páginas').replace('posts', 'páginas').replace('post', 'página')
                self.wfile.write(html.encode('utf-8'))

            def serve_admin_media(self, user):
                """Servir página de administración de medios"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <title>Medios - SoloYLibre WordPress Ultimate</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; padding: 50px; text-align: center; }}
                        h1 {{ color: #667eea; }}
                    </style>
                </head>
                <body>
                    <h1>📷 Biblioteca de Medios</h1>
                    <p>Gestión de archivos multimedia en desarrollo...</p>
                    <a href="/dashboard" style="color: #667eea;">← Volver al Dashboard</a>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))

            def serve_admin_comments(self, user):
                """Servir página de administración de comentarios"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <title>Comentarios - SoloYLibre WordPress Ultimate</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; padding: 50px; text-align: center; }}
                        h1 {{ color: #667eea; }}
                    </style>
                </head>
                <body>
                    <h1>💬 Comentarios</h1>
                    <p>Gestión de comentarios en desarrollo...</p>
                    <a href="/dashboard" style="color: #667eea;">← Volver al Dashboard</a>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))

            def serve_admin_users(self, user):
                """Servir página de administración de usuarios"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <title>Usuarios - SoloYLibre WordPress Ultimate</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; padding: 50px; text-align: center; }}
                        h1 {{ color: #667eea; }}
                    </style>
                </head>
                <body>
                    <h1>👥 Usuarios</h1>
                    <p>Gestión de usuarios en desarrollo...</p>
                    <a href="/dashboard" style="color: #667eea;">← Volver al Dashboard</a>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))

            def serve_admin_appearance(self, user):
                """Servir página de administración de apariencia"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <title>Apariencia - SoloYLibre WordPress Ultimate</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; padding: 50px; text-align: center; }}
                        h1 {{ color: #667eea; }}
                    </style>
                </head>
                <body>
                    <h1>🎨 Apariencia</h1>
                    <p>Personalización de temas en desarrollo...</p>
                    <a href="/dashboard" style="color: #667eea;">← Volver al Dashboard</a>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))

            def serve_admin_plugins(self, user):
                """Servir página de administración de plugins"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <title>Plugins - SoloYLibre WordPress Ultimate</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; padding: 50px; text-align: center; }}
                        h1 {{ color: #667eea; }}
                    </style>
                </head>
                <body>
                    <h1>🔌 Plugins</h1>
                    <p>Gestión de plugins en desarrollo...</p>
                    <a href="/dashboard" style="color: #667eea;">← Volver al Dashboard</a>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))

            def serve_admin_settings(self, user):
                """Servir página de administración de configuración"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <title>Configuración - SoloYLibre WordPress Ultimate</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; padding: 50px; text-align: center; }}
                        h1 {{ color: #667eea; }}
                    </style>
                </head>
                <body>
                    <h1>⚙️ Configuración</h1>
                    <p>Configuración del sistema en desarrollo...</p>
                    <a href="/dashboard" style="color: #667eea;">← Volver al Dashboard</a>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))

            def serve_pwa_manifest(self):
                """Servir manifest para PWA"""
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()

                manifest = {
                    "name": "SoloYLibre WordPress Ultimate",
                    "short_name": "SoloYLibre",
                    "description": "Sistema WordPress profesional desde San José de Ocoa, RD",
                    "start_url": "/",
                    "display": "standalone",
                    "background_color": "#667eea",
                    "theme_color": "#667eea",
                    "orientation": "portrait-primary",
                    "categories": ["productivity", "business"],
                    "lang": "es",
                    "icons": [
                        {
                            "src": "/icon-192.png",
                            "sizes": "192x192",
                            "type": "image/png",
                            "purpose": "any maskable"
                        },
                        {
                            "src": "/icon-512.png",
                            "sizes": "512x512",
                            "type": "image/png",
                            "purpose": "any maskable"
                        }
                    ]
                }

                self.wfile.write(json.dumps(manifest, indent=2).encode('utf-8'))

            def serve_service_worker(self):
                """Servir Service Worker para PWA"""
                self.send_response(200)
                self.send_header('Content-type', 'application/javascript')
                self.end_headers()

                sw_content = f"""
                const CACHE_NAME = 'soloylibre-v{self.server_instance.core.version}';
                const urlsToCache = [
                    '/',
                    '/soloylibre-admin',
                    '/manifest.json',
                    '/api/health'
                ];

                // Instalar Service Worker
                self.addEventListener('install', event => {{
                    console.log('🔧 Service Worker instalando...');
                    event.waitUntil(
                        caches.open(CACHE_NAME)
                            .then(cache => {{
                                console.log('📦 Cache abierto');
                                return cache.addAll(urlsToCache);
                            }})
                    );
                }});

                // Activar Service Worker
                self.addEventListener('activate', event => {{
                    console.log('✅ Service Worker activado');
                    event.waitUntil(
                        caches.keys().then(cacheNames => {{
                            return Promise.all(
                                cacheNames.map(cacheName => {{
                                    if (cacheName !== CACHE_NAME) {{
                                        console.log('🗑️ Eliminando cache antiguo:', cacheName);
                                        return caches.delete(cacheName);
                                    }}
                                }})
                            );
                        }})
                    );
                }});

                // Interceptar requests
                self.addEventListener('fetch', event => {{
                    event.respondWith(
                        caches.match(event.request)
                            .then(response => {{
                                // Devolver desde cache si existe
                                if (response) {{
                                    return response;
                                }}

                                // Sino, hacer request a la red
                                return fetch(event.request).then(response => {{
                                    // Verificar si es una respuesta válida
                                    if (!response || response.status !== 200 || response.type !== 'basic') {{
                                        return response;
                                    }}

                                    // Clonar respuesta
                                    const responseToCache = response.clone();

                                    // Agregar al cache
                                    caches.open(CACHE_NAME)
                                        .then(cache => {{
                                            cache.put(event.request, responseToCache);
                                        }});

                                    return response;
                                }});
                            }})
                    );
                }});

                // Manejar notificaciones push
                self.addEventListener('push', event => {{
                    const options = {{
                        body: event.data ? event.data.text() : 'Nueva notificación de SoloYLibre',
                        icon: '/icon-192.png',
                        badge: '/icon-192.png',
                        vibrate: [100, 50, 100],
                        data: {{
                            dateOfArrival: Date.now(),
                            primaryKey: 1
                        }},
                        actions: [
                            {{
                                action: 'explore',
                                title: 'Ver más',
                                icon: '/icon-192.png'
                            }},
                            {{
                                action: 'close',
                                title: 'Cerrar',
                                icon: '/icon-192.png'
                            }}
                        ]
                    }};

                    event.waitUntil(
                        self.registration.showNotification('SoloYLibre WordPress', options)
                    );
                }});
                """

                self.wfile.write(sw_content.encode('utf-8'))
        
        # Crear handler factory
        def handler_factory(*args, **kwargs):
            return WordPressUltimateHandler(*args, server_instance=self, **kwargs)
        
        # Cambiar al directorio de trabajo
        os.chdir(self.wordpress_dir)
        
        # Iniciar servidor
        try:
            with socketserver.TCPServer(("", self.port), handler_factory) as httpd:
                print(f"✅ SoloYLibre WordPress Ultimate Server corriendo en http://localhost:{self.port}")
                print("\n🔐 CREDENCIALES DE ACCESO:")
                print("👤 Usuario: josetusabe")
                print("🔑 Contraseña: JoseTusabe2025!")
                print("📧 Email: <EMAIL>")
                print("\n🌐 URLS PRINCIPALES:")
                print(f"🏠 Frontend: http://localhost:{self.port}")
                print(f"🔧 Admin: http://localhost:{self.port}/soloylibre-admin")
                print(f"📊 Dashboard: http://localhost:{self.port}/dashboard")
                print(f"🔌 API: http://localhost:{self.port}/api/")
                print(f"📱 PWA Manifest: http://localhost:{self.port}/manifest.json")
                print("\n🚀 MÓDULOS ACTIVOS:")
                print("✅ Core System - Núcleo del sistema")
                print("✅ Authentication Manager - Gestión de usuarios y seguridad")
                print("✅ Content Manager - Gestión de contenido")
                print("✅ API REST - Endpoints completos")
                print("✅ PWA Support - Progressive Web App")
                print("✅ Analytics - Métricas en tiempo real")
                print("\n🛑 Presiona Ctrl+C para detener")
                print("="*100)
                
                # Abrir navegador automáticamente
                threading.Thread(
                    target=lambda: (time.sleep(2), webbrowser.open(f'http://localhost:{self.port}/soloylibre-admin')),
                    daemon=True
                ).start()
                
                # Limpiar sesiones expiradas periódicamente
                def cleanup_sessions():
                    while True:
                        time.sleep(3600)  # Cada hora
                        try:
                            cleaned = self.auth.cleanup_expired_sessions()
                            if cleaned > 0:
                                self.core.log_info(f"Cleaned {cleaned} expired sessions")
                        except Exception as e:
                            self.core.log_error(f"Error cleaning sessions: {e}")
                
                threading.Thread(target=cleanup_sessions, daemon=True).start()
                
                try:
                    httpd.serve_forever()
                except KeyboardInterrupt:
                    print("\n🛑 Deteniendo servidor...")
                    self.core.log_info("Sistema detenido por el usuario")
                    print("¡Dale paisano, que estuvo brutal! 🇩🇴")
                    print("WordPress Ultimate funcionando perfectamente desde San José de Ocoa.")
                    
        except Exception as e:
            self.core.log_error(f"Server error: {e}")
            print(f"❌ Error iniciando servidor: {e}")

if __name__ == '__main__':
    server = SoloYLibreUltimateServer()
    server.start_server()
