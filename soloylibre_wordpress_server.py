#!/usr/bin/env python3
"""
SoloYLibre WordPress Server
Servidor PHP integrado para WordPress Multisite
Desarrollado por <PERSON> (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import subprocess
import os
import sys
import time
import threading
import sqlite3
from pathlib import Path

class SoloYLibreWordPressServer:
    def __init__(self):
        self.port = 8090
        self.host = 'localhost'
        self.wordpress_dir = '/Users/<USER>/Desktop/SoloYLibre-WordPress'
        self.db_file = os.path.join(self.wordpress_dir, 'soloylibre_wordpress.db')
        
    def setup_database(self):
        """Setup SQLite database for WordPress"""
        print("🗄️ Configurando base de datos SQLite...")
        
        # Create database directory
        os.makedirs(os.path.dirname(self.db_file), exist_ok=True)
        
        # Create basic WordPress tables structure
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # WordPress core tables
        tables = [
            """CREATE TABLE IF NOT EXISTS sl_users (
                ID bigint(20) unsigned NOT NULL PRIMARY KEY,
                user_login varchar(60) NOT NULL DEFAULT '',
                user_pass varchar(255) NOT NULL DEFAULT '',
                user_nicename varchar(50) NOT NULL DEFAULT '',
                user_email varchar(100) NOT NULL DEFAULT '',
                user_url varchar(100) NOT NULL DEFAULT '',
                user_registered datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                user_activation_key varchar(255) NOT NULL DEFAULT '',
                user_status int(11) NOT NULL DEFAULT '0',
                display_name varchar(250) NOT NULL DEFAULT ''
            )""",
            
            """CREATE TABLE IF NOT EXISTS sl_options (
                option_id bigint(20) unsigned NOT NULL PRIMARY KEY,
                option_name varchar(191) NOT NULL DEFAULT '',
                option_value longtext NOT NULL,
                autoload varchar(20) NOT NULL DEFAULT 'yes'
            )""",
            
            """CREATE TABLE IF NOT EXISTS sl_posts (
                ID bigint(20) unsigned NOT NULL PRIMARY KEY,
                post_author bigint(20) unsigned NOT NULL DEFAULT '0',
                post_date datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                post_date_gmt datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                post_content longtext NOT NULL,
                post_title text NOT NULL,
                post_excerpt text NOT NULL,
                post_status varchar(20) NOT NULL DEFAULT 'publish',
                comment_status varchar(20) NOT NULL DEFAULT 'open',
                ping_status varchar(20) NOT NULL DEFAULT 'open',
                post_password varchar(255) NOT NULL DEFAULT '',
                post_name varchar(200) NOT NULL DEFAULT '',
                to_ping text NOT NULL,
                pinged text NOT NULL,
                post_modified datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                post_modified_gmt datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                post_content_filtered longtext NOT NULL,
                post_parent bigint(20) unsigned NOT NULL DEFAULT '0',
                guid varchar(255) NOT NULL DEFAULT '',
                menu_order int(11) NOT NULL DEFAULT '0',
                post_type varchar(20) NOT NULL DEFAULT 'post',
                post_mime_type varchar(100) NOT NULL DEFAULT '',
                comment_count bigint(20) NOT NULL DEFAULT '0'
            )"""
        ]
        
        for table_sql in tables:
            cursor.execute(table_sql)
        
        # Insert default admin user
        cursor.execute("""
            INSERT OR IGNORE INTO sl_users 
            (ID, user_login, user_pass, user_nicename, user_email, user_url, user_registered, display_name)
            VALUES 
            (1, 'josetusabe', '$P$BZlPX7NIx8MYpXokBW2AGsN7i.aUOt0', 'josetusabe', '<EMAIL>', 'https://soloylibre.com', datetime('now'), 'Jose L Encarnacion (JoseTusabe)')
        """)
        
        # Insert basic options
        options = [
            ('siteurl', 'http://localhost:8090'),
            ('home', 'http://localhost:8090'),
            ('blogname', 'SoloYLibre WordPress Multisite'),
            ('blogdescription', 'Desarrollado desde San José de Ocoa, República Dominicana'),
            ('admin_email', '<EMAIL>'),
            ('start_of_week', '1'),
            ('use_balanceTags', '0'),
            ('use_smilies', '1'),
            ('require_name_email', '1'),
            ('comments_notify', '1'),
            ('posts_per_rss', '10'),
            ('rss_use_excerpt', '0'),
            ('mailserver_url', 'mail.example.com'),
            ('mailserver_login', '<EMAIL>'),
            ('mailserver_pass', 'password'),
            ('mailserver_port', '110'),
            ('default_category', '1'),
            ('default_comment_status', 'open'),
            ('default_ping_status', 'open'),
            ('default_pingback_flag', '1'),
            ('posts_per_page', '10'),
            ('date_format', 'F j, Y'),
            ('time_format', 'g:i a'),
            ('links_updated_date_format', 'F j, Y g:i a'),
            ('comment_moderation', '0'),
            ('moderation_notify', '1'),
            ('permalink_structure', '/%year%/%monthnum%/%day%/%postname%/'),
            ('rewrite_rules', ''),
            ('hack_file', '0'),
            ('blog_charset', 'UTF-8'),
            ('moderation_keys', ''),
            ('active_plugins', 'a:0:{}'),
            ('category_base', ''),
            ('ping_sites', 'http://rpc.pingomatic.com/'),
            ('comment_max_links', '2'),
            ('gmt_offset', '0'),
            ('default_email_category', '1'),
            ('recently_edited', ''),
            ('template', 'twentytwentyfour'),
            ('stylesheet', 'twentytwentyfour'),
            ('comment_registration', '0'),
            ('html_type', 'text/html'),
            ('use_trackback', '0'),
            ('default_role', 'subscriber'),
            ('db_version', '57155'),
            ('uploads_use_yearmonth_folders', '1'),
            ('upload_path', ''),
            ('blog_public', '1'),
            ('default_link_category', '2'),
            ('show_on_front', 'posts'),
            ('tag_base', ''),
            ('show_avatars', '1'),
            ('avatar_rating', 'G'),
            ('upload_url_path', ''),
            ('thumbnail_size_w', '150'),
            ('thumbnail_size_h', '150'),
            ('thumbnail_crop', '1'),
            ('medium_size_w', '300'),
            ('medium_size_h', '300'),
            ('avatar_default', 'mystery'),
            ('large_size_w', '1024'),
            ('large_size_h', '1024'),
            ('image_default_link_type', 'none'),
            ('image_default_size', ''),
            ('image_default_align', ''),
            ('close_comments_for_old_posts', '0'),
            ('close_comments_days_old', '14'),
            ('thread_comments', '1'),
            ('thread_comments_depth', '5'),
            ('page_comments', '0'),
            ('comments_per_page', '50'),
            ('default_comments_page', 'newest'),
            ('comment_order', 'asc'),
            ('sticky_posts', 'a:0:{}'),
            ('widget_categories', 'a:2:{i:2;a:4:{s:5:"title";s:0:"";s:5:"count";i:0;s:12:"hierarchical";i:0;s:8:"dropdown";i:0;}s:12:"_multiwidget";i:1;}'),
            ('widget_text', 'a:0:{}'),
            ('widget_rss', 'a:0:{}'),
            ('uninstall_plugins', 'a:0:{}'),
            ('timezone_string', ''),
            ('page_for_posts', '0'),
            ('page_on_front', '0'),
            ('default_post_format', '0'),
            ('link_manager_enabled', '0'),
            ('finished_splitting_shared_terms', '1'),
            ('site_icon', '0'),
            ('medium_large_size_w', '768'),
            ('medium_large_size_h', '0'),
            ('wp_page_for_privacy_policy', '3'),
            ('show_comments_cookies_opt_in', '1'),
            ('admin_email_lifespan', '1735689600'),
            ('disallowed_keys', ''),
            ('comment_previously_approved', '1'),
            ('auto_plugin_theme_update_emails', 'a:0:{}'),
            ('auto_update_core_dev', 'enabled'),
            ('auto_update_core_minor', 'enabled'),
            ('auto_update_core_major', 'enabled'),
            ('wp_force_deactivated_plugins', 'a:0:{}'),
            ('initial_db_version', '57155'),
            ('sl_user_roles', 'a:5:{s:13:"administrator";a:2:{s:4:"name";s:13:"Administrator";s:12:"capabilities";a:61:{s:13:"switch_themes";b:1;s:11:"edit_themes";b:1;s:16:"activate_plugins";b:1;s:12:"edit_plugins";b:1;s:10:"edit_users";b:1;s:10:"edit_files";b:1;s:14:"manage_options";b:1;s:17:"moderate_comments";b:1;s:17:"manage_categories";b:1;s:12:"manage_links";b:1;s:12:"upload_files";b:1;s:6:"import";b:1;s:15:"unfiltered_html";b:1;s:10:"edit_posts";b:1;s:17:"edit_others_posts";b:1;s:20:"edit_published_posts";b:1;s:13:"publish_posts";b:1;s:10:"edit_pages";b:1;s:4:"read";b:1;s:8:"level_10";b:1;s:7:"level_9";b:1;s:7:"level_8";b:1;s:7:"level_7";b:1;s:7:"level_6";b:1;s:7:"level_5";b:1;s:7:"level_4";b:1;s:7:"level_3";b:1;s:7:"level_2";b:1;s:7:"level_1";b:1;s:7:"level_0";b:1;s:17:"edit_others_pages";b:1;s:20:"edit_published_pages";b:1;s:13:"publish_pages";b:1;s:12:"delete_pages";b:1;s:19:"delete_others_pages";b:1;s:22:"delete_published_pages";b:1;s:12:"delete_posts";b:1;s:19:"delete_others_posts";b:1;s:22:"delete_published_posts";b:1;s:20:"delete_private_posts";b:1;s:18:"edit_private_posts";b:1;s:18:"read_private_posts";b:1;s:20:"delete_private_pages";b:1;s:18:"edit_private_pages";b:1;s:18:"read_private_pages";b:1;s:12:"delete_users";b:1;s:12:"create_users";b:1;s:17:"unfiltered_upload";b:1;s:14:"edit_dashboard";b:1;s:14:"update_plugins";b:1;s:14:"delete_plugins";b:1;s:15:"install_plugins";b:1;s:13:"update_themes";b:1;s:14:"install_themes";b:1;s:11:"update_core";b:1;s:10:"list_users";b:1;s:12:"remove_users";b:1;s:13:"promote_users";b:1;s:18:"edit_theme_options";b:1;s:13:"delete_themes";b:1;s:6:"export";b:1;}}s:6:"editor";a:2:{s:4:"name";s:6:"Editor";s:12:"capabilities";a:34:{s:17:"moderate_comments";b:1;s:17:"manage_categories";b:1;s:12:"manage_links";b:1;s:12:"upload_files";b:1;s:15:"unfiltered_html";b:1;s:10:"edit_posts";b:1;s:17:"edit_others_posts";b:1;s:20:"edit_published_posts";b:1;s:13:"publish_posts";b:1;s:10:"edit_pages";b:1;s:4:"read";b:1;s:7:"level_7";b:1;s:7:"level_6";b:1;s:7:"level_5";b:1;s:7:"level_4";b:1;s:7:"level_3";b:1;s:7:"level_2";b:1;s:7:"level_1";b:1;s:7:"level_0";b:1;s:17:"edit_others_pages";b:1;s:20:"edit_published_pages";b:1;s:13:"publish_pages";b:1;s:12:"delete_pages";b:1;s:19:"delete_others_pages";b:1;s:22:"delete_published_pages";b:1;s:12:"delete_posts";b:1;s:19:"delete_others_posts";b:1;s:22:"delete_published_posts";b:1;s:20:"delete_private_posts";b:1;s:18:"edit_private_posts";b:1;s:18:"read_private_posts";b:1;s:20:"delete_private_pages";b:1;s:18:"edit_private_pages";b:1;s:18:"read_private_pages";b:1;}}s:6:"author";a:2:{s:4:"name";s:6:"Author";s:12:"capabilities";a:10:{s:12:"upload_files";b:1;s:10:"edit_posts";b:1;s:20:"edit_published_posts";b:1;s:13:"publish_posts";b:1;s:4:"read";b:1;s:7:"level_2";b:1;s:7:"level_1";b:1;s:7:"level_0";b:1;s:12:"delete_posts";b:1;s:22:"delete_published_posts";b:1;}}s:11:"contributor";a:2:{s:4:"name";s:11:"Contributor";s:12:"capabilities";a:5:{s:10:"edit_posts";b:1;s:4:"read";b:1;s:7:"level_1";b:1;s:7:"level_0";b:1;s:12:"delete_posts";b:1;}}s:10:"subscriber";a:2:{s:4:"name";s:10:"Subscriber";s:12:"capabilities";a:2:{s:4:"read";b:1;s:7:"level_0";b:1;}}}'),
            ('fresh_site', '1'),
            ('WPLANG', ''),
            ('new_admin_email', '<EMAIL>'),
            ('soloylibre_developer', 'Jose L Encarnacion (JoseTusabe)'),
            ('soloylibre_location', 'San José de Ocoa, República Dominicana'),
            ('soloylibre_email', '<EMAIL>'),
            ('soloylibre_phone', '************')
        ]
        
        for option_name, option_value in options:
            cursor.execute("""
                INSERT OR IGNORE INTO sl_options (option_name, option_value, autoload)
                VALUES (?, ?, 'yes')
            """, (option_name, option_value))
        
        conn.commit()
        conn.close()
        
        print("✅ Base de datos configurada exitosamente")
        
    def start_server(self):
        """Start PHP built-in server"""
        print("🚀 Iniciando SoloYLibre WordPress Server...")
        print(f"🇩🇴 Desarrollado por Jose L Encarnacion (JoseTusabe)")
        print(f"🏔️ Desde San José de Ocoa, República Dominicana")
        print(f"🌐 URL: http://{self.host}:{self.port}")
        print("=" * 70)
        
        # Setup database first
        self.setup_database()
        
        # Check if PHP is available
        try:
            subprocess.run(['php', '--version'], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ PHP no está instalado. Instalando...")
            # For now, we'll create a simple Python server
            self.start_python_server()
            return
        
        # Start PHP server
        os.chdir(self.wordpress_dir)
        cmd = ['php', '-S', f'{self.host}:{self.port}']
        
        try:
            subprocess.run(cmd)
        except KeyboardInterrupt:
            print("\n🛑 Servidor detenido")
        except Exception as e:
            print(f"❌ Error: {e}")
            
    def start_python_server(self):
        """Start Python server as fallback"""
        print("🐍 Iniciando servidor Python como alternativa...")
        
        import http.server
        import socketserver
        
        class SoloYLibreHandler(http.server.SimpleHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()
                    
                    html = f"""
                    <!DOCTYPE html>
                    <html lang="es">
                    <head>
                        <meta charset="UTF-8">
                        <title>🇩🇴 SoloYLibre WordPress</title>
                        <style>
                            body {{ 
                                font-family: Arial, sans-serif; 
                                background: linear-gradient(135deg, #CE1126, #002D62);
                                color: white; 
                                text-align: center; 
                                padding: 50px;
                            }}
                            .container {{ 
                                max-width: 800px; 
                                margin: 0 auto; 
                                background: rgba(255,255,255,0.1);
                                padding: 40px;
                                border-radius: 20px;
                            }}
                            h1 {{ font-size: 3rem; margin-bottom: 20px; }}
                            .credentials {{ 
                                background: rgba(0,0,0,0.3); 
                                padding: 20px; 
                                border-radius: 10px; 
                                margin: 20px 0;
                                text-align: left;
                            }}
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <h1>🇩🇴 SoloYLibre WordPress Multisite</h1>
                            <p>Desarrollado desde San José de Ocoa, República Dominicana</p>
                            <p>Por Jose L Encarnacion (JoseTusabe)</p>
                            
                            <div class="credentials">
                                <h3>🔐 Credenciales de Acceso:</h3>
                                <p><strong>URL:</strong> http://localhost:{self.port}/wp-admin</p>
                                <p><strong>Usuario:</strong> josetusabe</p>
                                <p><strong>Contraseña:</strong> JoseTusabe2025!</p>
                                <p><strong>Email:</strong> <EMAIL></p>
                            </div>
                            
                            <div class="credentials">
                                <h3>📧 Información de Contacto:</h3>
                                <p><strong>Desarrollador:</strong> Jose L Encarnacion (JoseTusabe)</p>
                                <p><strong>Email:</strong> <EMAIL></p>
                                <p><strong>Teléfono:</strong> ************</p>
                                <p><strong>Ubicación:</strong> San José de Ocoa, República Dominicana</p>
                            </div>
                            
                            <p>🚀 Para instalar WordPress completo, necesitas PHP y MySQL</p>
                            <p>💻 Mientras tanto, puedes usar este servidor de desarrollo</p>
                        </div>
                    </body>
                    </html>
                    """
                    
                    self.wfile.write(html.encode())
                else:
                    super().do_GET()
        
        os.chdir(self.wordpress_dir)
        with socketserver.TCPServer(("", self.port), SoloYLibreHandler) as httpd:
            print(f"🌐 Servidor corriendo en http://localhost:{self.port}")
            httpd.serve_forever()

if __name__ == '__main__':
    server = SoloYLibreWordPressServer()
    server.start_server()
