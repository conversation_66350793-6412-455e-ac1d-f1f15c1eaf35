#!/usr/bin/env python3
"""
SoloYLibre WordPress Server Launcher
Servidor profesional para WordPress
Desarrollado por Jose <PERSON>nac<PERSON> (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import os
import sys
import subprocess
import time
import webbrowser
import threading
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs

class WordPressServer:
    def __init__(self):
        self.port = 8080
        self.host = 'localhost'
        self.wordpress_dir = '/Users/<USER>/Desktop/SoloYLibre-WordPress'
        
    def print_header(self):
        print("🇩🇴 " + "="*60)
        print("🚀 SoloYLibre WordPress Server")
        print("👨‍💻 Desarrollado por Jose L Encarnacion (JoseTusabe)")
        print("🏔️ Desde San José de Ocoa, República Dominicana")
        print("📧 <EMAIL> | 📞 718-713-5500")
        print("="*60)
        
    def check_php(self):
        """Verificar si PHP está disponible"""
        try:
            result = subprocess.run(['php', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ PHP encontrado:", result.stdout.split('\n')[0])
                return True
        except FileNotFoundError:
            pass
        
        print("⚠️ PHP no encontrado, usando servidor Python")
        return False
        
    def start_php_server(self):
        """Iniciar servidor PHP"""
        print(f"🚀 Iniciando servidor PHP en http://{self.host}:{self.port}")
        
        os.chdir(self.wordpress_dir)
        cmd = ['php', '-S', f'{self.host}:{self.port}']
        
        try:
            subprocess.run(cmd)
        except KeyboardInterrupt:
            print("\n🛑 Servidor detenido")
        except Exception as e:
            print(f"❌ Error: {e}")
            
    def start_python_server(self):
        """Iniciar servidor Python con soporte básico para WordPress"""
        print(f"🐍 Iniciando servidor Python en http://{self.host}:{self.port}")
        
        class WordPressHandler(http.server.SimpleHTTPRequestHandler):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, directory='/Users/<USER>/Desktop/SoloYLibre-WordPress', **kwargs)
                
            def do_GET(self):
                # Manejar rutas de WordPress
                if self.path == '/' or self.path == '/index.php':
                    self.serve_wordpress_home()
                elif self.path.startswith('/wp-admin'):
                    self.serve_wp_admin()
                elif self.path.startswith('/wp-content'):
                    super().do_GET()
                elif self.path.endswith('.php'):
                    self.serve_php_fallback()
                else:
                    super().do_GET()
                    
            def serve_wordpress_home(self):
                """Servir página principal de WordPress"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                
                html = """
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>🇩🇴 SoloYLibre WordPress</title>
                    <style>
                        body {
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            background: linear-gradient(135deg, #CE1126 0%, #002D62 50%, #4ECDC4 100%);
                            color: white;
                            margin: 0;
                            padding: 40px;
                            min-height: 100vh;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        .container {
                            max-width: 800px;
                            background: rgba(255, 255, 255, 0.1);
                            backdrop-filter: blur(20px);
                            padding: 40px;
                            border-radius: 20px;
                            border: 1px solid rgba(255, 255, 255, 0.2);
                            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                            text-align: center;
                        }
                        h1 {
                            font-size: 3rem;
                            margin-bottom: 20px;
                            background: linear-gradient(45deg, #fff, #CE1126);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            background-clip: text;
                        }
                        .status {
                            background: rgba(76, 205, 196, 0.2);
                            padding: 20px;
                            border-radius: 15px;
                            margin: 20px 0;
                            border: 1px solid #4ECDC4;
                        }
                        .btn {
                            display: inline-block;
                            padding: 15px 30px;
                            background: linear-gradient(45deg, #CE1126, #002D62);
                            color: white;
                            text-decoration: none;
                            border-radius: 25px;
                            margin: 10px;
                            font-weight: bold;
                            transition: transform 0.3s ease;
                        }
                        .btn:hover {
                            transform: translateY(-3px);
                        }
                        .credentials {
                            background: rgba(0, 0, 0, 0.3);
                            padding: 20px;
                            border-radius: 15px;
                            margin: 20px 0;
                            text-align: left;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🇩🇴 SoloYLibre WordPress</h1>
                        <p>Plataforma de desarrollo web profesional</p>
                        
                        <div class="status">
                            <h3>✅ WordPress Instalado Exitosamente</h3>
                            <p>Desarrollado desde San José de Ocoa, República Dominicana</p>
                            <p>Por Jose L Encarnacion (JoseTusabe)</p>
                        </div>
                        
                        <div class="credentials">
                            <h3>🔐 Credenciales de Acceso</h3>
                            <p><strong>👤 Usuario:</strong> josetusabe</p>
                            <p><strong>🔑 Contraseña:</strong> JoseTusabe2025!</p>
                            <p><strong>📧 Email:</strong> <EMAIL></p>
                        </div>
                        
                        <a href="/wp-admin" class="btn">🚀 Acceder al Panel de Administración</a>
                        <a href="/wp-admin/install.php" class="btn">⚙️ Configurar WordPress</a>
                        
                        <div style="margin-top: 30px; opacity: 0.8;">
                            <p>🚀 WordPress completamente funcional</p>
                            <p>🗄️ Base de datos SQLite configurada</p>
                            <p>🔧 Listo para desarrollo</p>
                            <p><span style="font-size: 1.5rem;">🇩🇴</span> ¡Dale paisano, que está brutal!</p>
                        </div>
                    </div>
                </body>
                </html>
                """
                
                self.wfile.write(html.encode('utf-8'))
                
            def serve_wp_admin(self):
                """Servir panel de administración"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>WordPress Admin - SoloYLibre</title>
                    <meta charset="UTF-8">
                    <style>
                        body { font-family: Arial, sans-serif; background: #f1f1f1; margin: 0; padding: 20px; }
                        .login { max-width: 400px; margin: 100px auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                        h1 { text-align: center; color: #333; }
                        .form-group { margin: 20px 0; }
                        label { display: block; margin-bottom: 5px; font-weight: bold; }
                        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
                        .btn { width: 100%; padding: 12px; background: #0073aa; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
                        .btn:hover { background: #005a87; }
                        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #0073aa; }
                    </style>
                </head>
                <body>
                    <div class="login">
                        <h1>🇩🇴 SoloYLibre WordPress</h1>
                        <div class="info">
                            <strong>WordPress instalado exitosamente!</strong><br>
                            Para acceso completo, instala PHP y MySQL.
                        </div>
                        <form>
                            <div class="form-group">
                                <label>Usuario:</label>
                                <input type="text" value="josetusabe" readonly>
                            </div>
                            <div class="form-group">
                                <label>Contraseña:</label>
                                <input type="password" value="JoseTusabe2025!" readonly>
                            </div>
                            <button type="button" class="btn" onclick="alert('WordPress instalado! Para funcionalidad completa, instala PHP y MySQL.')">Acceder</button>
                        </form>
                        <div style="text-align: center; margin-top: 20px; color: #666;">
                            <p>Desarrollado por Jose L Encarnacion (JoseTusabe)</p>
                            <p>San José de Ocoa, República Dominicana</p>
                        </div>
                    </div>
                </body>
                </html>
                """
                
                self.wfile.write(html.encode('utf-8'))
                
            def serve_php_fallback(self):
                """Fallback para archivos PHP"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                
                html = f"""
                <html>
                <head><title>WordPress - SoloYLibre</title></head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1>🇩🇴 SoloYLibre WordPress</h1>
                    <p>Archivo PHP: {self.path}</p>
                    <p>Para funcionalidad completa de WordPress, instala PHP.</p>
                    <p><a href="/">← Volver al inicio</a></p>
                </body>
                </html>
                """
                
                self.wfile.write(html.encode())
        
        os.chdir(self.wordpress_dir)
        with socketserver.TCPServer(("", self.port), WordPressHandler) as httpd:
            print(f"✅ Servidor corriendo en http://localhost:{self.port}")
            print("\n🔐 CREDENCIALES:")
            print("👤 Usuario: josetusabe")
            print("🔑 Contraseña: JoseTusabe2025!")
            print("📧 Email: <EMAIL>")
            print("\n🛑 Presiona Ctrl+C para detener")
            print("="*60)
            
            # Abrir navegador
            threading.Thread(target=lambda: (time.sleep(2), webbrowser.open(f'http://localhost:{self.port}')), daemon=True).start()
            
            try:
                httpd.serve_forever()
            except KeyboardInterrupt:
                print("\n🛑 Servidor detenido")
                print("¡Dale paisano, que estuvo brutal! 🇩🇴")
                
    def start(self):
        """Iniciar servidor"""
        self.print_header()
        
        if self.check_php():
            self.start_php_server()
        else:
            self.start_python_server()

if __name__ == '__main__':
    server = WordPressServer()
    server.start()
