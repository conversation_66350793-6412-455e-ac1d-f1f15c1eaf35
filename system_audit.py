#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - System Audit
Auditoría completa del sistema para identificar todo lo que falta
Desarrollado por Jose L Encarnacion (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import requests
import os
import json
from datetime import datetime

class SystemAudit:
    """Auditoría completa del sistema WordPress Ultimate"""
    
    def __init__(self):
        self.base_url = "http://localhost:8080"
        self.issues = []
        self.missing_features = []
        self.incomplete_pages = []
        self.login_issues = []
        
    def print_header(self):
        """Header de auditoría"""
        print("🇩🇴 " + "="*80)
        print("🔍 AUDITORÍA COMPLETA DEL SISTEMA")
        print("👨‍💻 Desarrollado por Jose L Encarnacion (JoseTusabe)")
        print("🏔️ Desde San José de Ocoa, República Dominicana")
        print("🎯 Identificando todo lo que falta por implementar")
        print("="*80)
    
    def audit_login_system(self):
        """Auditar sistema de login"""
        print("\n🔐 AUDITANDO SISTEMA DE LOGIN...")
        
        # Test 1: Página de login accesible
        try:
            response = requests.get(f"{self.base_url}/soloylibre-admin", timeout=10)
            if response.status_code == 200:
                print("✅ Página de login accesible")
                
                # Verificar elementos del login
                content = response.text
                login_elements = [
                    ('Formulario de login', 'form'),
                    ('Campo usuario', 'name="log"'),
                    ('Campo contraseña', 'name="pwd"'),
                    ('Botón submit', 'type="submit"'),
                    ('Credenciales visibles', 'josetusabe'),
                ]
                
                for element, search_term in login_elements:
                    if search_term in content:
                        print(f"   ✅ {element}")
                    else:
                        print(f"   ❌ {element}")
                        self.login_issues.append(f"Falta: {element}")
            else:
                print(f"❌ Página de login no accesible (código {response.status_code})")
                self.login_issues.append("Página de login no accesible")
        except Exception as e:
            print(f"❌ Error accediendo al login: {e}")
            self.login_issues.append(f"Error de acceso: {e}")
        
        # Test 2: Proceso de login
        print("\n🔑 Probando proceso de login...")
        try:
            login_data = {
                'log': 'josetusabe',
                'pwd': 'JoseTusabe2025!'
            }
            
            response = requests.post(
                f"{self.base_url}/soloylibre-admin",
                data=login_data,
                allow_redirects=False,
                timeout=10
            )
            
            if response.status_code == 302:
                print("✅ Login funciona correctamente (redirección)")
            elif response.status_code == 200:
                if "Dashboard" in response.text or "dashboard" in response.text:
                    print("✅ Login funciona (dashboard mostrado)")
                else:
                    print("⚠️ Login procesa pero no redirige al dashboard")
                    self.login_issues.append("Login no redirige correctamente")
            else:
                print(f"❌ Login falla (código {response.status_code})")
                self.login_issues.append(f"Login falla con código {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error en proceso de login: {e}")
            self.login_issues.append(f"Error en proceso: {e}")
    
    def audit_admin_pages(self):
        """Auditar páginas de administración"""
        print("\n📊 AUDITANDO PÁGINAS DE ADMINISTRACIÓN...")
        
        admin_pages = [
            ('/dashboard', 'Dashboard Principal'),
            ('/soloylibre-admin/posts', 'Gestión de Posts'),
            ('/soloylibre-admin/pages', 'Gestión de Páginas'),
            ('/soloylibre-admin/media', 'Biblioteca de Medios'),
            ('/soloylibre-admin/comments', 'Gestión de Comentarios'),
            ('/soloylibre-admin/users', 'Gestión de Usuarios'),
            ('/soloylibre-admin/appearance', 'Personalización'),
            ('/soloylibre-admin/plugins', 'Gestión de Plugins'),
            ('/soloylibre-admin/settings', 'Configuración'),
        ]
        
        for url, name in admin_pages:
            try:
                response = requests.get(f"{self.base_url}{url}", timeout=10)
                content = response.text
                
                # Verificar si está "en desarrollo"
                development_indicators = [
                    'en desarrollo',
                    'en desarrollo...',
                    'development',
                    'coming soon',
                    'próximamente',
                    'no implementado',
                    'not implemented'
                ]
                
                is_incomplete = any(indicator in content.lower() for indicator in development_indicators)
                
                if response.status_code == 200:
                    if is_incomplete:
                        print(f"⚠️ {name}: Funcional pero incompleto")
                        self.incomplete_pages.append(name)
                    else:
                        print(f"✅ {name}: Completamente funcional")
                else:
                    print(f"❌ {name}: No accesible (código {response.status_code})")
                    self.issues.append(f"{name} no accesible")
                    
            except Exception as e:
                print(f"❌ {name}: Error - {e}")
                self.issues.append(f"{name}: {e}")
    
    def audit_missing_features(self):
        """Auditar funcionalidades faltantes"""
        print("\n🔧 AUDITANDO FUNCIONALIDADES FALTANTES...")
        
        # Verificar funcionalidades WordPress estándar
        wordpress_features = [
            ('Editor de posts', '/soloylibre-admin/post-new.php'),
            ('Editor de páginas', '/soloylibre-admin/page-new.php'),
            ('Upload de medios', '/soloylibre-admin/media-new.php'),
            ('Gestión de menús', '/soloylibre-admin/nav-menus.php'),
            ('Widgets', '/soloylibre-admin/widgets.php'),
            ('Customizer', '/soloylibre-admin/customize.php'),
            ('Importar/Exportar', '/soloylibre-admin/import.php'),
            ('Herramientas', '/soloylibre-admin/tools.php'),
            ('Actualizaciones', '/soloylibre-admin/update-core.php'),
        ]
        
        for feature, url in wordpress_features:
            try:
                response = requests.get(f"{self.base_url}{url}", timeout=5)
                if response.status_code == 404:
                    print(f"❌ {feature}: No implementado")
                    self.missing_features.append(feature)
                elif response.status_code == 200:
                    print(f"✅ {feature}: Implementado")
                else:
                    print(f"⚠️ {feature}: Parcialmente implementado")
            except:
                print(f"❌ {feature}: No accesible")
                self.missing_features.append(feature)
    
    def audit_file_structure(self):
        """Auditar estructura de archivos"""
        print("\n📁 AUDITANDO ESTRUCTURA DE ARCHIVOS...")
        
        required_files = [
            'soloylibre_ultimate_server.py',
            'core/soloylibre_core.py',
            'modules/auth_security.py',
            'modules/content_manager.py',
            'modules/wordpress_core.py',
            'templates/admin_login.py',
            'templates/admin_dashboard.py',
            'templates/admin_posts.py',
            'templates/backend_page.py',
            'templates/frontend_page.py',
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = f"/Users/<USER>/Desktop/SoloYLibre-WordPress/{file_path}"
            if os.path.exists(full_path):
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path}")
                missing_files.append(file_path)
        
        if missing_files:
            self.issues.extend(missing_files)
    
    def audit_database_completeness(self):
        """Auditar completitud de la base de datos"""
        print("\n🗄️ AUDITANDO BASE DE DATOS...")
        
        # Verificar que la base de datos existe
        db_path = "/Users/<USER>/Desktop/SoloYLibre-WordPress/soloylibre_ultimate.db"
        if os.path.exists(db_path):
            print("✅ Base de datos existe")
            
            # Verificar contenido básico
            try:
                import sqlite3
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Verificar tablas principales
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                required_tables = [
                    'wp_users', 'wp_posts', 'wp_comments', 'wp_options',
                    'wp_postmeta', 'wp_usermeta', 'wp_terms', 'wp_term_relationships'
                ]
                
                for table in required_tables:
                    if table in tables:
                        print(f"   ✅ Tabla {table}")
                    else:
                        print(f"   ❌ Tabla {table}")
                        self.issues.append(f"Tabla faltante: {table}")
                
                # Verificar usuario admin
                cursor.execute("SELECT COUNT(*) FROM wp_users WHERE user_role = 'administrator'")
                admin_count = cursor.fetchone()[0]
                
                if admin_count > 0:
                    print("   ✅ Usuario administrador existe")
                else:
                    print("   ❌ No hay usuario administrador")
                    self.issues.append("No hay usuario administrador")
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ Error verificando BD: {e}")
                self.issues.append(f"Error de BD: {e}")
        else:
            print("❌ Base de datos no existe")
            self.issues.append("Base de datos no existe")
    
    def generate_fix_plan(self):
        """Generar plan de correcciones"""
        print("\n" + "="*80)
        print("📋 PLAN DE CORRECCIONES")
        print("="*80)
        
        if self.login_issues:
            print("\n🔐 PROBLEMAS DE LOGIN A CORREGIR:")
            for i, issue in enumerate(self.login_issues, 1):
                print(f"   {i}. {issue}")
        
        if self.incomplete_pages:
            print("\n⚠️ PÁGINAS INCOMPLETAS A TERMINAR:")
            for i, page in enumerate(self.incomplete_pages, 1):
                print(f"   {i}. {page}")
        
        if self.missing_features:
            print("\n❌ FUNCIONALIDADES FALTANTES A IMPLEMENTAR:")
            for i, feature in enumerate(self.missing_features, 1):
                print(f"   {i}. {feature}")
        
        if self.issues:
            print("\n🔧 PROBLEMAS TÉCNICOS A RESOLVER:")
            for i, issue in enumerate(self.issues, 1):
                print(f"   {i}. {issue}")
        
        # Prioridades
        print("\n🎯 PRIORIDADES DE IMPLEMENTACIÓN:")
        print("   1. 🔐 ALTA: Corregir sistema de login")
        print("   2. 📝 ALTA: Completar editor de posts/páginas")
        print("   3. 📷 MEDIA: Implementar upload de medios")
        print("   4. 👥 MEDIA: Completar gestión de usuarios")
        print("   5. 🎨 MEDIA: Implementar customizer de temas")
        print("   6. 🔌 BAJA: Agregar sistema de plugins")
        print("   7. 🛠️ BAJA: Herramientas adicionales")
    
    def run_complete_audit(self):
        """Ejecutar auditoría completa"""
        self.print_header()
        
        # Ejecutar todas las auditorías
        self.audit_login_system()
        self.audit_admin_pages()
        self.audit_missing_features()
        self.audit_file_structure()
        self.audit_database_completeness()
        
        # Generar plan de correcciones
        self.generate_fix_plan()
        
        # Resumen final
        print("\n" + "="*80)
        print("📊 RESUMEN DE AUDITORÍA")
        print("="*80)
        
        total_issues = len(self.login_issues) + len(self.incomplete_pages) + len(self.missing_features) + len(self.issues)
        
        print(f"🔐 Problemas de login: {len(self.login_issues)}")
        print(f"⚠️ Páginas incompletas: {len(self.incomplete_pages)}")
        print(f"❌ Funcionalidades faltantes: {len(self.missing_features)}")
        print(f"🔧 Problemas técnicos: {len(self.issues)}")
        print(f"📊 Total de issues: {total_issues}")
        
        if total_issues == 0:
            print("\n🎉 ¡SISTEMA PERFECTO! No hay problemas encontrados")
        elif total_issues <= 5:
            print("\n✅ Sistema en buen estado, pocos ajustes necesarios")
        elif total_issues <= 15:
            print("\n⚠️ Sistema funcional, necesita mejoras importantes")
        else:
            print("\n🔧 Sistema necesita trabajo significativo")
        
        print("\n🇩🇴 Auditoría completada desde San José de Ocoa")
        print("👨‍💻 Por Jose L Encarnacion (JoseTusabe)")
        print("="*80)
        
        return {
            'login_issues': self.login_issues,
            'incomplete_pages': self.incomplete_pages,
            'missing_features': self.missing_features,
            'technical_issues': self.issues,
            'total_issues': total_issues
        }

if __name__ == '__main__':
    auditor = SystemAudit()
    results = auditor.run_complete_audit()
