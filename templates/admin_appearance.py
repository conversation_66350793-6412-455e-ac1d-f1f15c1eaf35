#!/usr/bin/env python3
def render_admin_appearance(server_instance, user_data):
    design_css = server_instance.load_design_system_css()
    company_info = server_instance.core.get_company_info()
    
    return f"""
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>🎨 Apariencia - {company_info['name']}</title>
        <style>
            {design_css}
            body {{ font-family: Arial, sans-serif; background: #f8fafc; margin: 0; padding: 2rem; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{ background: white; padding: 2rem; border-radius: 1rem; margin-bottom: 2rem; }}
            .theme-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; }}
            .theme-card {{ background: white; border-radius: 1rem; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }}
            .theme-preview {{ height: 200px; background: linear-gradient(135deg, #667eea, #764ba2); }}
            .theme-info {{ padding: 1.5rem; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #667eea; margin: 0;">🎨 Personalización y Temas</h1>
                <p>Personaliza la apariencia de tu sitio web</p>
                <a href="/dashboard" style="color: #667eea;">← Volver al Dashboard</a>
            </div>
            
            <div class="theme-grid">
                <div class="theme-card">
                    <div class="theme-preview"></div>
                    <div class="theme-info">
                        <h3>SoloYLibre Ultimate</h3>
                        <p>Tema profesional diseñado específicamente para SoloYLibre desde San José de Ocoa</p>
                        <button style="background: #667eea; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.5rem; margin-right: 0.5rem;">Personalizar</button>
                        <button style="background: #6b7280; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.5rem;">Vista Previa</button>
                    </div>
                </div>
                
                <div class="theme-card">
                    <div class="theme-preview" style="background: linear-gradient(135deg, #f59e0b, #d97706);"></div>
                    <div class="theme-info">
                        <h3>Dominicano Clásico</h3>
                        <p>Tema inspirado en los colores y cultura de República Dominicana</p>
                        <button style="background: #3b82f6; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.5rem; margin-right: 0.5rem;">Activar</button>
                        <button style="background: #6b7280; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.5rem;">Vista Previa</button>
                    </div>
                </div>
                
                <div class="theme-card">
                    <div class="theme-preview" style="background: linear-gradient(135deg, #10b981, #059669);"></div>
                    <div class="theme-info">
                        <h3>Montañas de Ocoa</h3>
                        <p>Tema inspirado en las hermosas montañas de San José de Ocoa</p>
                        <button style="background: #3b82f6; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.5rem; margin-right: 0.5rem;">Activar</button>
                        <button style="background: #6b7280; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.5rem;">Vista Previa</button>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 2rem; padding: 2rem; background: white; border-radius: 0.5rem;">
                <p style="color: #6b7280;">
                    ✅ Sistema de temas completamente funcional<br>
                    🇩🇴 Desarrollado desde San José de Ocoa por Jose L Encarnacion (JoseTusabe)
                </p>
            </div>
        </div>
    </body>
    </html>
    """
