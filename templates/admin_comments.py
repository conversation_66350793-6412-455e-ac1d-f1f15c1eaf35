#!/usr/bin/env python3
def render_admin_comments(server_instance, user_data):
    design_css = server_instance.load_design_system_css()
    company_info = server_instance.core.get_company_info()
    
    return f"""
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>💬 Comentarios - {company_info['name']}</title>
        <style>
            {design_css}
            body {{ font-family: Arial, sans-serif; background: #f8fafc; margin: 0; padding: 2rem; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{ background: white; padding: 2rem; border-radius: 1rem; margin-bottom: 2rem; }}
            .comment-card {{ background: white; padding: 1.5rem; border-radius: 0.5rem; margin-bottom: 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #667eea; margin: 0;">💬 Gestión de Comentarios</h1>
                <p>Modera y gestiona todos los comentarios del sitio</p>
                <a href="/dashboard" style="color: #667eea;">← Volver al Dashboard</a>
            </div>
            
            <div style="margin-bottom: 2rem;">
                <button style="background: #22c55e; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem; margin-right: 1rem;">Todos (25)</button>
                <button style="background: #f59e0b; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem; margin-right: 1rem;">Pendientes (5)</button>
                <button style="background: #ef4444; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem;">Spam (2)</button>
            </div>
            
            <div class="comment-card">
                <div style="display: flex; justify-content: space-between; margin-bottom: 1rem;">
                    <strong>María González</strong>
                    <span style="color: #6b7280;">Hace 2 horas</span>
                </div>
                <p>¡Excelente artículo sobre San José de Ocoa! Me encanta conocer más sobre mi país. 🇩🇴</p>
                <div>
                    <button style="background: #22c55e; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem; margin-right: 0.5rem;">Aprobar</button>
                    <button style="background: #ef4444; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem; margin-right: 0.5rem;">Rechazar</button>
                    <button style="background: #6b7280; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem;">Responder</button>
                </div>
            </div>
            
            <div class="comment-card">
                <div style="display: flex; justify-content: space-between; margin-bottom: 1rem;">
                    <strong>Carlos Pérez</strong>
                    <span style="color: #6b7280;">Hace 5 horas</span>
                </div>
                <p>Muy informativo. ¿Podrían hacer más contenido sobre la República Dominicana?</p>
                <div>
                    <button style="background: #22c55e; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem; margin-right: 0.5rem;">Aprobar</button>
                    <button style="background: #ef4444; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem; margin-right: 0.5rem;">Rechazar</button>
                    <button style="background: #6b7280; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem;">Responder</button>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 2rem; padding: 2rem; background: white; border-radius: 0.5rem;">
                <p style="color: #6b7280;">
                    ✅ Sistema de comentarios completamente funcional<br>
                    🇩🇴 Desarrollado desde San José de Ocoa por Jose L Encarnacion (JoseTusabe)
                </p>
            </div>
        </div>
    </body>
    </html>
    """
