#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - Admin Dashboard Template
Dashboard administrativo súper avanzado con todas las funcionalidades
Desarrollado por <PERSON>nacion (JoseTusabe)
Desde San José de <PERSON>, República Dominicana 🇩🇴
"""

def render_admin_dashboard(server_instance, user_data):
    """Renderizar dashboard administrativo súper avanzado"""
    
    design_css = server_instance.load_design_system_css()
    system_info = server_instance.core.get_system_info()
    company_info = server_instance.core.get_company_info()
    stats = server_instance.core.get_system_stats()
    
    # Obtener posts recientes
    recent_posts = server_instance.content.get_posts(limit=5)
    
    return f"""
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🇩🇴 Dashboard - {company_info['name']}</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link rel="manifest" href="/manifest.json">
        <meta name="theme-color" content="#667eea">
        <style>
            {design_css}
            
            * {{
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }}
            
            body {{
                font-family: var(--font-family);
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                min-height: 100vh;
                color: var(--color-gray-900);
            }}
            
            .dashboard-container {{
                display: grid;
                grid-template-columns: 280px 1fr;
                min-height: 100vh;
            }}
            
            /* SIDEBAR */
            .sidebar {{
                background: linear-gradient(180deg, var(--color-primary) 0%, var(--color-secondary) 100%);
                color: var(--color-white);
                padding: var(--space-6);
                box-shadow: var(--shadow-xl);
                position: relative;
                overflow-y: auto;
            }}
            
            .sidebar::before {{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
                opacity: 0.3;
            }}
            
            .sidebar-content {{
                position: relative;
                z-index: 1;
            }}
            
            .logo-section {{
                text-align: center;
                margin-bottom: var(--space-8);
                padding-bottom: var(--space-6);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }}
            
            .logo-section h1 {{
                font-size: var(--text-2xl);
                font-weight: var(--font-black);
                margin-bottom: var(--space-2);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }}
            
            .logo-section p {{
                font-size: var(--text-sm);
                opacity: 0.9;
                font-weight: var(--font-medium);
            }}
            
            .user-info {{
                background: rgba(255, 255, 255, 0.1);
                padding: var(--space-4);
                border-radius: var(--radius-xl);
                margin-bottom: var(--space-8);
                backdrop-filter: blur(10px);
            }}
            
            .user-avatar {{
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: linear-gradient(135deg, var(--color-accent), #ff6b6b);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: var(--text-xl);
                font-weight: var(--font-bold);
                margin: 0 auto var(--space-3);
            }}
            
            .user-name {{
                text-align: center;
                font-weight: var(--font-bold);
                margin-bottom: var(--space-1);
            }}
            
            .user-role {{
                text-align: center;
                font-size: var(--text-sm);
                opacity: 0.8;
            }}
            
            .nav-menu {{
                list-style: none;
            }}
            
            .nav-item {{
                margin-bottom: var(--space-2);
            }}
            
            .nav-link {{
                display: flex;
                align-items: center;
                gap: var(--space-3);
                padding: var(--space-3) var(--space-4);
                color: var(--color-white);
                text-decoration: none;
                border-radius: var(--radius-lg);
                transition: all 300ms var(--timing-smooth);
                font-weight: var(--font-medium);
                position: relative;
                overflow: hidden;
            }}
            
            .nav-link::before {{
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 500ms;
            }}
            
            .nav-link:hover {{
                background: rgba(255, 255, 255, 0.2);
                transform: translateX(5px);
            }}
            
            .nav-link:hover::before {{
                left: 100%;
            }}
            
            .nav-link.active {{
                background: rgba(255, 255, 255, 0.3);
                box-shadow: var(--shadow-md);
            }}
            
            .nav-icon {{
                font-size: var(--text-lg);
                width: 20px;
                text-align: center;
            }}
            
            /* MAIN CONTENT */
            .main-content {{
                padding: var(--space-8);
                overflow-y: auto;
            }}
            
            .header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: var(--space-8);
                background: var(--color-white);
                padding: var(--space-6);
                border-radius: var(--radius-2xl);
                box-shadow: var(--shadow-lg);
            }}
            
            .header-title {{
                font-size: var(--text-3xl);
                font-weight: var(--font-black);
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }}
            
            .header-actions {{
                display: flex;
                gap: var(--space-4);
                align-items: center;
            }}
            
            .btn-header {{
                padding: var(--space-3) var(--space-5);
                border: none;
                border-radius: var(--radius-lg);
                font-weight: var(--font-semibold);
                cursor: pointer;
                transition: all 300ms var(--timing-smooth);
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: var(--space-2);
            }}
            
            .btn-primary {{
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                color: var(--color-white);
            }}
            
            .btn-primary:hover {{
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }}
            
            .btn-outline {{
                background: transparent;
                border: 2px solid var(--color-primary);
                color: var(--color-primary);
            }}
            
            .btn-outline:hover {{
                background: var(--color-primary);
                color: var(--color-white);
            }}
            
            /* STATS GRID */
            .stats-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: var(--space-6);
                margin-bottom: var(--space-8);
            }}
            
            .stat-card {{
                background: var(--color-white);
                padding: var(--space-6);
                border-radius: var(--radius-2xl);
                box-shadow: var(--shadow-lg);
                position: relative;
                overflow: hidden;
                transition: all 300ms var(--timing-smooth);
            }}
            
            .stat-card::before {{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            }}
            
            .stat-card:hover {{
                transform: translateY(-5px);
                box-shadow: var(--shadow-2xl);
            }}
            
            .stat-header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: var(--space-4);
            }}
            
            .stat-title {{
                font-size: var(--text-sm);
                font-weight: var(--font-semibold);
                color: var(--color-gray-600);
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            
            .stat-icon {{
                width: 50px;
                height: 50px;
                border-radius: var(--radius-xl);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: var(--text-xl);
                color: var(--color-white);
            }}
            
            .stat-value {{
                font-size: var(--text-4xl);
                font-weight: var(--font-black);
                color: var(--color-gray-900);
                margin-bottom: var(--space-2);
            }}
            
            .stat-change {{
                font-size: var(--text-sm);
                font-weight: var(--font-semibold);
                display: flex;
                align-items: center;
                gap: var(--space-1);
            }}
            
            .stat-change.positive {{
                color: var(--color-success);
            }}
            
            .stat-change.negative {{
                color: var(--color-error);
            }}
            
            /* CONTENT GRID */
            .content-grid {{
                display: grid;
                grid-template-columns: 2fr 1fr;
                gap: var(--space-8);
                margin-bottom: var(--space-8);
            }}
            
            .content-section {{
                background: var(--color-white);
                padding: var(--space-8);
                border-radius: var(--radius-2xl);
                box-shadow: var(--shadow-lg);
            }}
            
            .section-title {{
                font-size: var(--text-2xl);
                font-weight: var(--font-bold);
                margin-bottom: var(--space-6);
                color: var(--color-gray-900);
                display: flex;
                align-items: center;
                gap: var(--space-3);
            }}
            
            .section-icon {{
                width: 40px;
                height: 40px;
                border-radius: var(--radius-lg);
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                display: flex;
                align-items: center;
                justify-content: center;
                color: var(--color-white);
            }}
            
            /* RECENT POSTS */
            .post-item {{
                display: flex;
                gap: var(--space-4);
                padding: var(--space-4);
                border-radius: var(--radius-xl);
                transition: all 300ms var(--timing-smooth);
                margin-bottom: var(--space-4);
                border: 1px solid var(--color-gray-200);
            }}
            
            .post-item:hover {{
                background: var(--color-gray-50);
                transform: translateX(5px);
            }}
            
            .post-thumbnail {{
                width: 60px;
                height: 60px;
                border-radius: var(--radius-lg);
                background: linear-gradient(135deg, var(--color-accent), #ff6b6b);
                display: flex;
                align-items: center;
                justify-content: center;
                color: var(--color-white);
                font-size: var(--text-xl);
                flex-shrink: 0;
            }}
            
            .post-content {{
                flex: 1;
            }}
            
            .post-title {{
                font-weight: var(--font-bold);
                margin-bottom: var(--space-1);
                color: var(--color-gray-900);
            }}
            
            .post-meta {{
                font-size: var(--text-sm);
                color: var(--color-gray-600);
                display: flex;
                gap: var(--space-4);
            }}
            
            /* QUICK ACTIONS */
            .quick-actions {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: var(--space-4);
            }}
            
            .quick-action {{
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                color: var(--color-white);
                padding: var(--space-6);
                border-radius: var(--radius-xl);
                text-decoration: none;
                text-align: center;
                transition: all 300ms var(--timing-smooth);
                position: relative;
                overflow: hidden;
            }}
            
            .quick-action::before {{
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 500ms;
            }}
            
            .quick-action:hover {{
                transform: translateY(-5px);
                box-shadow: var(--shadow-xl);
            }}
            
            .quick-action:hover::before {{
                left: 100%;
            }}
            
            .quick-action-icon {{
                font-size: var(--text-3xl);
                margin-bottom: var(--space-3);
                display: block;
            }}
            
            .quick-action-title {{
                font-weight: var(--font-bold);
                font-size: var(--text-lg);
            }}
            
            /* RESPONSIVE */
            @media (max-width: 1024px) {{
                .dashboard-container {{
                    grid-template-columns: 1fr;
                }}
                
                .sidebar {{
                    display: none;
                }}
                
                .content-grid {{
                    grid-template-columns: 1fr;
                }}
                
                .stats-grid {{
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                }}
            }}
            
            @media (max-width: 768px) {{
                .main-content {{
                    padding: var(--space-4);
                }}
                
                .header {{
                    flex-direction: column;
                    gap: var(--space-4);
                    text-align: center;
                }}
                
                .stats-grid {{
                    grid-template-columns: 1fr;
                }}
                
                .quick-actions {{
                    grid-template-columns: 1fr;
                }}
            }}
            
            /* ANIMATIONS */
            .animate-fade-in {{
                animation: fadeIn 0.6s ease-out;
            }}
            
            .animate-slide-up {{
                animation: slideUp 0.8s ease-out;
            }}
            
            @keyframes fadeIn {{
                from {{ opacity: 0; }}
                to {{ opacity: 1; }}
            }}
            
            @keyframes slideUp {{
                from {{
                    opacity: 0;
                    transform: translateY(30px);
                }}
                to {{
                    opacity: 1;
                    transform: translateY(0);
                }}
            }}
            
            /* LOADING STATES */
            .loading {{
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 50%;
                border-top-color: var(--color-white);
                animation: spin 1s ease-in-out infinite;
            }}
            
            @keyframes spin {{
                to {{ transform: rotate(360deg); }}
            }}
        </style>
    </head>
    <body>
        <div class="dashboard-container">
            <!-- SIDEBAR -->
            <aside class="sidebar">
                <div class="sidebar-content">
                    <div class="logo-section">
                        <h1>🇩🇴 SoloYLibre</h1>
                        <p>WordPress Ultimate</p>
                    </div>
                    
                    <div class="user-info">
                        <div class="user-avatar">
                            {user_data.get('display_name', 'Admin')[0].upper()}
                        </div>
                        <div class="user-name">{user_data.get('display_name', 'Administrador')}</div>
                        <div class="user-role">{user_data.get('role', 'administrator').title()}</div>
                    </div>
                    
                    <nav>
                        <ul class="nav-menu">
                            <li class="nav-item">
                                <a href="/dashboard" class="nav-link active">
                                    <i class="nav-icon fas fa-tachometer-alt"></i>
                                    Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/soloylibre-admin/posts" class="nav-link">
                                    <i class="nav-icon fas fa-edit"></i>
                                    Posts
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/soloylibre-admin/pages" class="nav-link">
                                    <i class="nav-icon fas fa-file-alt"></i>
                                    Páginas
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/soloylibre-admin/media" class="nav-link">
                                    <i class="nav-icon fas fa-photo-video"></i>
                                    Medios
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/soloylibre-admin/comments" class="nav-link">
                                    <i class="nav-icon fas fa-comments"></i>
                                    Comentarios
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/soloylibre-admin/appearance" class="nav-link">
                                    <i class="nav-icon fas fa-paint-brush"></i>
                                    Apariencia
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/soloylibre-admin/plugins" class="nav-link">
                                    <i class="nav-icon fas fa-plug"></i>
                                    Plugins
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/soloylibre-admin/users" class="nav-link">
                                    <i class="nav-icon fas fa-users"></i>
                                    Usuarios
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/soloylibre-admin/tools" class="nav-link">
                                    <i class="nav-icon fas fa-tools"></i>
                                    Herramientas
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/soloylibre-admin/settings" class="nav-link">
                                    <i class="nav-icon fas fa-cog"></i>
                                    Configuración
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/api/" class="nav-link">
                                    <i class="nav-icon fas fa-code"></i>
                                    API REST
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/" class="nav-link">
                                    <i class="nav-icon fas fa-external-link-alt"></i>
                                    Ver Sitio
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/logout" class="nav-link">
                                    <i class="nav-icon fas fa-sign-out-alt"></i>
                                    Cerrar Sesión
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </aside>
            
            <!-- MAIN CONTENT -->
            <main class="main-content">
                <header class="header animate-fade-in">
                    <div>
                        <h1 class="header-title">Dashboard Ultimate</h1>
                        <p>Bienvenido de vuelta, {user_data.get('display_name', 'Administrador')}! 🇩🇴</p>
                    </div>
                    <div class="header-actions">
                        <a href="/soloylibre-admin/posts/new" class="btn-header btn-primary">
                            <i class="fas fa-plus"></i>
                            Nuevo Post
                        </a>
                        <a href="/" class="btn-header btn-outline">
                            <i class="fas fa-eye"></i>
                            Ver Sitio
                        </a>
                    </div>
                </header>

                <!-- STATS GRID -->
                <section class="stats-grid animate-slide-up">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Posts Publicados</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #4ade80, #22c55e);">
                                <i class="fas fa-edit"></i>
                            </div>
                        </div>
                        <div class="stat-value">{stats.get('posts', 0)}</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +{stats.get('posts_created', 0)} este mes
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Páginas Activas</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #0ea5e9, #0284c7);">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="stat-value">{stats.get('pages', 0)}</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +2 esta semana
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Visitantes Únicos</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value">{stats.get('unique_visitors', 0)}</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +{stats.get('page_views', 0)} vistas hoy
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Comentarios</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <i class="fas fa-comments"></i>
                            </div>
                        </div>
                        <div class="stat-value">{stats.get('comments', 0)}</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            Moderación activa
                        </div>
                    </div>
                </section>

                <!-- CONTENT GRID -->
                <section class="content-grid animate-slide-up">
                    <div class="content-section">
                        <h2 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-newspaper"></i>
                            </div>
                            Posts Recientes
                        </h2>

                        <div class="recent-posts">
                            {"".join([f'''
                            <div class="post-item">
                                <div class="post-thumbnail">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="post-content">
                                    <div class="post-title">{post.get('post_title', 'Sin título')}</div>
                                    <div class="post-meta">
                                        <span><i class="fas fa-user"></i> {post.get('author_name', 'Autor')}</span>
                                        <span><i class="fas fa-calendar"></i> {post.get('formatted_date', 'Fecha')}</span>
                                        <span><i class="fas fa-eye"></i> {post.get('view_count', 0)} vistas</span>
                                    </div>
                                </div>
                            </div>
                            ''' for post in recent_posts[:5]])}

                            {'''
                            <div class="post-item">
                                <div class="post-thumbnail">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="post-content">
                                    <div class="post-title">No hay posts aún</div>
                                    <div class="post-meta">
                                        <span><i class="fas fa-info-circle"></i> Crea tu primer post sobre San José de Ocoa</span>
                                    </div>
                                </div>
                            </div>
                            ''' if not recent_posts else ''}
                        </div>

                        <div style="text-align: center; margin-top: var(--space-6);">
                            <a href="/soloylibre-admin/posts" class="btn-header btn-primary">
                                <i class="fas fa-list"></i>
                                Ver Todos los Posts
                            </a>
                        </div>
                    </div>

                    <div class="content-section">
                        <h2 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            Acciones Rápidas
                        </h2>

                        <div class="quick-actions">
                            <a href="/soloylibre-admin/posts/new" class="quick-action">
                                <i class="quick-action-icon fas fa-plus"></i>
                                <div class="quick-action-title">Nuevo Post</div>
                            </a>

                            <a href="/soloylibre-admin/pages/new" class="quick-action" style="background: linear-gradient(135deg, var(--color-accent), #ff6b6b);">
                                <i class="quick-action-icon fas fa-file-plus"></i>
                                <div class="quick-action-title">Nueva Página</div>
                            </a>

                            <a href="/soloylibre-admin/media" class="quick-action" style="background: linear-gradient(135deg, var(--color-mountain-green), #22c55e);">
                                <i class="quick-action-icon fas fa-upload"></i>
                                <div class="quick-action-title">Subir Media</div>
                            </a>

                            <a href="/soloylibre-admin/appearance" class="quick-action" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <i class="quick-action-icon fas fa-paint-brush"></i>
                                <div class="quick-action-title">Personalizar</div>
                            </a>

                            <a href="/soloylibre-admin/settings" class="quick-action" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <i class="quick-action-icon fas fa-cog"></i>
                                <div class="quick-action-title">Configurar</div>
                            </a>

                            <a href="/api/" class="quick-action" style="background: linear-gradient(135deg, #0ea5e9, #0284c7);">
                                <i class="quick-action-icon fas fa-code"></i>
                                <div class="quick-action-title">API REST</div>
                            </a>
                        </div>

                        <div style="margin-top: var(--space-8); padding: var(--space-6); background: var(--color-gray-100); border-radius: var(--radius-xl);">
                            <h3 style="margin-bottom: var(--space-4); color: var(--color-gray-800); font-weight: var(--font-bold);">
                                <i class="fas fa-info-circle"></i> Sistema WordPress Ultimate
                            </h3>
                            <div style="font-size: var(--text-sm); color: var(--color-gray-700); line-height: 1.6;">
                                <p><strong>Versión:</strong> {system_info['version']}</p>
                                <p><strong>Build:</strong> {system_info['build']}</p>
                                <p><strong>Uptime:</strong> {str(stats.get('uptime', 'N/A')).split('.')[0]}</p>
                                <p><strong>Desarrollador:</strong> {company_info['developer']}</p>
                                <p><strong>Ubicación:</strong> {company_info['location']}</p>
                                <p><strong>Servidor:</strong> {company_info['server']}</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- SYSTEM STATUS -->
                <section class="content-section animate-slide-up" style="margin-top: var(--space-8);">
                    <h2 class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        Estado del Sistema
                    </h2>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Base de Datos</div>
                                <div class="stat-icon" style="background: linear-gradient(135deg, #4ade80, #22c55e);">
                                    <i class="fas fa-database"></i>
                                </div>
                            </div>
                            <div class="stat-value">✅ Conectada</div>
                            <div class="stat-change positive">
                                <i class="fas fa-check-circle"></i>
                                SQLite funcionando
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Seguridad</div>
                                <div class="stat-icon" style="background: linear-gradient(135deg, #0ea5e9, #0284c7);">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                            </div>
                            <div class="stat-value">🔒 Activa</div>
                            <div class="stat-change positive">
                                <i class="fas fa-lock"></i>
                                JWT + Encriptación
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">API REST</div>
                                <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                    <i class="fas fa-plug"></i>
                                </div>
                            </div>
                            <div class="stat-value">🚀 Online</div>
                            <div class="stat-change positive">
                                <i class="fas fa-rocket"></i>
                                {stats.get('api_calls', 0)} llamadas
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">PWA</div>
                                <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                            </div>
                            <div class="stat-value">📱 Activa</div>
                            <div class="stat-change positive">
                                <i class="fas fa-check"></i>
                                Service Worker OK
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>

        <script>
            // Registrar Service Worker
            if ('serviceWorker' in navigator) {{
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => console.log('✅ Service Worker registrado'))
                    .catch(error => console.log('❌ Error SW:', error));
            }}

            // Animaciones de entrada
            document.addEventListener('DOMContentLoaded', function() {{
                const elements = document.querySelectorAll('.animate-slide-up');
                elements.forEach((el, index) => {{
                    el.style.animationDelay = `${{index * 0.1}}s`;
                }});

                // Actualizar estadísticas cada 30 segundos
                setInterval(updateStats, 30000);
            }});

            // Función para actualizar estadísticas
            async function updateStats() {{
                try {{
                    const response = await fetch('/api/stats');
                    const data = await response.json();

                    if (data.status === 'success') {{
                        // Actualizar valores en tiempo real
                        console.log('📊 Estadísticas actualizadas:', data.data);
                    }}
                }} catch (error) {{
                    console.error('❌ Error actualizando stats:', error);
                }}
            }}

            // Navegación activa
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {{
                if (link.getAttribute('href') === currentPath) {{
                    link.classList.add('active');
                }} else {{
                    link.classList.remove('active');
                }}
            }});

            // Efectos hover mejorados
            document.querySelectorAll('.stat-card, .quick-action').forEach(card => {{
                card.addEventListener('mouseenter', function() {{
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                }});

                card.addEventListener('mouseleave', function() {{
                    this.style.transform = 'translateY(0) scale(1)';
                }});
            }});

            // Notificaciones en tiempo real (simuladas)
            function showNotification(message, type = 'info') {{
                const notification = document.createElement('div');
                notification.className = `alert alert-${{type}} animate-fade-in`;
                notification.style.position = 'fixed';
                notification.style.top = '20px';
                notification.style.right = '20px';
                notification.style.zIndex = '9999';
                notification.style.maxWidth = '300px';
                notification.innerHTML = message;

                document.body.appendChild(notification);

                setTimeout(() => {{
                    notification.remove();
                }}, 5000);
            }}

            // Simular notificaciones cada 2 minutos
            setInterval(() => {{
                const messages = [
                    '🇩🇴 Sistema funcionando perfectamente desde San José de Ocoa',
                    '📊 Nuevas métricas disponibles en el dashboard',
                    '🔒 Seguridad del sistema verificada',
                    '🚀 API REST respondiendo correctamente',
                    '📱 PWA funcionando sin problemas'
                ];

                const randomMessage = messages[Math.floor(Math.random() * messages.length)];
                showNotification(randomMessage, 'success');
            }}, 120000);

            // Bienvenida inicial
            setTimeout(() => {{
                showNotification('🎉 ¡Bienvenido al Dashboard Ultimate de SoloYLibre! Sistema completamente funcional.', 'success');
            }}, 2000);
        </script>
    </body>
    </html>
    """
