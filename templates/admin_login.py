#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - Admin Login Template
Template de login administrativo súper avanzado
Desarrollado por <PERSON> (JoseTusabe)
Desde San José de <PERSON>, República Dominicana 🇩🇴
"""

def render_admin_login(server_instance, error=""):
    """Renderizar página de login administrativo"""
    
    design_css = server_instance.load_design_system_css()
    error_html = f'<div class="alert alert-error animate-fade-in">{error}</div>' if error else ''
    system_info = server_instance.core.get_system_info()
    company_info = server_instance.core.get_company_info()
    
    return f"""
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🇩🇴 {company_info['name']} - Sistema Ultimate</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link rel="manifest" href="/manifest.json">
        <meta name="theme-color" content="#667eea">
        <meta name="description" content="Sistema WordPress Ultimate desarrollado desde San José de Ocoa, República Dominicana">
        <style>
            {design_css}
            
            body {{
                background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 25%, var(--color-accent) 50%, #ff6b6b 75%, var(--color-mountain-green) 100%);
                background-size: 400% 400%;
                animation: gradientShift 25s ease infinite;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: var(--space-5);
                position: relative;
                overflow: hidden;
            }}
            
            @keyframes gradientShift {{
                0% {{ background-position: 0% 50%; }}
                50% {{ background-position: 100% 50%; }}
                100% {{ background-position: 0% 50%; }}
            }}
            
            .floating-elements {{
                position: absolute;
                width: 100%;
                height: 100%;
                overflow: hidden;
                z-index: 1;
            }}
            
            .floating-shape {{
                position: absolute;
                background: var(--color-glass);
                border-radius: 50%;
                animation: floatAnimation 30s linear infinite;
            }}
            
            .floating-shape:nth-child(1) {{ width: 100px; height: 100px; left: 10%; animation-delay: 0s; }}
            .floating-shape:nth-child(2) {{ width: 150px; height: 150px; left: 80%; animation-delay: 10s; }}
            .floating-shape:nth-child(3) {{ width: 80px; height: 80px; left: 50%; animation-delay: 20s; }}
            .floating-shape:nth-child(4) {{ width: 120px; height: 120px; left: 20%; animation-delay: 15s; }}
            .floating-shape:nth-child(5) {{ width: 90px; height: 90px; left: 70%; animation-delay: 5s; }}
            
            @keyframes floatAnimation {{
                0% {{ transform: translateY(100vh) rotate(0deg); opacity: 0; }}
                10% {{ opacity: 0.6; }}
                90% {{ opacity: 0.6; }}
                100% {{ transform: translateY(-100px) rotate(360deg); opacity: 0; }}
            }}
            
            .login-container {{
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(30px);
                border-radius: var(--radius-3xl);
                padding: var(--space-16) var(--space-12);
                box-shadow: var(--shadow-2xl);
                width: 100%;
                max-width: 520px;
                position: relative;
                z-index: 10;
                border: 1px solid rgba(255, 255, 255, 0.3);
                animation: slideUpFade 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            }}
            
            @keyframes slideUpFade {{
                from {{
                    opacity: 0;
                    transform: translateY(50px) scale(0.95);
                }}
                to {{
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }}
            }}
            
            .logo-section {{
                text-align: center;
                margin-bottom: var(--space-12);
                position: relative;
            }}
            
            .logo-section::before {{
                content: '';
                position: absolute;
                top: -25px;
                left: 50%;
                transform: translateX(-50%);
                width: 120px;
                height: 5px;
                background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
                border-radius: 3px;
                animation: pulse 2s ease-in-out infinite;
            }}
            
            @keyframes pulse {{
                0%, 100% {{ opacity: 1; transform: translateX(-50%) scaleX(1); }}
                50% {{ opacity: 0.7; transform: translateX(-50%) scaleX(1.1); }}
            }}
            
            .logo-section h1 {{
                font-size: var(--text-6xl);
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary), var(--color-accent));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: var(--space-4);
                font-weight: var(--font-black);
                letter-spacing: -2px;
                animation: textGlow 3s ease-in-out infinite;
            }}
            
            @keyframes textGlow {{
                0%, 100% {{ filter: drop-shadow(0 0 10px rgba(102, 126, 234, 0.3)); }}
                50% {{ filter: drop-shadow(0 0 20px rgba(240, 147, 251, 0.5)); }}
            }}
            
            .logo-section p {{
                color: var(--color-gray-600);
                font-size: var(--text-lg);
                font-weight: var(--font-semibold);
                margin-bottom: var(--space-2);
            }}
            
            .version-info {{
                color: var(--color-gray-500);
                font-size: var(--text-sm);
                font-weight: var(--font-medium);
            }}
            
            .form-section {{
                margin-bottom: var(--space-10);
            }}
            
            .form-group {{
                margin-bottom: var(--space-8);
                position: relative;
            }}
            
            .form-group label {{
                display: block;
                margin-bottom: var(--space-3);
                font-weight: var(--font-bold);
                color: var(--color-gray-900);
                font-size: var(--text-base);
                display: flex;
                align-items: center;
                gap: var(--space-2);
            }}
            
            .input-wrapper {{
                position: relative;
            }}
            
            .input-icon {{
                position: absolute;
                right: var(--space-5);
                top: 50%;
                transform: translateY(-50%);
                font-size: var(--text-xl);
                color: var(--color-gray-500);
                transition: all 300ms var(--timing-smooth);
                pointer-events: none;
            }}
            
            .input:focus + .input-icon {{
                color: var(--color-primary);
                transform: translateY(-50%) scale(1.1);
            }}
            
            .btn-login {{
                width: 100%;
                padding: var(--space-5) var(--space-6);
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                color: var(--color-white);
                border: none;
                border-radius: var(--radius-xl);
                font-size: var(--text-lg);
                font-weight: var(--font-extrabold);
                cursor: pointer;
                transition: all 300ms var(--timing-smooth);
                text-transform: uppercase;
                letter-spacing: 1px;
                position: relative;
                overflow: hidden;
                margin-top: var(--space-6);
            }}
            
            .btn-login::before {{
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 500ms;
            }}
            
            .btn-login:hover::before {{
                left: 100%;
            }}
            
            .btn-login:hover {{
                transform: translateY(-3px);
                box-shadow: var(--shadow-xl);
                background: linear-gradient(135deg, #5a6fd8, #6a42a0);
            }}
            
            .btn-login:active {{
                transform: translateY(-1px);
            }}
            
            .credentials-panel {{
                background: linear-gradient(135deg, var(--color-accent), #ff6b6b);
                color: var(--color-white);
                padding: var(--space-8);
                border-radius: var(--radius-2xl);
                margin: var(--space-10) 0;
                text-align: center;
                box-shadow: var(--shadow-lg);
                position: relative;
                overflow: hidden;
            }}
            
            .credentials-panel::before {{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
                opacity: 0.3;
            }}
            
            .credentials-panel h3 {{
                margin-bottom: var(--space-6);
                font-size: var(--text-xl);
                font-weight: var(--font-extrabold);
                position: relative;
                z-index: 1;
            }}
            
            .credential-item {{
                margin: var(--space-4) 0;
                font-size: var(--text-base);
                font-weight: var(--font-semibold);
                position: relative;
                z-index: 1;
            }}
            
            .system-features {{
                background: var(--color-gray-100);
                padding: var(--space-6);
                border-radius: var(--radius-xl);
                margin: var(--space-8) 0;
            }}
            
            .system-features h4 {{
                font-weight: var(--font-bold);
                margin-bottom: var(--space-4);
                color: var(--color-gray-800);
                font-size: var(--text-lg);
                text-align: center;
            }}
            
            .features-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: var(--space-3);
                font-size: var(--text-sm);
            }}
            
            .feature-item {{
                display: flex;
                align-items: center;
                gap: var(--space-2);
                color: var(--color-gray-700);
                font-weight: var(--font-medium);
            }}
            
            .feature-item::before {{
                content: '✅';
                font-size: var(--text-base);
            }}
            
            .quick-links {{
                background: rgba(102, 126, 234, 0.1);
                padding: var(--space-6);
                border-radius: var(--radius-xl);
                margin: var(--space-8) 0;
                text-align: center;
                border: 1px solid rgba(102, 126, 234, 0.2);
            }}
            
            .quick-links h4 {{
                margin-bottom: var(--space-5);
                color: var(--color-primary);
                font-weight: var(--font-extrabold);
                font-size: var(--text-lg);
            }}
            
            .quick-links a {{
                color: var(--color-primary);
                text-decoration: none;
                font-weight: var(--font-bold);
                margin: 0 var(--space-4);
                padding: var(--space-2) var(--space-4);
                border-radius: var(--radius-lg);
                transition: all 300ms var(--timing-smooth);
                display: inline-block;
                font-size: var(--text-sm);
            }}
            
            .quick-links a:hover {{
                background: var(--color-primary);
                color: var(--color-white);
                transform: translateY(-2px);
            }}

            /* Botones de Acceso Directo */
            .access-buttons {{
                margin: var(--space-8) 0;
                text-align: center;
            }}

            .btn-access {{
                display: flex;
                align-items: center;
                gap: var(--space-4);
                padding: var(--space-5) var(--space-6);
                border-radius: var(--radius-xl);
                text-decoration: none;
                color: var(--color-white);
                font-weight: var(--font-bold);
                transition: all 300ms var(--timing-smooth);
                position: relative;
                overflow: hidden;
                box-shadow: var(--shadow-lg);
            }}

            .btn-access::before {{
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 500ms;
            }}

            .btn-access:hover::before {{
                left: 100%;
            }}

            .btn-access:hover {{
                transform: translateY(-3px) scale(1.02);
                box-shadow: var(--shadow-2xl);
            }}

            .btn-access i {{
                font-size: var(--text-3xl);
                opacity: 0.9;
            }}

            .btn-access div {{
                text-align: left;
            }}

            .btn-access strong {{
                display: block;
                font-size: var(--text-lg);
                margin-bottom: var(--space-1);
            }}

            .btn-access small {{
                display: block;
                font-size: var(--text-sm);
                opacity: 0.8;
                font-weight: var(--font-medium);
            }}

            .frontend-btn {{
                background: linear-gradient(135deg, #4ade80, #22c55e);
            }}

            .backend-btn {{
                background: linear-gradient(135deg, #f59e0b, #d97706);
            }}

            .api-btn {{
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                width: 100%;
            }}
            
            .developer-section {{
                text-align: center;
                margin-top: var(--space-10);
                padding-top: var(--space-8);
                border-top: 2px solid var(--color-gray-300);
                color: var(--color-gray-600);
                font-size: var(--text-sm);
            }}
            
            .developer-section p {{
                margin: var(--space-2) 0;
                font-weight: var(--font-semibold);
            }}
            
            .dominican-flag {{
                font-size: var(--text-xl);
                margin: 0 var(--space-2);
                display: inline-block;
                animation: wave 4s ease-in-out infinite;
            }}
            
            @keyframes wave {{
                0%, 100% {{ transform: rotate(0deg) scale(1); }}
                25% {{ transform: rotate(-12deg) scale(1.1); }}
                75% {{ transform: rotate(12deg) scale(1.1); }}
            }}
            
            .loading-spinner {{
                display: none;
                width: 20px;
                height: 20px;
                border: 2px solid rgba(255,255,255,0.3);
                border-top: 2px solid white;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-left: var(--space-2);
            }}
            
            @keyframes spin {{
                0% {{ transform: rotate(0deg); }}
                100% {{ transform: rotate(360deg); }}
            }}
            
            @media (max-width: 768px) {{
                .login-container {{
                    padding: var(--space-12) var(--space-8);
                    margin: var(--space-4);
                }}
                
                .logo-section h1 {{
                    font-size: var(--text-4xl);
                }}
                
                .features-grid {{
                    grid-template-columns: 1fr;
                }}
                
                .quick-links a {{
                    display: block;
                    margin: var(--space-2) 0;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="floating-elements">
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
        </div>
        
        <div class="login-container">
            <div class="logo-section">
                <h1>🇩🇴 SoloYLibre</h1>
                <p>WordPress Ultimate System</p>
                <div class="version-info">v{system_info['version']} - Build {system_info['build']}</div>
            </div>
            
            {error_html}
            
            <form method="POST" action="/soloylibre-admin" id="loginForm" class="form-section">
                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        Usuario Administrador
                    </label>
                    <div class="input-wrapper">
                        <input type="text" id="username" name="log" value="josetusabe" required 
                               autocomplete="username" class="input" placeholder="Ingresa tu usuario">
                        <span class="input-icon"><i class="fas fa-user"></i></span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        Contraseña Maestra
                    </label>
                    <div class="input-wrapper">
                        <input type="password" id="password" name="pwd" value="JoseTusabe2025!" required 
                               autocomplete="current-password" class="input" placeholder="Ingresa tu contraseña">
                        <span class="input-icon"><i class="fas fa-lock"></i></span>
                    </div>
                </div>
                
                <button type="submit" class="btn-login" id="loginBtn">
                    <i class="fas fa-rocket"></i>
                    Acceder al Sistema Ultimate
                    <span class="loading-spinner" id="loadingSpinner"></span>
                </button>
            </form>
            
            <div class="credentials-panel">
                <h3><i class="fas fa-key"></i> Acceso Completo al Sistema</h3>
                <div class="credential-item"><strong>👤 Usuario:</strong> josetusabe</div>
                <div class="credential-item"><strong>🔑 Contraseña:</strong> JoseTusabe2025!</div>
                <div class="credential-item"><strong>📧 Email:</strong> {company_info['email']}</div>
                <div class="credential-item"><strong>🌟 Nivel:</strong> Super Administrador</div>
            </div>
            
            <div class="system-features">
                <h4><i class="fas fa-cogs"></i> Sistema WordPress Ultimate - Todos los Roles Implementados</h4>
                <div class="features-grid">
                    <div class="feature-item">Product Manager</div>
                    <div class="feature-item">UX/UI Designer</div>
                    <div class="feature-item">Frontend Developer</div>
                    <div class="feature-item">Backend Developer</div>
                    <div class="feature-item">DevOps Engineer</div>
                    <div class="feature-item">Security Expert</div>
                    <div class="feature-item">Data Analyst</div>
                    <div class="feature-item">AI Engineer</div>
                    <div class="feature-item">QA Specialist</div>
                    <div class="feature-item">Digital Marketer</div>
                    <div class="feature-item">Support Technician</div>
                    <div class="feature-item">Legal Advisor</div>
                    <div class="feature-item">Project Manager</div>
                </div>
            </div>
            
            <div class="access-buttons">
                <h4 style="margin-bottom: var(--space-5); color: var(--color-primary); font-weight: var(--font-extrabold);">
                    <i class="fas fa-rocket"></i> Acceso Directo al Sistema
                </h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-4); margin-bottom: var(--space-4);">
                    <a href="/" target="_blank" class="btn-access frontend-btn">
                        <i class="fas fa-globe"></i>
                        <div>
                            <strong>🏠 FRONTEND</strong>
                            <small>Sitio Web Público</small>
                        </div>
                    </a>
                    <a href="/backend" target="_blank" class="btn-access backend-btn">
                        <i class="fas fa-cogs"></i>
                        <div>
                            <strong>🔧 BACKEND</strong>
                            <small>Panel de Control</small>
                        </div>
                    </a>
                </div>
                <div style="margin-bottom: var(--space-4);">
                    <a href="/api/" target="_blank" class="btn-access api-btn">
                        <i class="fas fa-code"></i>
                        <div>
                            <strong>🔌 API REST</strong>
                            <small>Documentación y Endpoints</small>
                        </div>
                    </a>
                </div>
            </div>

            <div class="quick-links">
                <h4><i class="fas fa-external-link-alt"></i> Enlaces Adicionales</h4>
                <a href="/manifest.json" target="_blank"><i class="fas fa-mobile-alt"></i> PWA Manifest</a>
                <a href="/sw.js" target="_blank"><i class="fas fa-cog"></i> Service Worker</a>
                <a href="/debug_report.json" target="_blank"><i class="fas fa-bug"></i> Debug Report</a>
            </div>
            
            <div class="developer-section">
                <p><strong><i class="fas fa-code"></i> Desarrollado por:</strong> {company_info['developer']}</p>
                <p><span class="dominican-flag">🇩🇴</span> {company_info['location']}</p>
                <p><strong><i class="fas fa-envelope"></i> Email:</strong> {company_info['email']}</p>
                <p><strong><i class="fas fa-phone"></i> Teléfono:</strong> {company_info['phone']}</p>
                <p><strong><i class="fas fa-building"></i> Empresa:</strong> {company_info['name']}</p>
                <p><strong><i class="fas fa-server"></i> Servidor:</strong> {company_info['server']}</p>
                <p><strong><i class="fas fa-globe"></i> Sitios:</strong> 
                   {company_info['websites']['main']} | {company_info['websites']['personal']} | {company_info['websites']['photography']}
                </p>
            </div>
        </div>
        
        <script>
            // Registrar Service Worker para PWA
            if ('serviceWorker' in navigator) {{
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => console.log('✅ Service Worker registrado'))
                    .catch(error => console.log('❌ Error registrando Service Worker:', error));
            }}
            
            // Auto-focus en el primer input
            document.addEventListener('DOMContentLoaded', function() {{
                document.getElementById('username').focus();
                
                // Animación de entrada
                document.querySelector('.login-container').style.opacity = '0';
                setTimeout(() => {{
                    document.querySelector('.login-container').style.opacity = '1';
                }}, 100);
            }});
            
            // Manejar envío del formulario
            document.getElementById('loginForm').addEventListener('submit', function(e) {{
                const loginBtn = document.getElementById('loginBtn');
                const spinner = document.getElementById('loadingSpinner');
                
                loginBtn.disabled = true;
                spinner.style.display = 'inline-block';
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Iniciando sesión...';
                
                // Permitir que el formulario se envíe normalmente
                setTimeout(() => {{
                    if (!window.location.href.includes('dashboard')) {{
                        loginBtn.disabled = false;
                        spinner.style.display = 'none';
                        loginBtn.innerHTML = '<i class="fas fa-rocket"></i> Acceder al Sistema Ultimate';
                    }}
                }}, 5000);
            }});
            
            // Efectos de teclado
            document.addEventListener('keydown', function(e) {{
                if (e.key === 'Enter') {{
                    document.getElementById('loginForm').submit();
                }}
            }});
            
            // Animaciones adicionales
            const shapes = document.querySelectorAll('.floating-shape');
            shapes.forEach((shape, index) => {{
                shape.style.animationDelay = `${{index * 6}}s`;
            }});
        </script>
    </body>
    </html>
    """
