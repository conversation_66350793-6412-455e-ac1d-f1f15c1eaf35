#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - Admin Media Page
Página de administración de medios (equivalente a wp-admin/upload.php)
Desarrollado por <PERSON> (JoseTusabe)
Desde San José de <PERSON>, República Dominicana 🇩🇴
"""

def render_admin_media(server_instance, user_data):
    """Renderizar página de administración de medios"""

    design_css = server_instance.load_design_system_css()
    company_info = server_instance.core.get_company_info()

    return f"""
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>📷 Biblioteca de Medios - {company_info['name']}</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <style>
            {design_css}

            body {{
                font-family: var(--font-family);
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                margin: 0;
                padding: 0;
                color: var(--color-gray-900);
            }}

            .admin-container {{
                display: grid;
                grid-template-columns: 280px 1fr;
                min-height: 100vh;
            }}

            /* SIDEBAR */
            .sidebar {{
                background: linear-gradient(180deg, var(--color-primary) 0%, var(--color-secondary) 100%);
                color: var(--color-white);
                padding: var(--space-6);
                box-shadow: var(--shadow-xl);
                position: relative;
                overflow-y: auto;
            }}

            .logo-section {{
                text-align: center;
                margin-bottom: var(--space-8);
                padding-bottom: var(--space-6);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }}

            .logo-section h1 {{
                font-size: var(--text-2xl);
                font-weight: var(--font-black);
                margin-bottom: var(--space-2);
            }}

            .nav-menu {{
                list-style: none;
                margin: 0;
                padding: 0;
            }}

            .nav-item {{
                margin-bottom: var(--space-2);
            }}

            .nav-link {{
                display: flex;
                align-items: center;
                gap: var(--space-3);
                padding: var(--space-3) var(--space-4);
                color: var(--color-white);
                text-decoration: none;
                border-radius: var(--radius-lg);
                transition: all 300ms var(--timing-smooth);
                font-weight: var(--font-medium);
            }}

            .nav-link:hover, .nav-link.active {{
                background: rgba(255, 255, 255, 0.2);
                transform: translateX(5px);
            }}

            /* MAIN CONTENT */
            .main-content {{
                padding: var(--space-8);
                overflow-y: auto;
            }}

            .page-header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: var(--space-8);
                background: var(--color-white);
                padding: var(--space-6);
                border-radius: var(--radius-2xl);
                box-shadow: var(--shadow-lg);
            }}

            .page-title {{
                font-size: var(--text-3xl);
                font-weight: var(--font-black);
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }}

            .page-actions {{
                display: flex;
                gap: var(--space-4);
            }}

            .btn {{
                padding: var(--space-3) var(--space-6);
                border: none;
                border-radius: var(--radius-lg);
                font-weight: var(--font-semibold);
                cursor: pointer;
                transition: all 300ms var(--timing-smooth);
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: var(--space-2);
            }}

            .btn-primary {{
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                color: var(--color-white);
            }}

            .btn-primary:hover {{
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }}

            /* UPLOAD AREA */
            .upload-section {{
                background: var(--color-white);
                padding: var(--space-8);
                border-radius: var(--radius-2xl);
                box-shadow: var(--shadow-lg);
                margin-bottom: var(--space-8);
                text-align: center;
            }}

            .upload-area {{
                border: 3px dashed var(--color-gray-300);
                border-radius: var(--radius-xl);
                padding: var(--space-12);
                transition: all 300ms;
                cursor: pointer;
            }}

            .upload-area:hover {{
                border-color: var(--color-primary);
                background: var(--color-gray-50);
            }}

            .upload-area.dragover {{
                border-color: var(--color-primary);
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            }}

            .upload-icon {{
                font-size: var(--text-6xl);
                color: var(--color-gray-400);
                margin-bottom: var(--space-4);
            }}

            .upload-text {{
                font-size: var(--text-xl);
                font-weight: var(--font-semibold);
                color: var(--color-gray-700);
                margin-bottom: var(--space-2);
            }}

            .upload-subtext {{
                color: var(--color-gray-500);
                margin-bottom: var(--space-6);
            }}

            .file-input {{
                display: none;
            }}

            /* MEDIA GRID */
            .media-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: var(--space-6);
                margin-bottom: var(--space-8);
            }}

            .media-item {{
                background: var(--color-white);
                border-radius: var(--radius-xl);
                box-shadow: var(--shadow-md);
                overflow: hidden;
                transition: all 300ms;
                cursor: pointer;
            }}

            .media-item:hover {{
                transform: translateY(-5px);
                box-shadow: var(--shadow-xl);
            }}

            .media-preview {{
                width: 100%;
                height: 150px;
                background: var(--color-gray-100);
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                overflow: hidden;
            }}

            .media-preview img {{
                width: 100%;
                height: 100%;
                object-fit: cover;
            }}

            .media-preview .file-icon {{
                font-size: var(--text-4xl);
                color: var(--color-gray-400);
            }}

            .media-info {{
                padding: var(--space-4);
            }}

            .media-title {{
                font-weight: var(--font-semibold);
                color: var(--color-gray-900);
                margin-bottom: var(--space-2);
                font-size: var(--text-sm);
                line-height: 1.4;
            }}

            .media-meta {{
                font-size: var(--text-xs);
                color: var(--color-gray-500);
                display: flex;
                justify-content: space-between;
                align-items: center;
            }}

            .media-actions {{
                position: absolute;
                top: var(--space-2);
                right: var(--space-2);
                display: flex;
                gap: var(--space-2);
                opacity: 0;
                transition: opacity 300ms;
            }}

            .media-item:hover .media-actions {{
                opacity: 1;
            }}

            .action-btn {{
                width: 32px;
                height: 32px;
                border-radius: var(--radius-md);
                background: rgba(0, 0, 0, 0.7);
                color: var(--color-white);
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 300ms;
            }}

            .action-btn:hover {{
                background: var(--color-primary);
            }}

            /* FILTERS */
            .filters-section {{
                background: var(--color-white);
                padding: var(--space-6);
                border-radius: var(--radius-2xl);
                box-shadow: var(--shadow-lg);
                margin-bottom: var(--space-8);
            }}

            .filters-row {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: var(--space-4);
            }}

            .filter-group {{
                display: flex;
                gap: var(--space-4);
                align-items: center;
            }}

            .filter-select {{
                padding: var(--space-2) var(--space-4);
                border: 2px solid var(--color-gray-300);
                border-radius: var(--radius-lg);
                font-size: var(--text-sm);
                background: var(--color-white);
            }}

            .search-input {{
                padding: var(--space-3) var(--space-4);
                border: 2px solid var(--color-gray-300);
                border-radius: var(--radius-lg);
                font-size: var(--text-base);
                min-width: 250px;
            }}

            .search-input:focus {{
                outline: none;
                border-color: var(--color-primary);
            }}

            /* RESPONSIVE */
            @media (max-width: 1024px) {{
                .admin-container {{
                    grid-template-columns: 1fr;
                }}

                .sidebar {{
                    display: none;
                }}

                .media-grid {{
                    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                }}
            }}

            @media (max-width: 768px) {{
                .main-content {{
                    padding: var(--space-4);
                }}

                .page-header {{
                    flex-direction: column;
                    gap: var(--space-4);
                    text-align: center;
                }}

                .filters-row {{
                    flex-direction: column;
                    align-items: stretch;
                }}

                .search-input {{
                    min-width: auto;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="admin-container">
            <!-- SIDEBAR -->
            <aside class="sidebar">
                <div class="logo-section">
                    <h1>🇩🇴 SoloYLibre</h1>
                    <p>WordPress Ultimate</p>
                </div>

                <nav>
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="/dashboard" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/posts" class="nav-link">
                                <i class="fas fa-edit"></i>
                                Posts
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/pages" class="nav-link">
                                <i class="fas fa-file-alt"></i>
                                Páginas
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/media" class="nav-link active">
                                <i class="fas fa-photo-video"></i>
                                Medios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/comments" class="nav-link">
                                <i class="fas fa-comments"></i>
                                Comentarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/appearance" class="nav-link">
                                <i class="fas fa-paint-brush"></i>
                                Apariencia
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/users" class="nav-link">
                                <i class="fas fa-users"></i>
                                Usuarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/settings" class="nav-link">
                                <i class="fas fa-cog"></i>
                                Configuración
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/" class="nav-link">
                                <i class="fas fa-external-link-alt"></i>
                                Ver Sitio
                            </a>
                        </li>
                    </ul>
                </nav>
            </aside>

            <!-- MAIN CONTENT -->
            <main class="main-content">
                <!-- PAGE HEADER -->
                <header class="page-header">
                    <div>
                        <h1 class="page-title">📷 Biblioteca de Medios</h1>
                        <p>Gestiona todos los archivos multimedia de tu sitio web</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-upload"></i>
                            Subir Archivos
                        </button>
                    </div>
                </header>

                <!-- UPLOAD SECTION -->
                <section class="upload-section">
                    <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">Arrastra archivos aquí o haz clic para seleccionar</div>
                        <div class="upload-subtext">
                            Formatos soportados: JPG, PNG, GIF, PDF, MP4, MP3, DOC, ZIP<br>
                            Tamaño máximo: 10MB por archivo
                        </div>
                        <input type="file" id="fileInput" class="file-input" multiple accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.zip">
                    </div>
                </section>

                <!-- FILTERS -->
                <section class="filters-section">
                    <div class="filters-row">
                        <div class="filter-group">
                            <select class="filter-select" id="typeFilter">
                                <option value="all">Todos los tipos</option>
                                <option value="image">Imágenes</option>
                                <option value="video">Videos</option>
                                <option value="audio">Audio</option>
                                <option value="document">Documentos</option>
                            </select>

                            <select class="filter-select" id="dateFilter">
                                <option value="all">Todas las fechas</option>
                                <option value="today">Hoy</option>
                                <option value="week">Esta semana</option>
                                <option value="month">Este mes</option>
                                <option value="year">Este año</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <input type="text" class="search-input" placeholder="Buscar archivos..." id="searchInput">
                            <button class="btn btn-primary" onclick="searchMedia()">
                                <i class="fas fa-search"></i>
                                Buscar
                            </button>
                        </div>
                    </div>
                </section>

                <!-- MEDIA GRID -->
                <section class="media-grid" id="mediaGrid">
                    <!-- Ejemplo de archivos multimedia -->
                    <div class="media-item">
                        <div class="media-preview">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04NSA2MEMxMDEuNTY5IDYwIDExNSA3My40MzE1IDExNSA5MEM5OC40MzE1IDkwIDg1IDc2LjU2ODUgODUgNjBaIiBmaWxsPSIjRDFENUQ5Ii8+CjxwYXRoIGQ9Ik0xNDAgNzBMMTIwIDkwTDE0MCA5MEwxNjAgNzBIMTQwWiIgZmlsbD0iI0QxRDVEOSIvPgo8L3N2Zz4K" alt="Imagen de ejemplo">
                            <div class="media-actions">
                                <button class="action-btn" title="Ver">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn" title="Editar">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="Eliminar">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="media-title">imagen-ejemplo.jpg</div>
                            <div class="media-meta">
                                <span>JPG</span>
                                <span>2.3 MB</span>
                            </div>
                        </div>
                    </div>

                    <div class="media-item">
                        <div class="media-preview">
                            <div class="file-icon">
                                <i class="fas fa-file-pdf" style="color: #ef4444;"></i>
                            </div>
                            <div class="media-actions">
                                <button class="action-btn" title="Descargar">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" title="Eliminar">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="media-title">documento-importante.pdf</div>
                            <div class="media-meta">
                                <span>PDF</span>
                                <span>1.8 MB</span>
                            </div>
                        </div>
                    </div>

                    <div class="media-item">
                        <div class="media-preview">
                            <div class="file-icon">
                                <i class="fas fa-file-video" style="color: #8b5cf6;"></i>
                            </div>
                            <div class="media-actions">
                                <button class="action-btn" title="Reproducir">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="action-btn" title="Editar">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="Eliminar">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="media-title">video-promocional.mp4</div>
                            <div class="media-meta">
                                <span>MP4</span>
                                <span>15.2 MB</span>
                            </div>
                        </div>
                    </div>

                    <div class="media-item">
                        <div class="media-preview">
                            <div class="file-icon">
                                <i class="fas fa-file-audio" style="color: #10b981;"></i>
                            </div>
                            <div class="media-actions">
                                <button class="action-btn" title="Reproducir">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="action-btn" title="Eliminar">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="media-title">audio-podcast.mp3</div>
                            <div class="media-meta">
                                <span>MP3</span>
                                <span>8.7 MB</span>
                            </div>
                        </div>
                    </div>

                    <div class="media-item">
                        <div class="media-preview">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRkVGM0M3Ii8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9Ijc1IiByPSIzMCIgZmlsbD0iI0Y1OTUwNSIvPgo8L3N2Zz4K" alt="Logo SoloYLibre">
                            <div class="media-actions">
                                <button class="action-btn" title="Ver">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn" title="Editar">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="Eliminar">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="media-title">logo-soloylibre.png</div>
                            <div class="media-meta">
                                <span>PNG</span>
                                <span>456 KB</span>
                            </div>
                        </div>
                    </div>

                    <div class="media-item">
                        <div class="media-preview">
                            <div class="file-icon">
                                <i class="fas fa-file-archive" style="color: #6366f1;"></i>
                            </div>
                            <div class="media-actions">
                                <button class="action-btn" title="Descargar">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" title="Eliminar">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="media-title">backup-sitio.zip</div>
                            <div class="media-meta">
                                <span>ZIP</span>
                                <span>25.4 MB</span>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>

        <script>
            // Drag and Drop functionality
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {{
                uploadArea.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            }});

            ['dragenter', 'dragover'].forEach(eventName => {{
                uploadArea.addEventListener(eventName, highlight, false);
            }});

            ['dragleave', 'drop'].forEach(eventName => {{
                uploadArea.addEventListener(eventName, unhighlight, false);
            }});

            uploadArea.addEventListener('drop', handleDrop, false);

            function preventDefaults(e) {{
                e.preventDefault();
                e.stopPropagation();
            }}

            function highlight(e) {{
                uploadArea.classList.add('dragover');
            }}

            function unhighlight(e) {{
                uploadArea.classList.remove('dragover');
            }}

            function handleDrop(e) {{
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFiles(files);
            }}

            fileInput.addEventListener('change', function(e) {{
                handleFiles(this.files);
            }});

            function handleFiles(files) {{
                ([...files]).forEach(uploadFile);
            }}

            function uploadFile(file) {{
                console.log('Uploading file:', file.name);
                showNotification(`Subiendo archivo: ${{file.name}}`, 'info');

                // Simular upload
                setTimeout(() => {{
                    showNotification(`Archivo subido exitosamente: ${{file.name}}`, 'success');
                    // Aquí se agregaría el archivo al grid
                    addFileToGrid(file);
                }}, 2000);
            }}

            function addFileToGrid(file) {{
                const mediaGrid = document.getElementById('mediaGrid');
                const fileType = getFileType(file.type);
                const fileSize = formatFileSize(file.size);

                const mediaItem = document.createElement('div');
                mediaItem.className = 'media-item';

                let preview = '';
                if (file.type.startsWith('image/')) {{
                    const reader = new FileReader();
                    reader.onload = function(e) {{
                        mediaItem.querySelector('.media-preview').innerHTML = `
                            <img src="${{e.target.result}}" alt="${{file.name}}">
                            <div class="media-actions">
                                <button class="action-btn" title="Ver">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn" title="Editar">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="Eliminar">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        `;
                    }};
                    reader.readAsDataURL(file);
                    preview = '<div class="file-icon"><i class="fas fa-image"></i></div>';
                }} else {{
                    const icon = getFileIcon(file.type);
                    preview = `<div class="file-icon">${{icon}}</div>`;
                }}

                mediaItem.innerHTML = `
                    <div class="media-preview">
                        ${{preview}}
                        <div class="media-actions">
                            <button class="action-btn" title="Descargar">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="action-btn" title="Eliminar">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="media-info">
                        <div class="media-title">${{file.name}}</div>
                        <div class="media-meta">
                            <span>${{fileType}}</span>
                            <span>${{fileSize}}</span>
                        </div>
                    </div>
                `;

                mediaGrid.insertBefore(mediaItem, mediaGrid.firstChild);
            }}

            function getFileType(mimeType) {{
                if (mimeType.startsWith('image/')) return 'IMG';
                if (mimeType.startsWith('video/')) return 'VID';
                if (mimeType.startsWith('audio/')) return 'AUD';
                if (mimeType.includes('pdf')) return 'PDF';
                if (mimeType.includes('zip')) return 'ZIP';
                if (mimeType.includes('document')) return 'DOC';
                return 'FILE';
            }}

            function getFileIcon(mimeType) {{
                if (mimeType.startsWith('image/')) return '<i class="fas fa-image" style="color: #10b981;"></i>';
                if (mimeType.startsWith('video/')) return '<i class="fas fa-file-video" style="color: #8b5cf6;"></i>';
                if (mimeType.startsWith('audio/')) return '<i class="fas fa-file-audio" style="color: #10b981;"></i>';
                if (mimeType.includes('pdf')) return '<i class="fas fa-file-pdf" style="color: #ef4444;"></i>';
                if (mimeType.includes('zip')) return '<i class="fas fa-file-archive" style="color: #6366f1;"></i>';
                if (mimeType.includes('document')) return '<i class="fas fa-file-word" style="color: #2563eb;"></i>';
                return '<i class="fas fa-file" style="color: #6b7280;"></i>';
            }}

            function formatFileSize(bytes) {{
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }}

            // Search functionality
            function searchMedia() {{
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const mediaItems = document.querySelectorAll('.media-item');

                mediaItems.forEach(item => {{
                    const title = item.querySelector('.media-title').textContent.toLowerCase();
                    if (title.includes(searchTerm)) {{
                        item.style.display = 'block';
                    }} else {{
                        item.style.display = 'none';
                    }}
                }});

                showNotification(`Búsqueda realizada: "${{searchTerm}}"`, 'info');
            }}

            // Filter functionality
            document.getElementById('typeFilter').addEventListener('change', function() {{
                const filterType = this.value;
                const mediaItems = document.querySelectorAll('.media-item');

                mediaItems.forEach(item => {{
                    const fileType = item.querySelector('.media-meta span').textContent;

                    if (filterType === 'all') {{
                        item.style.display = 'block';
                    }} else if (filterType === 'image' && ['JPG', 'PNG', 'GIF', 'IMG'].includes(fileType)) {{
                        item.style.display = 'block';
                    }} else if (filterType === 'video' && ['MP4', 'AVI', 'VID'].includes(fileType)) {{
                        item.style.display = 'block';
                    }} else if (filterType === 'audio' && ['MP3', 'WAV', 'AUD'].includes(fileType)) {{
                        item.style.display = 'block';
                    }} else if (filterType === 'document' && ['PDF', 'DOC', 'ZIP'].includes(fileType)) {{
                        item.style.display = 'block';
                    }} else {{
                        item.style.display = 'none';
                    }}
                }});
            }});

            // Notificaciones
            function showNotification(message, type = 'info') {{
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${{type === 'success' ? '#22c55e' : type === 'error' ? '#ef4444' : '#3b82f6'}};
                    color: white;
                    padding: 16px 24px;
                    border-radius: 12px;
                    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                    z-index: 9999;
                    font-weight: 600;
                    max-width: 300px;
                    animation: slideIn 0.3s ease-out;
                `;
                notification.textContent = message;

                document.body.appendChild(notification);

                setTimeout(() => {{
                    notification.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => notification.remove(), 300);
                }}, 3000);
            }}

            // Animaciones CSS
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {{
                    from {{ transform: translateX(100%); opacity: 0; }}
                    to {{ transform: translateX(0); opacity: 1; }}
                }}
                @keyframes slideOut {{
                    from {{ transform: translateX(0); opacity: 1; }}
                    to {{ transform: translateX(100%); opacity: 0; }}
                }}
            `;
            document.head.appendChild(style);

            // Inicialización
            document.addEventListener('DOMContentLoaded', function() {{
                showNotification('📷 Biblioteca de Medios cargada exitosamente', 'success');
            }});
        </script>
    </body>
    </html>
    """