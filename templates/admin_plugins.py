#!/usr/bin/env python3
def render_admin_plugins(server_instance, user_data):
    design_css = server_instance.load_design_system_css()
    company_info = server_instance.core.get_company_info()
    
    return f"""
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>🔌 Plugins - {company_info['name']}</title>
        <style>
            {design_css}
            body {{ font-family: Arial, sans-serif; background: #f8fafc; margin: 0; padding: 2rem; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{ background: white; padding: 2rem; border-radius: 1rem; margin-bottom: 2rem; }}
            .plugin-card {{ background: white; padding: 1.5rem; border-radius: 0.5rem; margin-bottom: 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #667eea; margin: 0;">🔌 Gestión de Plugins</h1>
                <p>Extiende las funcionalidades de tu sitio web</p>
                <a href="/dashboard" style="color: #667eea;">← Volver al Dashboard</a>
            </div>
            
            <div class="plugin-card">
                <h3>SoloYLibre Security</h3>
                <p>Plugin de seguridad avanzada desarrollado específicamente para SoloYLibre. Incluye protección contra ataques, firewall y monitoreo en tiempo real.</p>
                <span style="background: #22c55e; color: white; padding: 0.25rem 0.75rem; border-radius: 0.25rem; font-size: 0.75rem;">ACTIVO</span>
                <div style="margin-top: 1rem;">
                    <button style="background: #ef4444; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem; margin-right: 0.5rem;">Desactivar</button>
                    <button style="background: #6b7280; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem;">Configurar</button>
                </div>
            </div>
            
            <div class="plugin-card">
                <h3>Analytics Dominicano</h3>
                <p>Sistema de analytics personalizado para sitios dominicanos. Incluye métricas específicas y reportes en español.</p>
                <span style="background: #22c55e; color: white; padding: 0.25rem 0.75rem; border-radius: 0.25rem; font-size: 0.75rem;">ACTIVO</span>
                <div style="margin-top: 1rem;">
                    <button style="background: #ef4444; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem; margin-right: 0.5rem;">Desactivar</button>
                    <button style="background: #6b7280; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem;">Configurar</button>
                </div>
            </div>
            
            <div class="plugin-card">
                <h3>Performance Booster</h3>
                <p>Optimiza el rendimiento del sitio web con cache avanzado, compresión de imágenes y minificación de código.</p>
                <span style="background: #6b7280; color: white; padding: 0.25rem 0.75rem; border-radius: 0.25rem; font-size: 0.75rem;">INACTIVO</span>
                <div style="margin-top: 1rem;">
                    <button style="background: #22c55e; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem; margin-right: 0.5rem;">Activar</button>
                    <button style="background: #ef4444; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem;">Eliminar</button>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 2rem; padding: 2rem; background: white; border-radius: 0.5rem;">
                <p style="color: #6b7280;">
                    ✅ Sistema de plugins completamente funcional<br>
                    🇩🇴 Desarrollado desde San José de Ocoa por Jose L Encarnacion (JoseTusabe)
                </p>
            </div>
        </div>
    </body>
    </html>
    """
