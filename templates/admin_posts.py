#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - Admin Posts Page
Página de administración de posts (equivalente a wp-admin/edit.php)
Desarrollado por <PERSON> (JoseTusabe)
Desde San José de <PERSON>, República Dominicana 🇩🇴
"""

def render_admin_posts(server_instance, user_data, posts_data, filters=None):
    """Renderizar página de administración de posts"""
    
    design_css = server_instance.load_design_system_css()
    company_info = server_instance.core.get_company_info()
    
    # Datos de filtros
    filters = filters or {}
    current_status = filters.get('status', 'all')
    current_search = filters.get('search', '')
    post_counts = filters.get('counts', {})
    
    return f"""
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Posts - {company_info['name']}</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <style>
            {design_css}
            
            body {{
                font-family: var(--font-family);
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                margin: 0;
                padding: 0;
                color: var(--color-gray-900);
            }}
            
            .admin-container {{
                display: grid;
                grid-template-columns: 280px 1fr;
                min-height: 100vh;
            }}
            
            /* SIDEBAR */
            .sidebar {{
                background: linear-gradient(180deg, var(--color-primary) 0%, var(--color-secondary) 100%);
                color: var(--color-white);
                padding: var(--space-6);
                box-shadow: var(--shadow-xl);
                position: relative;
                overflow-y: auto;
            }}
            
            .logo-section {{
                text-align: center;
                margin-bottom: var(--space-8);
                padding-bottom: var(--space-6);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }}
            
            .logo-section h1 {{
                font-size: var(--text-2xl);
                font-weight: var(--font-black);
                margin-bottom: var(--space-2);
            }}
            
            .nav-menu {{
                list-style: none;
                margin: 0;
                padding: 0;
            }}
            
            .nav-item {{
                margin-bottom: var(--space-2);
            }}
            
            .nav-link {{
                display: flex;
                align-items: center;
                gap: var(--space-3);
                padding: var(--space-3) var(--space-4);
                color: var(--color-white);
                text-decoration: none;
                border-radius: var(--radius-lg);
                transition: all 300ms var(--timing-smooth);
                font-weight: var(--font-medium);
            }}
            
            .nav-link:hover, .nav-link.active {{
                background: rgba(255, 255, 255, 0.2);
                transform: translateX(5px);
            }}
            
            /* MAIN CONTENT */
            .main-content {{
                padding: var(--space-8);
                overflow-y: auto;
            }}
            
            .page-header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: var(--space-8);
                background: var(--color-white);
                padding: var(--space-6);
                border-radius: var(--radius-2xl);
                box-shadow: var(--shadow-lg);
            }}
            
            .page-title {{
                font-size: var(--text-3xl);
                font-weight: var(--font-black);
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }}
            
            .page-actions {{
                display: flex;
                gap: var(--space-4);
            }}
            
            .btn {{
                padding: var(--space-3) var(--space-6);
                border: none;
                border-radius: var(--radius-lg);
                font-weight: var(--font-semibold);
                cursor: pointer;
                transition: all 300ms var(--timing-smooth);
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: var(--space-2);
            }}
            
            .btn-primary {{
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                color: var(--color-white);
            }}
            
            .btn-primary:hover {{
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }}
            
            /* FILTERS */
            .filters-section {{
                background: var(--color-white);
                padding: var(--space-6);
                border-radius: var(--radius-2xl);
                box-shadow: var(--shadow-lg);
                margin-bottom: var(--space-8);
            }}
            
            .filters-row {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: var(--space-4);
            }}
            
            .status-filters {{
                display: flex;
                gap: var(--space-4);
                flex-wrap: wrap;
            }}
            
            .status-filter {{
                color: var(--color-gray-600);
                text-decoration: none;
                font-weight: var(--font-semibold);
                padding: var(--space-2) var(--space-4);
                border-radius: var(--radius-lg);
                transition: all 300ms;
            }}
            
            .status-filter.active {{
                background: var(--color-primary);
                color: var(--color-white);
            }}
            
            .status-filter:hover {{
                background: var(--color-gray-100);
            }}
            
            .search-box {{
                display: flex;
                gap: var(--space-3);
            }}
            
            .search-input {{
                padding: var(--space-3) var(--space-4);
                border: 2px solid var(--color-gray-300);
                border-radius: var(--radius-lg);
                font-size: var(--text-base);
                min-width: 250px;
            }}
            
            .search-input:focus {{
                outline: none;
                border-color: var(--color-primary);
            }}
            
            /* BULK ACTIONS */
            .bulk-actions {{
                background: var(--color-white);
                padding: var(--space-4) var(--space-6);
                border-radius: var(--radius-xl);
                box-shadow: var(--shadow-md);
                margin-bottom: var(--space-6);
                display: flex;
                align-items: center;
                gap: var(--space-4);
            }}
            
            .bulk-select {{
                padding: var(--space-2) var(--space-4);
                border: 2px solid var(--color-gray-300);
                border-radius: var(--radius-lg);
                font-size: var(--text-sm);
            }}
            
            /* POSTS TABLE */
            .posts-table {{
                background: var(--color-white);
                border-radius: var(--radius-2xl);
                box-shadow: var(--shadow-lg);
                overflow: hidden;
            }}
            
            .table {{
                width: 100%;
                border-collapse: collapse;
            }}
            
            .table th {{
                background: var(--color-gray-50);
                padding: var(--space-4) var(--space-6);
                text-align: left;
                font-weight: var(--font-bold);
                color: var(--color-gray-900);
                border-bottom: 2px solid var(--color-gray-200);
            }}
            
            .table td {{
                padding: var(--space-4) var(--space-6);
                border-bottom: 1px solid var(--color-gray-200);
                vertical-align: top;
            }}
            
            .table tr:hover {{
                background: var(--color-gray-50);
            }}
            
            .post-title {{
                font-weight: var(--font-bold);
                color: var(--color-gray-900);
                margin-bottom: var(--space-2);
            }}
            
            .post-title a {{
                color: var(--color-primary);
                text-decoration: none;
            }}
            
            .post-title a:hover {{
                text-decoration: underline;
            }}
            
            .post-actions {{
                display: flex;
                gap: var(--space-3);
                font-size: var(--text-sm);
            }}
            
            .post-actions a {{
                color: var(--color-gray-600);
                text-decoration: none;
            }}
            
            .post-actions a:hover {{
                color: var(--color-primary);
            }}
            
            .post-status {{
                padding: var(--space-1) var(--space-3);
                border-radius: var(--radius-md);
                font-size: var(--text-xs);
                font-weight: var(--font-bold);
                text-transform: uppercase;
            }}
            
            .status-publish {{
                background: var(--color-success);
                color: var(--color-white);
            }}
            
            .status-draft {{
                background: var(--color-warning);
                color: var(--color-white);
            }}
            
            .status-pending {{
                background: var(--color-info);
                color: var(--color-white);
            }}
            
            .status-private {{
                background: var(--color-secondary);
                color: var(--color-white);
            }}
            
            .status-trash {{
                background: var(--color-error);
                color: var(--color-white);
            }}
            
            .post-meta {{
                font-size: var(--text-sm);
                color: var(--color-gray-600);
                margin-top: var(--space-2);
            }}
            
            .post-categories {{
                display: flex;
                gap: var(--space-2);
                flex-wrap: wrap;
                margin-top: var(--space-2);
            }}
            
            .category-tag {{
                background: var(--color-gray-100);
                color: var(--color-gray-700);
                padding: var(--space-1) var(--space-2);
                border-radius: var(--radius-md);
                font-size: var(--text-xs);
                text-decoration: none;
            }}
            
            .category-tag:hover {{
                background: var(--color-primary);
                color: var(--color-white);
            }}
            
            /* PAGINATION */
            .pagination {{
                display: flex;
                justify-content: center;
                align-items: center;
                gap: var(--space-4);
                margin-top: var(--space-8);
                padding: var(--space-6);
                background: var(--color-white);
                border-radius: var(--radius-xl);
                box-shadow: var(--shadow-md);
            }}
            
            .pagination a {{
                padding: var(--space-2) var(--space-4);
                border: 2px solid var(--color-gray-300);
                border-radius: var(--radius-lg);
                color: var(--color-gray-700);
                text-decoration: none;
                transition: all 300ms;
            }}
            
            .pagination a:hover {{
                background: var(--color-primary);
                color: var(--color-white);
                border-color: var(--color-primary);
            }}
            
            .pagination .current {{
                background: var(--color-primary);
                color: var(--color-white);
                border-color: var(--color-primary);
            }}
            
            /* RESPONSIVE */
            @media (max-width: 1024px) {{
                .admin-container {{
                    grid-template-columns: 1fr;
                }}
                
                .sidebar {{
                    display: none;
                }}
                
                .filters-row {{
                    flex-direction: column;
                    align-items: stretch;
                }}
                
                .search-input {{
                    min-width: auto;
                }}
            }}
            
            @media (max-width: 768px) {{
                .main-content {{
                    padding: var(--space-4);
                }}
                
                .page-header {{
                    flex-direction: column;
                    gap: var(--space-4);
                    text-align: center;
                }}
                
                .table {{
                    font-size: var(--text-sm);
                }}
                
                .table th, .table td {{
                    padding: var(--space-3);
                }}
            }}
        </style>
    </head>
    <body>
        <div class="admin-container">
            <!-- SIDEBAR -->
            <aside class="sidebar">
                <div class="logo-section">
                    <h1>🇩🇴 SoloYLibre</h1>
                    <p>WordPress Ultimate</p>
                </div>
                
                <nav>
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="/dashboard" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/posts" class="nav-link active">
                                <i class="fas fa-edit"></i>
                                Posts
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/pages" class="nav-link">
                                <i class="fas fa-file-alt"></i>
                                Páginas
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/media" class="nav-link">
                                <i class="fas fa-photo-video"></i>
                                Medios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/comments" class="nav-link">
                                <i class="fas fa-comments"></i>
                                Comentarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/appearance" class="nav-link">
                                <i class="fas fa-paint-brush"></i>
                                Apariencia
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/users" class="nav-link">
                                <i class="fas fa-users"></i>
                                Usuarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/soloylibre-admin/settings" class="nav-link">
                                <i class="fas fa-cog"></i>
                                Configuración
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/" class="nav-link">
                                <i class="fas fa-external-link-alt"></i>
                                Ver Sitio
                            </a>
                        </li>
                    </ul>
                </nav>
            </aside>
            
            <!-- MAIN CONTENT -->
            <main class="main-content">
                <!-- PAGE HEADER -->
                <header class="page-header">
                    <div>
                        <h1 class="page-title">Posts</h1>
                        <p>Gestiona todos los posts de tu sitio web</p>
                    </div>
                    <div class="page-actions">
                        <a href="/soloylibre-admin/post-new.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Añadir Nuevo Post
                        </a>
                    </div>
                </header>
                
                <!-- FILTERS -->
                <section class="filters-section">
                    <div class="filters-row">
                        <div class="status-filters">
                            <a href="/soloylibre-admin/posts?status=all" class="status-filter {'active' if current_status == 'all' else ''}">
                                Todos ({post_counts.get('all', 0)})
                            </a>
                            <a href="/soloylibre-admin/posts?status=publish" class="status-filter {'active' if current_status == 'publish' else ''}">
                                Publicados ({post_counts.get('publish', 0)})
                            </a>
                            <a href="/soloylibre-admin/posts?status=draft" class="status-filter {'active' if current_status == 'draft' else ''}">
                                Borradores ({post_counts.get('draft', 0)})
                            </a>
                            <a href="/soloylibre-admin/posts?status=pending" class="status-filter {'active' if current_status == 'pending' else ''}">
                                Pendientes ({post_counts.get('pending', 0)})
                            </a>
                            <a href="/soloylibre-admin/posts?status=trash" class="status-filter {'active' if current_status == 'trash' else ''}">
                                Papelera ({post_counts.get('trash', 0)})
                            </a>
                        </div>
                        
                        <div class="search-box">
                            <input type="text" class="search-input" placeholder="Buscar posts..." value="{current_search}" id="searchInput">
                            <button class="btn btn-primary" onclick="searchPosts()">
                                <i class="fas fa-search"></i>
                                Buscar
                            </button>
                        </div>
                    </div>
                </section>

                <!-- BULK ACTIONS -->
                <div class="bulk-actions">
                    <input type="checkbox" id="selectAll" onchange="toggleAllPosts()">
                    <label for="selectAll">Seleccionar todo</label>

                    <select class="bulk-select" id="bulkAction">
                        <option value="">Acciones en lote</option>
                        <option value="trash">Mover a papelera</option>
                        <option value="delete">Eliminar permanentemente</option>
                        <option value="draft">Cambiar a borrador</option>
                        <option value="publish">Publicar</option>
                    </select>

                    <button class="btn btn-primary" onclick="executeBulkAction()">
                        <i class="fas fa-play"></i>
                        Aplicar
                    </button>
                </div>

                <!-- POSTS TABLE -->
                <div class="posts-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAllHeader" onchange="toggleAllPosts()">
                                </th>
                                <th>Título</th>
                                <th width="120">Autor</th>
                                <th width="150">Categorías</th>
                                <th width="120">Fecha</th>
                                <th width="80">Estado</th>
                                <th width="80">Comentarios</th>
                            </tr>
                        </thead>
                        <tbody>
                            {"".join([f'''
                            <tr>
                                <td>
                                    <input type="checkbox" class="post-checkbox" value="{post['ID']}">
                                </td>
                                <td>
                                    <div class="post-title">
                                        <a href="{post['edit_url']}">{post['post_title'] or '(Sin título)'}</a>
                                    </div>
                                    <div class="post-actions">
                                        <a href="{post['edit_url']}">Editar</a>
                                        <a href="{post['view_url']}" target="_blank">Ver</a>
                                        <a href="#" onclick="duplicatePost({post['ID']})">Duplicar</a>
                                        <a href="#" onclick="trashPost({post['ID']})" style="color: var(--color-error);">Papelera</a>
                                    </div>
                                    <div class="post-meta">
                                        {post['word_count']} palabras • {post.get('reading_time', 0)} min lectura
                                    </div>
                                </td>
                                <td>
                                    <strong>{post['author_name'] or 'Desconocido'}</strong>
                                    <div style="font-size: var(--text-sm); color: var(--color-gray-600);">
                                        {post['author_email'] or ''}
                                    </div>
                                </td>
                                <td>
                                    <div class="post-categories">
                                        {"".join([f'<a href="/category/{cat["slug"]}" class="category-tag">{cat["name"]}</a>' for cat in post.get('categories', [])])}
                                        {'' if post.get('categories') else '<span style="color: var(--color-gray-500);">Sin categoría</span>'}
                                    </div>
                                </td>
                                <td>
                                    <div>{post['formatted_date']}</div>
                                    <div style="font-size: var(--text-sm); color: var(--color-gray-600);">
                                        {post['post_date'].split(' ')[1] if post['post_date'] else ''}
                                    </div>
                                </td>
                                <td>
                                    <span class="post-status status-{post['post_status']}">
                                        {post['post_status'].title()}
                                    </span>
                                </td>
                                <td style="text-align: center;">
                                    <a href="/soloylibre-admin/comments?post_id={post['ID']}" style="color: var(--color-primary); font-weight: var(--font-bold);">
                                        {post.get('comment_count', 0)}
                                    </a>
                                </td>
                            </tr>
                            ''' for post in posts_data])}

                            {'''
                            <tr>
                                <td colspan="7" style="text-align: center; padding: var(--space-12); color: var(--color-gray-500);">
                                    <i class="fas fa-file-alt" style="font-size: var(--text-4xl); margin-bottom: var(--space-4); opacity: 0.5;"></i>
                                    <div style="font-size: var(--text-lg); font-weight: var(--font-semibold);">No hay posts aún</div>
                                    <div style="margin-top: var(--space-2);">
                                        <a href="/soloylibre-admin/post-new.php" class="btn btn-primary" style="margin-top: var(--space-4);">
                                            <i class="fas fa-plus"></i>
                                            Crear tu primer post
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            ''' if not posts_data else ''}
                        </tbody>
                    </table>
                </div>

                <!-- PAGINATION -->
                <div class="pagination">
                    <a href="#" onclick="changePage(1)">
                        <i class="fas fa-angle-double-left"></i>
                        Primera
                    </a>
                    <a href="#" onclick="changePage('prev')">
                        <i class="fas fa-angle-left"></i>
                        Anterior
                    </a>
                    <span class="current">1</span>
                    <a href="#" onclick="changePage('next')">
                        Siguiente
                        <i class="fas fa-angle-right"></i>
                    </a>
                    <a href="#" onclick="changePage('last')">
                        Última
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </div>
            </main>
        </div>

        <script>
            // Selección de posts
            function toggleAllPosts() {{
                const selectAll = document.getElementById('selectAll');
                const checkboxes = document.querySelectorAll('.post-checkbox');

                checkboxes.forEach(checkbox => {{
                    checkbox.checked = selectAll.checked;
                }});

                // Sincronizar checkbox del header
                document.getElementById('selectAllHeader').checked = selectAll.checked;
            }}

            // Ejecutar acción en lote
            function executeBulkAction() {{
                const action = document.getElementById('bulkAction').value;
                const selectedPosts = Array.from(document.querySelectorAll('.post-checkbox:checked'))
                    .map(cb => cb.value);

                if (!action) {{
                    alert('Por favor selecciona una acción');
                    return;
                }}

                if (selectedPosts.length === 0) {{
                    alert('Por favor selecciona al menos un post');
                    return;
                }}

                if (action === 'delete') {{
                    if (!confirm(`¿Estás seguro de que quieres eliminar permanentemente ${{selectedPosts.length}} post(s)?`)) {{
                        return;
                    }}
                }}

                // Simular acción (en implementación real sería AJAX)
                showNotification(`Acción "${{action}}" aplicada a ${{selectedPosts.length}} post(s)`, 'success');

                // Recargar página después de 1 segundo
                setTimeout(() => {{
                    window.location.reload();
                }}, 1000);
            }}

            // Acciones individuales
            function trashPost(postId) {{
                if (confirm('¿Mover este post a la papelera?')) {{
                    showNotification('Post movido a la papelera', 'success');
                    // En implementación real: AJAX call
                    setTimeout(() => window.location.reload(), 1000);
                }}
            }}

            function duplicatePost(postId) {{
                showNotification('Post duplicado exitosamente', 'success');
                // En implementación real: AJAX call
                setTimeout(() => window.location.reload(), 1000);
            }}

            // Búsqueda
            function searchPosts() {{
                const searchTerm = document.getElementById('searchInput').value;
                const currentUrl = new URL(window.location);

                if (searchTerm) {{
                    currentUrl.searchParams.set('search', searchTerm);
                }} else {{
                    currentUrl.searchParams.delete('search');
                }}

                window.location.href = currentUrl.toString();
            }}

            // Enter en búsqueda
            document.getElementById('searchInput').addEventListener('keypress', function(e) {{
                if (e.key === 'Enter') {{
                    searchPosts();
                }}
            }});

            // Paginación
            function changePage(page) {{
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('page', page);
                window.location.href = currentUrl.toString();
            }}

            // Notificaciones
            function showNotification(message, type = 'info') {{
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${{type === 'success' ? '#22c55e' : type === 'error' ? '#ef4444' : '#3b82f6'}};
                    color: white;
                    padding: 16px 24px;
                    border-radius: 12px;
                    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                    z-index: 9999;
                    font-weight: 600;
                    max-width: 300px;
                    animation: slideIn 0.3s ease-out;
                `;
                notification.textContent = message;

                document.body.appendChild(notification);

                setTimeout(() => {{
                    notification.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => notification.remove(), 300);
                }}, 3000);
            }}

            // Animaciones CSS
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {{
                    from {{ transform: translateX(100%); opacity: 0; }}
                    to {{ transform: translateX(0); opacity: 1; }}
                }}
                @keyframes slideOut {{
                    from {{ transform: translateX(0); opacity: 1; }}
                    to {{ transform: translateX(100%); opacity: 0; }}
                }}
            `;
            document.head.appendChild(style);

            // Inicialización
            document.addEventListener('DOMContentLoaded', function() {{
                // Mostrar notificación de bienvenida
                setTimeout(() => {{
                    showNotification('📝 Página de Posts cargada exitosamente', 'success');
                }}, 500);

                // Sincronizar checkboxes
                const selectAll = document.getElementById('selectAll');
                const selectAllHeader = document.getElementById('selectAllHeader');

                selectAllHeader.addEventListener('change', function() {{
                    selectAll.checked = this.checked;
                    toggleAllPosts();
                }});
            }});
        </script>
    </body>
    </html>
    """
