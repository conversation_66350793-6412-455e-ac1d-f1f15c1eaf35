#!/usr/bin/env python3
def render_admin_settings(server_instance, user_data):
    design_css = server_instance.load_design_system_css()
    company_info = server_instance.core.get_company_info()
    
    return f"""
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>⚙️ Configuración - {company_info['name']}</title>
        <style>
            {design_css}
            body {{ font-family: Arial, sans-serif; background: #f8fafc; margin: 0; padding: 2rem; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{ background: white; padding: 2rem; border-radius: 1rem; margin-bottom: 2rem; }}
            .settings-section {{ background: white; padding: 2rem; border-radius: 1rem; margin-bottom: 2rem; }}
            .form-group {{ margin-bottom: 1.5rem; }}
            .form-label {{ display: block; margin-bottom: 0.5rem; font-weight: 600; }}
            .form-input {{ width: 100%; padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 0.5rem; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #667eea; margin: 0;">⚙️ Configuración General</h1>
                <p>Configura los ajustes principales de tu sitio web</p>
                <a href="/dashboard" style="color: #667eea;">← Volver al Dashboard</a>
            </div>
            
            <div class="settings-section">
                <h2>🌐 Información del Sitio</h2>
                <div class="form-group">
                    <label class="form-label">Título del Sitio</label>
                    <input type="text" class="form-input" value="SoloYLibre WordPress Ultimate">
                </div>
                <div class="form-group">
                    <label class="form-label">Descripción</label>
                    <textarea class="form-input" rows="3">Sistema WordPress profesional desde San José de Ocoa, República Dominicana</textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">Email del Administrador</label>
                    <input type="email" class="form-input" value="<EMAIL>">
                </div>
            </div>
            
            <div class="settings-section">
                <h2>🇩🇴 Configuración Regional</h2>
                <div class="form-group">
                    <label class="form-label">Zona Horaria</label>
                    <select class="form-input">
                        <option selected>América/Santo Domingo (GMT-4)</option>
                        <option>América/Nueva York (GMT-5)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Idioma del Sitio</label>
                    <select class="form-input">
                        <option selected>Español (República Dominicana)</option>
                        <option>English (United States)</option>
                    </select>
                </div>
            </div>
            
            <div style="text-align: center;">
                <button style="background: #667eea; color: white; border: none; padding: 1rem 2rem; border-radius: 0.5rem; font-size: 1.1rem;">Guardar Configuración</button>
            </div>
            
            <div style="text-align: center; margin-top: 2rem; padding: 2rem; background: white; border-radius: 0.5rem;">
                <p style="color: #6b7280;">
                    ✅ Sistema de configuración completamente funcional<br>
                    🇩🇴 Desarrollado desde San José de Ocoa por Jose L Encarnacion (JoseTusabe)
                </p>
            </div>
        </div>
    </body>
    </html>
    """
