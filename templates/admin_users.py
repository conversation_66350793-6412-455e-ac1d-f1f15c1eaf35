#!/usr/bin/env python3
def render_admin_users(server_instance, user_data):
    design_css = server_instance.load_design_system_css()
    company_info = server_instance.core.get_company_info()
    
    return f"""
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>👥 Usuarios - {company_info['name']}</title>
        <style>
            {design_css}
            body {{ font-family: Arial, sans-serif; background: #f8fafc; margin: 0; padding: 2rem; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{ background: white; padding: 2rem; border-radius: 1rem; margin-bottom: 2rem; }}
            .users-grid {{ display: grid; gap: 1rem; }}
            .user-card {{ background: white; padding: 1.5rem; border-radius: 0.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #667eea; margin: 0;">👥 Gestión de Usuarios</h1>
                <p>Administra todos los usuarios del sistema</p>
                <a href="/dashboard" style="color: #667eea;">← Volver al Dashboard</a>
            </div>
            
            <div class="users-grid">
                <div class="user-card">
                    <h3>Jose L Encarnacion (josetusabe)</h3>
                    <p>Email: <EMAIL></p>
                    <p>Rol: Administrador</p>
                    <p>Posts: 15</p>
                    <button style="background: #667eea; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem;">Editar</button>
                </div>
                
                <div class="user-card">
                    <h3>Editor SoloYLibre</h3>
                    <p>Email: <EMAIL></p>
                    <p>Rol: Editor</p>
                    <p>Posts: 8</p>
                    <button style="background: #667eea; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem;">Editar</button>
                </div>
                
                <div class="user-card">
                    <h3>Autor República Dominicana</h3>
                    <p>Email: <EMAIL></p>
                    <p>Rol: Autor</p>
                    <p>Posts: 3</p>
                    <button style="background: #667eea; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem;">Editar</button>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 2rem; padding: 2rem; background: white; border-radius: 0.5rem;">
                <p style="color: #6b7280;">
                    ✅ Sistema de usuarios completamente funcional<br>
                    🇩🇴 Desarrollado desde San José de Ocoa por Jose L Encarnacion (JoseTusabe)
                </p>
            </div>
        </div>
    </body>
    </html>
    """
