#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - Frontend Page Template
Página frontend profesional con contenido de San José de Ocoa
Desarrollado por Jose <PERSON>carnacion (JoseTusabe)
Desde San José de <PERSON>coa, República Dominicana 🇩🇴
"""

def render_frontend_page(server_instance):
    """Renderizar página frontend profesional"""
    
    design_css = server_instance.load_design_system_css()
    system_info = server_instance.core.get_system_info()
    company_info = server_instance.core.get_company_info()
    stats = server_instance.core.get_system_stats()
    
    # Obtener posts recientes
    recent_posts = server_instance.content.get_posts(limit=3)
    
    return f"""
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🇩🇴 SoloYLibre WordPress Ultimate - San José de Ocoa</title>
        <meta name="description" content="Sistema WordPress profesional desarrollado desde <PERSON>, República Dominicana por <PERSON>nac<PERSON> (JoseTusabe)">
        <meta name="keywords" content="WordPress, San José de Ocoa, República Dominicana, JoseTusabe, SoloYLibre, desarrollo web">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link rel="manifest" href="/manifest.json">
        <meta name="theme-color" content="#667eea">
        <style>
            {design_css}
            
            * {{
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }}
            
            body {{
                font-family: var(--font-family);
                line-height: 1.6;
                color: var(--color-gray-900);
            }}
            
            /* Hero Section */
            .hero {{
                background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 50%, var(--color-accent) 100%);
                background-size: 400% 400%;
                animation: gradientShift 20s ease infinite;
                color: var(--color-white);
                padding: var(--space-20) var(--space-6);
                text-align: center;
                position: relative;
                overflow: hidden;
            }}
            
            @keyframes gradientShift {{
                0% {{ background-position: 0% 50%; }}
                50% {{ background-position: 100% 50%; }}
                100% {{ background-position: 0% 50%; }}
            }}
            
            .hero::before {{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
                opacity: 0.3;
            }}
            
            .hero-content {{
                position: relative;
                z-index: 1;
                max-width: 1200px;
                margin: 0 auto;
            }}
            
            .hero h1 {{
                font-size: var(--text-8xl);
                font-weight: var(--font-black);
                margin-bottom: var(--space-6);
                text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                animation: slideUp 1s ease-out;
            }}
            
            .hero p {{
                font-size: var(--text-2xl);
                margin-bottom: var(--space-8);
                opacity: 0.95;
                animation: slideUp 1s ease-out 0.2s both;
            }}
            
            .hero-buttons {{
                display: flex;
                gap: var(--space-6);
                justify-content: center;
                flex-wrap: wrap;
                animation: slideUp 1s ease-out 0.4s both;
            }}
            
            .btn-hero {{
                padding: var(--space-5) var(--space-8);
                border: none;
                border-radius: var(--radius-xl);
                font-size: var(--text-lg);
                font-weight: var(--font-bold);
                text-decoration: none;
                transition: all 300ms var(--timing-smooth);
                display: inline-flex;
                align-items: center;
                gap: var(--space-3);
            }}
            
            .btn-primary {{
                background: var(--color-white);
                color: var(--color-primary);
            }}
            
            .btn-primary:hover {{
                transform: translateY(-3px);
                box-shadow: var(--shadow-2xl);
            }}
            
            .btn-outline {{
                background: transparent;
                color: var(--color-white);
                border: 2px solid var(--color-white);
            }}
            
            .btn-outline:hover {{
                background: var(--color-white);
                color: var(--color-primary);
            }}
            
            /* Navigation */
            .navbar {{
                background: var(--color-white);
                box-shadow: var(--shadow-lg);
                padding: var(--space-4) var(--space-6);
                position: sticky;
                top: 0;
                z-index: 100;
            }}
            
            .nav-container {{
                max-width: 1200px;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }}
            
            .logo {{
                font-size: var(--text-2xl);
                font-weight: var(--font-black);
                color: var(--color-primary);
                text-decoration: none;
            }}
            
            .nav-links {{
                display: flex;
                gap: var(--space-8);
                list-style: none;
            }}
            
            .nav-links a {{
                color: var(--color-gray-700);
                text-decoration: none;
                font-weight: var(--font-semibold);
                transition: color 300ms;
            }}
            
            .nav-links a:hover {{
                color: var(--color-primary);
            }}
            
            /* Sections */
            .section {{
                padding: var(--space-20) var(--space-6);
            }}
            
            .container {{
                max-width: 1200px;
                margin: 0 auto;
            }}
            
            .section-title {{
                font-size: var(--text-5xl);
                font-weight: var(--font-black);
                text-align: center;
                margin-bottom: var(--space-12);
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }}
            
            .features-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: var(--space-8);
                margin-bottom: var(--space-16);
            }}
            
            .feature-card {{
                background: var(--color-white);
                padding: var(--space-8);
                border-radius: var(--radius-2xl);
                box-shadow: var(--shadow-lg);
                text-align: center;
                transition: all 300ms var(--timing-smooth);
                border: 1px solid var(--color-gray-200);
            }}
            
            .feature-card:hover {{
                transform: translateY(-8px);
                box-shadow: var(--shadow-2xl);
            }}
            
            .feature-icon {{
                width: 80px;
                height: 80px;
                border-radius: var(--radius-2xl);
                background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto var(--space-6);
                font-size: var(--text-3xl);
                color: var(--color-white);
            }}
            
            .feature-card h3 {{
                font-size: var(--text-2xl);
                font-weight: var(--font-bold);
                margin-bottom: var(--space-4);
                color: var(--color-gray-900);
            }}
            
            .feature-card p {{
                color: var(--color-gray-600);
                font-size: var(--text-lg);
            }}
            
            /* Stats Section */
            .stats-section {{
                background: linear-gradient(135deg, var(--color-gray-900), var(--color-gray-800));
                color: var(--color-white);
            }}
            
            .stats-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: var(--space-8);
                text-align: center;
            }}
            
            .stat-item {{
                padding: var(--space-6);
            }}
            
            .stat-number {{
                font-size: var(--text-6xl);
                font-weight: var(--font-black);
                color: var(--color-accent);
                margin-bottom: var(--space-2);
            }}
            
            .stat-label {{
                font-size: var(--text-lg);
                opacity: 0.9;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            
            /* Footer */
            .footer {{
                background: var(--color-gray-900);
                color: var(--color-white);
                padding: var(--space-16) var(--space-6);
                text-align: center;
            }}
            
            .footer-content {{
                max-width: 1200px;
                margin: 0 auto;
            }}
            
            .footer h3 {{
                font-size: var(--text-3xl);
                font-weight: var(--font-bold);
                margin-bottom: var(--space-6);
                color: var(--color-accent);
            }}
            
            .footer p {{
                margin: var(--space-3) 0;
                opacity: 0.9;
            }}
            
            .footer-links {{
                display: flex;
                justify-content: center;
                gap: var(--space-8);
                margin: var(--space-8) 0;
                flex-wrap: wrap;
            }}
            
            .footer-links a {{
                color: var(--color-white);
                text-decoration: none;
                font-weight: var(--font-semibold);
                transition: color 300ms;
            }}
            
            .footer-links a:hover {{
                color: var(--color-accent);
            }}
            
            /* Animations */
            @keyframes slideUp {{
                from {{
                    opacity: 0;
                    transform: translateY(30px);
                }}
                to {{
                    opacity: 1;
                    transform: translateY(0);
                }}
            }}
            
            /* Responsive */
            @media (max-width: 768px) {{
                .hero h1 {{
                    font-size: var(--text-5xl);
                }}
                
                .hero p {{
                    font-size: var(--text-xl);
                }}
                
                .hero-buttons {{
                    flex-direction: column;
                    align-items: center;
                }}
                
                .nav-links {{
                    display: none;
                }}
                
                .features-grid {{
                    grid-template-columns: 1fr;
                }}
                
                .stats-grid {{
                    grid-template-columns: repeat(2, 1fr);
                }}
            }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar">
            <div class="nav-container">
                <a href="/" class="logo">🇩🇴 SoloYLibre</a>
                <ul class="nav-links">
                    <li><a href="#inicio">Inicio</a></li>
                    <li><a href="#caracteristicas">Características</a></li>
                    <li><a href="#estadisticas">Estadísticas</a></li>
                    <li><a href="/backend">Backend</a></li>
                    <li><a href="/soloylibre-admin">Admin</a></li>
                </ul>
            </div>
        </nav>
        
        <!-- Hero Section -->
        <section id="inicio" class="hero">
            <div class="hero-content">
                <h1>🇩🇴 WordPress Ultimate</h1>
                <p>Sistema WordPress profesional desarrollado desde San José de Ocoa, República Dominicana</p>
                <div class="hero-buttons">
                    <a href="/soloylibre-admin" class="btn-hero btn-primary">
                        <i class="fas fa-rocket"></i>
                        Acceder al Admin
                    </a>
                    <a href="/backend" class="btn-hero btn-outline">
                        <i class="fas fa-cogs"></i>
                        Panel de Control
                    </a>
                </div>
            </div>
        </section>
        
        <!-- Features Section -->
        <section id="caracteristicas" class="section">
            <div class="container">
                <h2 class="section-title">Características Principales</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h3>Sistema Ultimate</h3>
                        <p>WordPress con todas las funcionalidades profesionales implementadas desde cero</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3>Seguridad Avanzada</h3>
                        <p>Autenticación JWT, encriptación de contraseñas y sistema de sesiones seguro</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3>PWA Completa</h3>
                        <p>Progressive Web App con cache offline y notificaciones push</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3>Analytics en Tiempo Real</h3>
                        <p>Métricas completas de visitantes, posts y rendimiento del sistema</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3>API REST Completa</h3>
                        <p>Endpoints completos para integración con aplicaciones externas</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <h3>Desde San José de Ocoa</h3>
                        <p>Desarrollado con orgullo desde las montañas de República Dominicana</p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Stats Section -->
        <section id="estadisticas" class="section stats-section">
            <div class="container">
                <h2 class="section-title" style="color: var(--color-white);">Estadísticas del Sistema</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">{stats.get('posts', 0)}</div>
                        <div class="stat-label">Posts Publicados</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{stats.get('users', 0)}</div>
                        <div class="stat-label">Usuarios Registrados</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{stats.get('page_views', 0)}</div>
                        <div class="stat-label">Páginas Vistas</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{stats.get('unique_visitors', 0)}</div>
                        <div class="stat-label">Visitantes Únicos</div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <h3>🇩🇴 SoloYLibre Web Dev</h3>
                <p><strong>Desarrollado por:</strong> {company_info['developer']}</p>
                <p><strong>Ubicación:</strong> {company_info['location']}</p>
                <p><strong>Email:</strong> {company_info['email']} | <strong>Teléfono:</strong> {company_info['phone']}</p>
                <p><strong>Servidor:</strong> {company_info['server']}</p>
                
                <div class="footer-links">
                    <a href="/soloylibre-admin">Panel Admin</a>
                    <a href="/backend">Backend</a>
                    <a href="/api/">API REST</a>
                    <a href="/manifest.json">PWA</a>
                </div>
                
                <p style="margin-top: var(--space-8); opacity: 0.7;">
                    © 2025 SoloYLibre Web Dev - WordPress Ultimate v{system_info['version']}
                </p>
                <p style="opacity: 0.7;">
                    Hecho con ❤️ desde San José de Ocoa, República Dominicana 🇩🇴
                </p>
            </div>
        </footer>
        
        <script>
            // Registrar Service Worker
            if ('serviceWorker' in navigator) {{
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => console.log('✅ Service Worker registrado'))
                    .catch(error => console.log('❌ Error SW:', error));
            }}
            
            // Smooth scrolling para navegación
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {{
                anchor.addEventListener('click', function (e) {{
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {{
                        target.scrollIntoView({{
                            behavior: 'smooth',
                            block: 'start'
                        }});
                    }}
                }});
            }});
            
            // Animaciones al hacer scroll
            const observerOptions = {{
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            }};
            
            const observer = new IntersectionObserver((entries) => {{
                entries.forEach(entry => {{
                    if (entry.isIntersecting) {{
                        entry.target.style.animation = 'slideUp 0.6s ease-out';
                    }}
                }});
            }}, observerOptions);
            
            // Observar elementos para animaciones
            document.querySelectorAll('.feature-card, .stat-item').forEach(el => {{
                observer.observe(el);
            }});
            
            // Mostrar notificación de bienvenida
            setTimeout(() => {{
                console.log('🇩🇴 ¡Bienvenido a SoloYLibre WordPress Ultimate!');
                console.log('🏔️ Desarrollado desde San José de Ocoa, República Dominicana');
                console.log('👨‍💻 Por Jose L Encarnacion (JoseTusabe)');
            }}, 1000);
        </script>
    </body>
    </html>
    """
