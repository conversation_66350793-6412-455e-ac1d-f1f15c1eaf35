#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - Test WordPress Admin
Script para verificar todas las funcionalidades de wp-admin
Desarrollado por Jose <PERSON>carnacion (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import requests
import time

def test_wordpress_admin():
    """Probar todas las funcionalidades de WordPress admin"""
    print("🇩🇴 " + "="*80)
    print("📊 PROBANDO WORDPRESS ADMIN COMPLETO")
    print("👨‍💻 Desarrollado por Jose L Encarnacion (JoseTusabe)")
    print("🏔️ Desde San José de Ocoa, República Dominicana")
    print("="*80)
    
    base_url = "http://localhost:8080"
    
    # URLs de administración a probar
    admin_urls = [
        ('/dashboard', 'Dashboard Principal'),
        ('/soloylibre-admin/posts', 'Gestión de Posts'),
        ('/soloylibre-admin/pages', 'Gestión de Páginas'),
        ('/soloylibre-admin/media', 'Biblioteca de Medios'),
        ('/soloylibre-admin/comments', 'Gestión de Comentarios'),
        ('/soloylibre-admin/users', 'Gestión de Usuarios'),
        ('/soloylibre-admin/appearance', 'Personalización de Apariencia'),
        ('/soloylibre-admin/plugins', 'Gestión de Plugins'),
        ('/soloylibre-admin/settings', 'Configuración General'),
    ]
    
    print("\n🔍 PROBANDO PÁGINAS DE ADMINISTRACIÓN...")
    
    working_pages = 0
    total_pages = len(admin_urls)
    
    for url, description in admin_urls:
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}{url}", timeout=10)
            end_time = time.time()
            
            load_time = (end_time - start_time) * 1000
            
            if response.status_code == 200:
                print(f"✅ {description}: {url} ({load_time:.0f}ms)")
                working_pages += 1
                
                # Verificar contenido específico
                content = response.text
                if 'SoloYLibre' in content and 'WordPress Ultimate' in content:
                    print(f"   ✅ Branding correcto")
                else:
                    print(f"   ⚠️ Branding faltante")
                    
                if 'sidebar' in content.lower() or 'nav-menu' in content:
                    print(f"   ✅ Navegación presente")
                else:
                    print(f"   ⚠️ Navegación faltante")
                    
            else:
                print(f"❌ {description}: {url} (código {response.status_code})")
                
        except Exception as e:
            print(f"❌ {description}: {url} (error: {e})")
    
    print(f"\n📊 RESULTADO: {working_pages}/{total_pages} páginas funcionando")
    
    # Test de funcionalidades específicas
    print("\n🔍 PROBANDO FUNCIONALIDADES ESPECÍFICAS...")
    
    # Test 1: Filtros de posts
    print("\n📝 Test: Filtros de Posts")
    filter_tests = [
        ('/soloylibre-admin/posts?status=all', 'Todos los posts'),
        ('/soloylibre-admin/posts?status=publish', 'Posts publicados'),
        ('/soloylibre-admin/posts?status=draft', 'Borradores'),
        ('/soloylibre-admin/posts?status=trash', 'Papelera'),
        ('/soloylibre-admin/posts?search=test', 'Búsqueda de posts'),
    ]
    
    for url, description in filter_tests:
        try:
            response = requests.get(f"{base_url}{url}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} (código {response.status_code})")
        except Exception as e:
            print(f"   ❌ {description} (error: {e})")
    
    # Test 2: Navegación entre páginas
    print("\n🧭 Test: Navegación")
    navigation_tests = [
        ('Dashboard → Posts', '/dashboard', '/soloylibre-admin/posts'),
        ('Posts → Dashboard', '/soloylibre-admin/posts', '/dashboard'),
        ('Dashboard → Páginas', '/dashboard', '/soloylibre-admin/pages'),
        ('Páginas → Usuarios', '/soloylibre-admin/pages', '/soloylibre-admin/users'),
    ]
    
    for description, from_url, to_url in navigation_tests:
        try:
            # Verificar que ambas páginas cargan
            response1 = requests.get(f"{base_url}{from_url}", timeout=5)
            response2 = requests.get(f"{base_url}{to_url}", timeout=5)
            
            if response1.status_code == 200 and response2.status_code == 200:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
        except Exception as e:
            print(f"   ❌ {description} (error: {e})")
    
    # Test 3: Elementos de interfaz
    print("\n🎨 Test: Elementos de Interfaz")
    interface_tests = [
        ('Sidebar de navegación', 'sidebar'),
        ('Filtros de estado', 'status-filter'),
        ('Búsqueda', 'search-input'),
        ('Acciones en lote', 'bulk-actions'),
        ('Tabla de datos', 'table'),
        ('Paginación', 'pagination'),
    ]
    
    # Probar en la página de posts
    try:
        response = requests.get(f"{base_url}/soloylibre-admin/posts", timeout=10)
        content = response.text
        
        for description, element in interface_tests:
            if element in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
                
    except Exception as e:
        print(f"   ❌ Error probando interfaz: {e}")
    
    # Test 4: Rendimiento
    print("\n⚡ Test: Rendimiento")
    performance_urls = [
        '/dashboard',
        '/soloylibre-admin/posts',
        '/soloylibre-admin/pages',
        '/soloylibre-admin/users'
    ]
    
    total_time = 0
    for url in performance_urls:
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}{url}", timeout=10)
            end_time = time.time()
            
            load_time = (end_time - start_time) * 1000
            total_time += load_time
            
            if load_time < 500:
                status = "✅ Excelente"
            elif load_time < 1000:
                status = "✅ Bueno"
            elif load_time < 2000:
                status = "⚠️ Aceptable"
            else:
                status = "❌ Lento"
                
            print(f"   {status}: {url} ({load_time:.0f}ms)")
            
        except Exception as e:
            print(f"   ❌ {url} (error: {e})")
    
    avg_time = total_time / len(performance_urls)
    print(f"   📊 Tiempo promedio: {avg_time:.0f}ms")
    
    # Test 5: Compatibilidad WordPress
    print("\n🔌 Test: Compatibilidad WordPress")
    wordpress_features = [
        ('Gestión de Posts', '/soloylibre-admin/posts'),
        ('Gestión de Páginas', '/soloylibre-admin/pages'),
        ('Biblioteca de Medios', '/soloylibre-admin/media'),
        ('Gestión de Comentarios', '/soloylibre-admin/comments'),
        ('Gestión de Usuarios', '/soloylibre-admin/users'),
        ('Personalización', '/soloylibre-admin/appearance'),
        ('Plugins', '/soloylibre-admin/plugins'),
        ('Configuración', '/soloylibre-admin/settings'),
    ]
    
    wp_working = 0
    for feature, url in wordpress_features:
        try:
            response = requests.get(f"{base_url}{url}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {feature}")
                wp_working += 1
            else:
                print(f"   ❌ {feature}")
        except Exception as e:
            print(f"   ❌ {feature} (error: {e})")
    
    print(f"   📊 Funcionalidades WordPress: {wp_working}/{len(wordpress_features)}")
    
    # Resumen final
    print("\n" + "="*80)
    print("📊 RESUMEN FINAL")
    print("="*80)
    
    overall_score = (working_pages / total_pages) * 100
    
    print(f"✅ Páginas funcionando: {working_pages}/{total_pages} ({overall_score:.1f}%)")
    print(f"✅ Funcionalidades WordPress: {wp_working}/{len(wordpress_features)}")
    print(f"⚡ Rendimiento promedio: {avg_time:.0f}ms")
    
    if overall_score >= 90:
        print("\n🎉 ¡EXCELENTE! WordPress Admin funcionando perfectamente")
        grade = "A+"
    elif overall_score >= 80:
        print("\n✅ ¡MUY BIEN! WordPress Admin funcionando correctamente")
        grade = "A"
    elif overall_score >= 70:
        print("\n👍 ¡BIEN! WordPress Admin funcionando con ajustes menores")
        grade = "B"
    else:
        print("\n⚠️ WordPress Admin necesita mejoras")
        grade = "C"
    
    print(f"🏆 Calificación: {grade}")
    print("\n🇩🇴 Sistema WordPress Ultimate desde San José de Ocoa")
    print("👨‍💻 Desarrollado por Jose L Encarnacion (JoseTusabe)")
    print("="*80)

if __name__ == '__main__':
    test_wordpress_admin()
