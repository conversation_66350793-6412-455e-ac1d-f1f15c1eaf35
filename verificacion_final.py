#!/usr/bin/env python3
"""
SoloYLibre WordPress Ultimate - Verificación Final del Sistema
Script para verificar que todo funcione perfectamente
Desarrollado por Jose L Encarnacion (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import requests
import time
import json
from datetime import datetime

class VerificacionFinal:
    """Verificación completa del sistema WordPress Ultimate"""
    
    def __init__(self):
        self.base_url = "http://localhost:8080"
        self.resultados = []
        
    def print_header(self):
        """Header de verificación"""
        print("🇩🇴 " + "="*80)
        print("✅ SoloYLibre WordPress Ultimate - Verificación Final")
        print("👨‍💻 Desarrollado por Jose L Encarnacion (JoseTusabe)")
        print("🏔️ Desde San José de Ocoa, República Dominicana")
        print("🔍 Verificando que todo funcione perfectamente...")
        print("="*80)
    
    def verificar_url(self, path, descripcion, metodo='GET', data=None, esperado=200):
        """Verificar una URL específica"""
        try:
            url = f"{self.base_url}{path}"
            
            if metodo == 'GET':
                response = requests.get(url, timeout=10)
            elif metodo == 'POST':
                response = requests.post(url, data=data, timeout=10, allow_redirects=False)
            
            if response.status_code == esperado or (esperado == 200 and response.status_code in [200, 302]):
                print(f"✅ {descripcion}: {path} (código {response.status_code})")
                self.resultados.append({'url': path, 'status': 'OK', 'code': response.status_code})
                return True
            else:
                print(f"❌ {descripcion}: {path} (código {response.status_code})")
                self.resultados.append({'url': path, 'status': 'ERROR', 'code': response.status_code})
                return False
                
        except Exception as e:
            print(f"❌ {descripcion}: {path} (error: {e})")
            self.resultados.append({'url': path, 'status': 'ERROR', 'error': str(e)})
            return False
    
    def verificar_login_completo(self):
        """Verificar proceso completo de login"""
        print("\n🔐 VERIFICANDO PROCESO COMPLETO DE LOGIN...")
        
        # 1. Verificar página de login
        login_ok = self.verificar_url('/soloylibre-admin', 'Página de Login')
        
        # 2. Verificar login con credenciales
        if login_ok:
            login_data = {
                'log': 'josetusabe',
                'pwd': 'JoseTusabe2025!'
            }
            
            login_success = self.verificar_url(
                '/soloylibre-admin', 
                'Login con Credenciales', 
                metodo='POST', 
                data=login_data, 
                esperado=302
            )
            
            if login_success:
                print("✅ Login completo funcionando perfectamente")
                return True
        
        return False
    
    def verificar_api_completa(self):
        """Verificar API REST completa"""
        print("\n🔌 VERIFICANDO API REST COMPLETA...")
        
        endpoints = [
            ('/api/', 'API Principal'),
            ('/api/health', 'Health Check'),
            ('/api/stats', 'Estadísticas'),
            ('/api/posts', 'Posts API'),
        ]
        
        api_ok = 0
        for path, desc in endpoints:
            if self.verificar_url(path, desc):
                api_ok += 1
        
        print(f"📊 API funcionando: {api_ok}/{len(endpoints)} endpoints")
        return api_ok == len(endpoints)
    
    def verificar_pwa(self):
        """Verificar Progressive Web App"""
        print("\n📱 VERIFICANDO PWA...")
        
        pwa_files = [
            ('/manifest.json', 'PWA Manifest'),
            ('/sw.js', 'Service Worker')
        ]
        
        pwa_ok = 0
        for path, desc in pwa_files:
            if self.verificar_url(path, desc):
                pwa_ok += 1
        
        print(f"📊 PWA funcionando: {pwa_ok}/{len(pwa_files)} archivos")
        return pwa_ok == len(pwa_files)
    
    def verificar_rendimiento(self):
        """Verificar rendimiento del sistema"""
        print("\n⚡ VERIFICANDO RENDIMIENTO...")
        
        urls_rendimiento = [
            '/',
            '/soloylibre-admin',
            '/api/health'
        ]
        
        tiempos = []
        for url in urls_rendimiento:
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}{url}", timeout=10)
                end_time = time.time()
                
                tiempo = (end_time - start_time) * 1000  # en milisegundos
                tiempos.append(tiempo)
                
                if tiempo < 1000:  # menos de 1 segundo
                    print(f"✅ {url}: {tiempo:.0f}ms")
                else:
                    print(f"⚠️ {url}: {tiempo:.0f}ms (lento)")
                    
            except Exception as e:
                print(f"❌ {url}: Error - {e}")
        
        if tiempos:
            promedio = sum(tiempos) / len(tiempos)
            print(f"📊 Tiempo promedio de respuesta: {promedio:.0f}ms")
            return promedio < 1000
        
        return False
    
    def verificar_seguridad(self):
        """Verificar aspectos de seguridad"""
        print("\n🔒 VERIFICANDO SEGURIDAD...")
        
        # Verificar que URLs protegidas requieren autenticación
        urls_protegidas = [
            '/dashboard',
            '/soloylibre-admin/posts',
            '/soloylibre-admin/settings'
        ]
        
        seguridad_ok = 0
        for url in urls_protegidas:
            try:
                response = requests.get(f"{self.base_url}{url}", timeout=5)
                # Debe redirigir al login o mostrar login
                if response.status_code in [302, 200]:
                    print(f"✅ URL protegida: {url}")
                    seguridad_ok += 1
                else:
                    print(f"❌ URL no protegida: {url}")
            except:
                print(f"❌ Error verificando: {url}")
        
        print(f"📊 Seguridad: {seguridad_ok}/{len(urls_protegidas)} URLs protegidas")
        return seguridad_ok == len(urls_protegidas)
    
    def generar_reporte_final(self):
        """Generar reporte final de verificación"""
        print("\n📋 GENERANDO REPORTE FINAL...")
        
        reporte = {
            'timestamp': datetime.now().isoformat(),
            'sistema': 'SoloYLibre WordPress Ultimate',
            'desarrollador': 'Jose L Encarnacion (JoseTusabe)',
            'ubicacion': 'San José de Ocoa, República Dominicana',
            'resultados': self.resultados,
            'total_verificaciones': len(self.resultados),
            'exitosas': len([r for r in self.resultados if r['status'] == 'OK']),
            'fallidas': len([r for r in self.resultados if r['status'] == 'ERROR'])
        }
        
        # Guardar reporte
        with open('/Users/<USER>/Desktop/SoloYLibre-WordPress/reporte_verificacion_final.json', 'w') as f:
            json.dump(reporte, f, indent=2, ensure_ascii=False)
        
        return reporte
    
    def ejecutar_verificacion_completa(self):
        """Ejecutar verificación completa del sistema"""
        self.print_header()
        
        print("🚀 INICIANDO VERIFICACIÓN COMPLETA...")
        
        # Verificaciones principales
        verificaciones = [
            ('URLs Básicas', lambda: all([
                self.verificar_url('/', 'Página Principal'),
                self.verificar_url('/soloylibre-admin', 'Admin Login')
            ])),
            ('Login Completo', self.verificar_login_completo),
            ('API REST', self.verificar_api_completa),
            ('PWA', self.verificar_pwa),
            ('Rendimiento', self.verificar_rendimiento),
            ('Seguridad', self.verificar_seguridad)
        ]
        
        resultados_verificacion = []
        for nombre, verificacion in verificaciones:
            try:
                resultado = verificacion()
                resultados_verificacion.append((nombre, resultado))
            except Exception as e:
                print(f"❌ Error en {nombre}: {e}")
                resultados_verificacion.append((nombre, False))
        
        # Generar reporte
        reporte = self.generar_reporte_final()
        
        # Resumen final
        print("\n" + "="*80)
        print("🎯 RESUMEN DE VERIFICACIÓN FINAL")
        print("="*80)
        
        exitosas = sum(1 for _, resultado in resultados_verificacion if resultado)
        total = len(resultados_verificacion)
        
        for nombre, resultado in resultados_verificacion:
            status = "✅" if resultado else "❌"
            print(f"{status} {nombre}")
        
        print(f"\n📊 RESULTADO GENERAL: {exitosas}/{total} verificaciones exitosas")
        print(f"📊 URLs verificadas: {reporte['exitosas']}/{reporte['total_verificaciones']}")
        
        if exitosas == total and reporte['exitosas'] == reporte['total_verificaciones']:
            print("\n🎉 ¡SISTEMA FUNCIONANDO PERFECTAMENTE!")
            print("🇩🇴 WordPress Ultimate desde San José de Ocoa está 100% operativo")
            print("\n🚀 LISTO PARA IMPLEMENTAR MEJORAS 500%")
            print("📋 Revisa el archivo ROADMAP_MEJORAS_500.md para el plan completo")
        else:
            print("\n⚠️ SISTEMA NECESITA AJUSTES MENORES")
            print("🔧 Revisa los errores reportados")
        
        print("\n🔗 URLS PRINCIPALES VERIFICADAS:")
        print("🏠 Frontend: http://localhost:8080")
        print("🔧 Admin: http://localhost:8080/soloylibre-admin")
        print("📊 Dashboard: http://localhost:8080/dashboard")
        print("🔌 API: http://localhost:8080/api/")
        print("📱 PWA: http://localhost:8080/manifest.json")
        
        print("\n🔐 CREDENCIALES:")
        print("👤 Usuario: josetusabe")
        print("🔑 Contraseña: JoseTusabe2025!")
        
        print("="*80)
        
        return reporte

if __name__ == '__main__':
    verificador = VerificacionFinal()
    verificador.ejecutar_verificacion_completa()
