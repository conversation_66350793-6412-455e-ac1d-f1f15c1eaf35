#!/bin/bash
# SoloYLibre WordPress Ultimate - Verificación de Instalación
# Desarrollado por Jose L Encarnacion (JoseTusabe)
# Desde San José de <PERSON>coa, República Dominicana 🇩🇴

echo "🇩🇴 =============================================="
echo "🔍 VERIFICACIÓN DE INSTALACIÓN WORDPRESS"
echo "👨‍💻 Jose L Encarnacion (JoseTusabe)"
echo "🏔️ San José de Ocoa, República Dominicana"
echo "=============================================="

# Variables
PROJECT_DIR="SoloYLibre-WordPress-Fresh"
DB_NAME="soloylibre_wp"
DB_USER="soloylibre_admin"
DB_PASS="SoloYLibre2025!"
SITE_URL="http://localhost:8080"

# Contadores
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

# Función para mostrar resultado de test
test_result() {
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if [ $1 -eq 0 ]; then
        echo "✅ $2"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo "❌ $2"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
}

echo ""
echo "🔍 INICIANDO VERIFICACIÓN..."
echo ""

# Test 1: Verificar directorio del proyecto
echo "📁 Verificando estructura de archivos..."
if [ -d "$PROJECT_DIR" ]; then
    test_result 0 "Directorio del proyecto existe"
else
    test_result 1 "Directorio del proyecto NO existe"
fi

# Test 2: Verificar archivos principales de WordPress
if [ -f "$PROJECT_DIR/wp-config.php" ]; then
    test_result 0 "Archivo wp-config.php existe"
else
    test_result 1 "Archivo wp-config.php NO existe"
fi

if [ -f "$PROJECT_DIR/index.php" ]; then
    test_result 0 "Archivo index.php existe"
else
    test_result 1 "Archivo index.php NO existe"
fi

if [ -d "$PROJECT_DIR/wp-content" ]; then
    test_result 0 "Directorio wp-content existe"
else
    test_result 1 "Directorio wp-content NO existe"
fi

# Test 3: Verificar tema personalizado
if [ -d "$PROJECT_DIR/wp-content/themes/soloylibre-ultimate" ]; then
    test_result 0 "Tema SoloYLibre Ultimate existe"
else
    test_result 1 "Tema SoloYLibre Ultimate NO existe"
fi

if [ -f "$PROJECT_DIR/wp-content/themes/soloylibre-ultimate/style.css" ]; then
    test_result 0 "Archivo style.css del tema existe"
else
    test_result 1 "Archivo style.css del tema NO existe"
fi

if [ -f "$PROJECT_DIR/wp-content/themes/soloylibre-ultimate/functions.php" ]; then
    test_result 0 "Archivo functions.php del tema existe"
else
    test_result 1 "Archivo functions.php del tema NO existe"
fi

# Test 4: Verificar permisos
echo ""
echo "🔒 Verificando permisos de archivos..."

if [ -r "$PROJECT_DIR/wp-config.php" ]; then
    test_result 0 "wp-config.php es legible"
else
    test_result 1 "wp-config.php NO es legible"
fi

if [ -w "$PROJECT_DIR/wp-content" ]; then
    test_result 0 "wp-content es escribible"
else
    test_result 1 "wp-content NO es escribible"
fi

# Test 5: Verificar dependencias del sistema
echo ""
echo "🛠️ Verificando dependencias del sistema..."

if command -v php &> /dev/null; then
    PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2)
    test_result 0 "PHP está instalado (versión $PHP_VERSION)"
else
    test_result 1 "PHP NO está instalado"
fi

if command -v mysql &> /dev/null; then
    MYSQL_VERSION=$(mysql --version | cut -d' ' -f3)
    test_result 0 "MySQL está instalado (versión $MYSQL_VERSION)"
else
    test_result 1 "MySQL NO está instalado"
fi

if command -v curl &> /dev/null; then
    test_result 0 "curl está disponible"
else
    test_result 1 "curl NO está disponible"
fi

# Test 6: Verificar conexión a base de datos
echo ""
echo "🗄️ Verificando base de datos..."

# Verificar si la base de datos existe
DB_EXISTS=$(mysql -u "$DB_USER" -p"$DB_PASS" -e "SHOW DATABASES LIKE '$DB_NAME';" 2>/dev/null | grep -c "$DB_NAME")
if [ "$DB_EXISTS" -eq 1 ]; then
    test_result 0 "Base de datos '$DB_NAME' existe"
else
    test_result 1 "Base de datos '$DB_NAME' NO existe"
fi

# Verificar conexión con credenciales
if mysql -u "$DB_USER" -p"$DB_PASS" -e "SELECT 1;" &> /dev/null; then
    test_result 0 "Conexión a MySQL exitosa con credenciales"
else
    test_result 1 "NO se puede conectar a MySQL con credenciales"
fi

# Verificar tablas personalizadas
if mysql -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SHOW TABLES LIKE 'wp_soloylibre_%';" 2>/dev/null | grep -q "wp_soloylibre"; then
    test_result 0 "Tablas personalizadas SoloYLibre existen"
else
    test_result 1 "Tablas personalizadas SoloYLibre NO existen"
fi

# Test 7: Verificar servidor web
echo ""
echo "🌐 Verificando servidor web..."

# Verificar si el puerto está en uso
if lsof -i :8080 &> /dev/null; then
    test_result 0 "Puerto 8080 está en uso (servidor corriendo)"
    
    # Verificar respuesta HTTP
    if curl -s -o /dev/null -w "%{http_code}" "$SITE_URL" | grep -q "200"; then
        test_result 0 "Sitio web responde correctamente (HTTP 200)"
    else
        test_result 1 "Sitio web NO responde correctamente"
    fi
    
    # Verificar contenido de WordPress
    if curl -s "$SITE_URL" | grep -q "WordPress"; then
        test_result 0 "Contenido de WordPress detectado"
    else
        test_result 1 "Contenido de WordPress NO detectado"
    fi
    
    # Verificar tema SoloYLibre
    if curl -s "$SITE_URL" | grep -q "SoloYLibre"; then
        test_result 0 "Tema SoloYLibre activo"
    else
        test_result 1 "Tema SoloYLibre NO detectado"
    fi
    
    # Verificar información del desarrollador
    if curl -s "$SITE_URL" | grep -q "Jose L Encarnacion"; then
        test_result 0 "Información del desarrollador presente"
    else
        test_result 1 "Información del desarrollador NO presente"
    fi
    
else
    test_result 1 "Puerto 8080 NO está en uso (servidor no corriendo)"
    echo "   💡 Para iniciar el servidor: cd $PROJECT_DIR && php -S localhost:8080"
fi

# Test 8: Verificar configuración de WordPress
echo ""
echo "⚙️ Verificando configuración de WordPress..."

if [ -f "$PROJECT_DIR/wp-config.php" ]; then
    # Verificar configuración de base de datos
    if grep -q "DB_NAME.*$DB_NAME" "$PROJECT_DIR/wp-config.php"; then
        test_result 0 "Configuración de base de datos correcta"
    else
        test_result 1 "Configuración de base de datos incorrecta"
    fi
    
    # Verificar configuración de debug
    if grep -q "WP_DEBUG.*true" "$PROJECT_DIR/wp-config.php"; then
        test_result 0 "Debug de WordPress habilitado"
    else
        test_result 1 "Debug de WordPress NO habilitado"
    fi
    
    # Verificar información del desarrollador
    if grep -q "SOLOYLIBRE_DEVELOPER" "$PROJECT_DIR/wp-config.php"; then
        test_result 0 "Información del desarrollador en configuración"
    else
        test_result 1 "Información del desarrollador NO en configuración"
    fi
    
    # Verificar multisite
    if grep -q "WP_ALLOW_MULTISITE.*true" "$PROJECT_DIR/wp-config.php"; then
        test_result 0 "Multisite habilitado"
    else
        test_result 1 "Multisite NO habilitado"
    fi
fi

# Test 9: Verificar logs y errores
echo ""
echo "📋 Verificando logs y errores..."

if [ -f "$PROJECT_DIR/wp-content/debug.log" ]; then
    LOG_SIZE=$(wc -l < "$PROJECT_DIR/wp-content/debug.log")
    if [ "$LOG_SIZE" -lt 10 ]; then
        test_result 0 "Log de errores limpio ($LOG_SIZE líneas)"
    else
        test_result 1 "Log de errores tiene muchas entradas ($LOG_SIZE líneas)"
        echo "   💡 Revisar: tail -20 $PROJECT_DIR/wp-content/debug.log"
    fi
else
    test_result 0 "No hay archivo de log de errores (buena señal)"
fi

# Test 10: Verificar acceso al admin
echo ""
echo "🔧 Verificando panel de administración..."

if curl -s "$SITE_URL/wp-admin/" | grep -q "wp-login"; then
    test_result 0 "Panel de administración accesible"
else
    test_result 1 "Panel de administración NO accesible"
fi

# Resumen final
echo ""
echo "🇩🇴 =============================================="
echo "📊 RESUMEN DE VERIFICACIÓN"
echo "=============================================="
echo "✅ Tests pasados: $TESTS_PASSED"
echo "❌ Tests fallidos: $TESTS_FAILED"
echo "📊 Total de tests: $TOTAL_TESTS"

# Calcular porcentaje
if [ $TOTAL_TESTS -gt 0 ]; then
    PERCENTAGE=$((TESTS_PASSED * 100 / TOTAL_TESTS))
    echo "📈 Porcentaje de éxito: $PERCENTAGE%"
    
    if [ $PERCENTAGE -ge 90 ]; then
        echo ""
        echo "🎉 ¡INSTALACIÓN EXCELENTE!"
        echo "✅ WordPress SoloYLibre Ultimate funcionando perfectamente"
        echo "🇩🇴 Desarrollado desde San José de Ocoa por JoseTusabe"
        echo ""
        echo "🌐 Acceso al sitio: $SITE_URL"
        echo "🔧 Panel admin: $SITE_URL/wp-admin"
        echo "👤 Usuario: josetusabe"
        echo "🔑 Contraseña: JoseTusabe2025!"
        
    elif [ $PERCENTAGE -ge 70 ]; then
        echo ""
        echo "⚠️ INSTALACIÓN FUNCIONAL CON AJUSTES MENORES"
        echo "🔧 Revisar los tests fallidos arriba"
        
    else
        echo ""
        echo "❌ INSTALACIÓN NECESITA CORRECCIONES"
        echo "🛠️ Revisar y corregir los problemas identificados"
    fi
else
    echo "❌ No se pudieron ejecutar los tests"
fi

echo ""
echo "📞 Soporte: <EMAIL> | 718-713-5500"
echo "🇩🇴 Jose L Encarnacion (JoseTusabe) - San José de Ocoa, RD"
echo "=============================================="
