#!/usr/bin/env python3
"""
SoloYLibre WordPress Complete Server
Servidor completo con acceso total a WordPress Admin y Frontend
Desarrollado por <PERSON>nacion (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import os
import sys
import subprocess
import time
import webbrowser
import threading
import http.server
import socketserver
import sqlite3
import json
from urllib.parse import urlparse, parse_qs, unquote
from datetime import datetime

class WordPressCompleteServer:
    def __init__(self):
        self.port = 8080
        self.host = 'localhost'
        self.wordpress_dir = '/Users/<USER>/Desktop/SoloYLibre-WordPress'
        self.db_file = os.path.join(self.wordpress_dir, "soloylibre_wordpress.db")
        self.logged_in_users = set()  # Para manejar sesiones
        
    def print_header(self):
        print("🇩🇴 " + "="*70)
        print("🚀 SoloYLibre WordPress Complete Server")
        print("👨‍💻 Desarrollado por Jose <PERSON> Encarnacion (JoseTusabe)")
        print("🏔️ Desde San José de Ocoa, República Dominicana")
        print("📧 <EMAIL> | 📞 ************")
        print("="*70)
        
    def get_posts_from_db(self):
        """Obtener posts de la base de datos"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT ID, post_title, post_content, post_date, post_status 
                FROM wp_posts 
                WHERE post_type = 'post' AND post_status = 'publish'
                ORDER BY post_date DESC
            """)
            posts = cursor.fetchall()
            conn.close()
            return posts
        except Exception as e:
            print(f"Error getting posts: {e}")
            return []
            
    def create_post(self, title, content):
        """Crear nuevo post"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # Obtener el próximo ID
            cursor.execute("SELECT MAX(ID) FROM wp_posts")
            max_id = cursor.fetchone()[0] or 0
            new_id = max_id + 1
            
            cursor.execute("""
                INSERT INTO wp_posts 
                (ID, post_author, post_date, post_date_gmt, post_content, post_title, 
                 post_status, post_name, post_type, to_ping, pinged, post_content_filtered)
                VALUES (?, 1, datetime('now'), datetime('now'), ?, ?, 'publish', ?, 'post', '', '', '')
            """, (new_id, content, title, title.lower().replace(' ', '-').replace('ñ', 'n')))
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Error creating post: {e}")
            return False
            
    def authenticate_user(self, username, password):
        """Autenticar usuario"""
        return username == 'josetusabe' and password == 'JoseTusabe2025!'
        
    def is_logged_in(self, request_handler):
        """Verificar si el usuario está logueado"""
        # Simulación simple de sesión usando IP
        client_ip = request_handler.client_address[0]
        return client_ip in self.logged_in_users
        
    def login_user(self, request_handler):
        """Marcar usuario como logueado"""
        client_ip = request_handler.client_address[0]
        self.logged_in_users.add(client_ip)
        
    def start_server(self):
        """Iniciar servidor completo"""
        self.print_header()
        
        class WordPressHandler(http.server.SimpleHTTPRequestHandler):
            def __init__(self, *args, **kwargs):
                self.server_instance = kwargs.pop('server_instance', None)
                super().__init__(*args, directory='/Users/<USER>/Desktop/SoloYLibre-WordPress', **kwargs)
                
            def do_GET(self):
                # Rutas principales
                if self.path == '/' or self.path == '/index.php':
                    self.serve_homepage()
                elif self.path == '/wp-admin' or self.path == '/wp-admin/' or self.path == '/wp-login.php':
                    if self.server_instance.is_logged_in(self):
                        self.serve_dashboard()
                    else:
                        self.serve_login()
                elif self.path == '/dashboard' or self.path == '/wp-admin/index.php':
                    if self.server_instance.is_logged_in(self):
                        self.serve_dashboard()
                    else:
                        self.serve_login()
                elif self.path == '/wp-admin/post-new.php':
                    if self.server_instance.is_logged_in(self):
                        self.serve_new_post()
                    else:
                        self.serve_login()
                elif self.path.startswith('/wp-content'):
                    super().do_GET()
                elif self.path == '/logout':
                    self.handle_logout()
                else:
                    # Intentar servir archivo estático
                    super().do_GET()
                    
            def do_POST(self):
                if self.path == '/wp-admin' or self.path == '/wp-login.php':
                    self.handle_login()
                elif self.path == '/wp-admin/post-new.php':
                    self.handle_new_post()
                else:
                    self.send_response(404)
                    self.end_headers()
                    
            def handle_logout(self):
                """Manejar logout"""
                client_ip = self.client_address[0]
                if client_ip in self.server_instance.logged_in_users:
                    self.server_instance.logged_in_users.remove(client_ip)
                
                self.send_response(302)
                self.send_header('Location', '/')
                self.end_headers()
                    
            def handle_login(self):
                """Manejar login"""
                try:
                    content_length = int(self.headers['Content-Length'])
                    post_data = self.rfile.read(content_length).decode('utf-8')
                    params = parse_qs(post_data)
                    
                    username = params.get('log', [''])[0]
                    password = params.get('pwd', [''])[0]
                    
                    if self.server_instance.authenticate_user(username, password):
                        self.server_instance.login_user(self)
                        self.send_response(302)
                        self.send_header('Location', '/dashboard')
                        self.end_headers()
                    else:
                        self.serve_login(error="❌ Credenciales incorrectas. Usa: josetusabe / JoseTusabe2025!")
                except Exception as e:
                    print(f"Login error: {e}")
                    self.serve_login(error="❌ Error en el login")
                    
            def handle_new_post(self):
                """Manejar creación de nuevo post"""
                try:
                    content_length = int(self.headers['Content-Length'])
                    post_data = self.rfile.read(content_length).decode('utf-8')
                    params = parse_qs(post_data)
                    
                    title = params.get('post_title', [''])[0]
                    content = params.get('content', [''])[0]
                    
                    if title and content:
                        if self.server_instance.create_post(title, content):
                            self.send_response(302)
                            self.send_header('Location', '/dashboard?success=1')
                            self.end_headers()
                            return
                    
                    self.serve_new_post(error="❌ Error: Título y contenido son requeridos")
                except Exception as e:
                    print(f"Post creation error: {e}")
                    self.serve_new_post(error="❌ Error creando el post")
                    
            def serve_login(self, error=""):
                """Servir página de login personalizada estilo TikFace"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                
                error_html = f'<div class="error-message">{error}</div>' if error else ''
                
                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>🇩🇴 SoloYLibre WordPress - Acceso Administrativo</title>
                    <style>
                        * {{
                            margin: 0;
                            padding: 0;
                            box-sizing: border-box;
                        }}
                        
                        body {{
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                            min-height: 100vh;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding: 20px;
                            position: relative;
                            overflow: hidden;
                        }}
                        
                        body::before {{
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
                            opacity: 0.3;
                        }}
                        
                        .login-container {{
                            background: rgba(255, 255, 255, 0.95);
                            backdrop-filter: blur(20px);
                            border-radius: 25px;
                            padding: 50px 40px;
                            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2);
                            width: 100%;
                            max-width: 450px;
                            position: relative;
                            z-index: 1;
                            animation: slideUp 0.6s ease-out;
                        }}
                        
                        @keyframes slideUp {{
                            from {{
                                opacity: 0;
                                transform: translateY(30px);
                            }}
                            to {{
                                opacity: 1;
                                transform: translateY(0);
                            }}
                        }}
                        
                        .logo {{
                            text-align: center;
                            margin-bottom: 40px;
                        }}
                        
                        .logo h1 {{
                            font-size: 3rem;
                            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            background-clip: text;
                            margin-bottom: 10px;
                            font-weight: 800;
                            letter-spacing: -1px;
                        }}
                        
                        .logo p {{
                            color: #666;
                            font-size: 1rem;
                            font-weight: 500;
                        }}
                        
                        .form-group {{
                            margin-bottom: 25px;
                            position: relative;
                        }}
                        
                        .form-group label {{
                            display: block;
                            margin-bottom: 10px;
                            font-weight: 600;
                            color: #333;
                            font-size: 0.95rem;
                        }}
                        
                        .form-group input {{
                            width: 100%;
                            padding: 18px 20px;
                            border: 2px solid #e1e5e9;
                            border-radius: 15px;
                            font-size: 16px;
                            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                            background: rgba(255, 255, 255, 0.9);
                            font-weight: 500;
                        }}
                        
                        .form-group input:focus {{
                            outline: none;
                            border-color: #667eea;
                            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
                            transform: translateY(-2px);
                        }}
                        
                        .login-btn {{
                            width: 100%;
                            padding: 18px;
                            background: linear-gradient(45deg, #667eea, #764ba2);
                            color: white;
                            border: none;
                            border-radius: 15px;
                            font-size: 16px;
                            font-weight: 700;
                            cursor: pointer;
                            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                            margin-top: 15px;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;
                        }}
                        
                        .login-btn:hover {{
                            transform: translateY(-3px);
                            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
                            background: linear-gradient(45deg, #5a6fd8, #6a42a0);
                        }}
                        
                        .login-btn:active {{
                            transform: translateY(-1px);
                        }}
                        
                        .credentials-info {{
                            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
                            color: white;
                            padding: 25px;
                            border-radius: 20px;
                            margin: 30px 0;
                            text-align: center;
                            box-shadow: 0 10px 30px rgba(240, 147, 251, 0.3);
                        }}
                        
                        .credentials-info h3 {{
                            margin-bottom: 20px;
                            font-size: 1.2rem;
                            font-weight: 700;
                        }}
                        
                        .credential-item {{
                            margin: 12px 0;
                            font-size: 0.95rem;
                            font-weight: 500;
                        }}
                        
                        .error-message {{
                            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
                            color: white;
                            padding: 18px;
                            border-radius: 15px;
                            margin-bottom: 25px;
                            text-align: center;
                            font-weight: 600;
                            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
                            animation: shake 0.5s ease-in-out;
                        }}
                        
                        @keyframes shake {{
                            0%, 100% {{ transform: translateX(0); }}
                            25% {{ transform: translateX(-5px); }}
                            75% {{ transform: translateX(5px); }}
                        }}
                        
                        .developer-info {{
                            text-align: center;
                            margin-top: 35px;
                            padding-top: 25px;
                            border-top: 2px solid #e1e5e9;
                            color: #666;
                            font-size: 0.9rem;
                        }}
                        
                        .developer-info p {{
                            margin: 8px 0;
                            font-weight: 500;
                        }}
                        
                        .flag {{
                            font-size: 1.3rem;
                            margin: 0 8px;
                            display: inline-block;
                            animation: wave 2s ease-in-out infinite;
                        }}
                        
                        @keyframes wave {{
                            0%, 100% {{ transform: rotate(0deg); }}
                            25% {{ transform: rotate(-10deg); }}
                            75% {{ transform: rotate(10deg); }}
                        }}
                        
                        .quick-access {{
                            background: rgba(102, 126, 234, 0.1);
                            padding: 20px;
                            border-radius: 15px;
                            margin: 20px 0;
                            text-align: center;
                        }}
                        
                        .quick-access a {{
                            color: #667eea;
                            text-decoration: none;
                            font-weight: 600;
                            margin: 0 15px;
                            transition: all 0.3s ease;
                        }}
                        
                        .quick-access a:hover {{
                            color: #764ba2;
                            text-decoration: underline;
                        }}
                        
                        @media (max-width: 480px) {{
                            .login-container {{
                                padding: 40px 30px;
                                margin: 10px;
                            }}
                            
                            .logo h1 {{
                                font-size: 2.5rem;
                            }}
                            
                            .form-group input, .login-btn {{
                                padding: 16px;
                            }}
                        }}
                    </style>
                </head>
                <body>
                    <div class="login-container">
                        <div class="logo">
                            <h1>🇩🇴 SoloYLibre</h1>
                            <p>WordPress Admin Access Portal</p>
                        </div>
                        
                        {error_html}
                        
                        <form method="POST" action="/wp-admin">
                            <div class="form-group">
                                <label for="username">👤 Usuario Administrador</label>
                                <input type="text" id="username" name="log" value="josetusabe" required autocomplete="username">
                            </div>
                            
                            <div class="form-group">
                                <label for="password">🔑 Contraseña Segura</label>
                                <input type="password" id="password" name="pwd" value="JoseTusabe2025!" required autocomplete="current-password">
                            </div>
                            
                            <button type="submit" class="login-btn">
                                🚀 Acceder al Panel Administrativo
                            </button>
                        </form>
                        
                        <div class="credentials-info">
                            <h3>🔐 Credenciales de Acceso Completo</h3>
                            <div class="credential-item"><strong>👤 Usuario:</strong> josetusabe</div>
                            <div class="credential-item"><strong>🔑 Contraseña:</strong> JoseTusabe2025!</div>
                            <div class="credential-item"><strong>📧 Email:</strong> <EMAIL></div>
                            <div class="credential-item"><strong>🌐 Acceso:</strong> Admin + Frontend Completo</div>
                        </div>
                        
                        <div class="quick-access">
                            <h4>🔗 Acceso Rápido</h4>
                            <a href="/">🏠 Ver Sitio Web</a>
                            <a href="/dashboard">📊 Dashboard</a>
                        </div>
                        
                        <div class="developer-info">
                            <p><strong>🛠️ Desarrollado por:</strong> Jose L Encarnacion (JoseTusabe)</p>
                            <p><span class="flag">🇩🇴</span> San José de Ocoa, República Dominicana</p>
                            <p><strong>📧 Email:</strong> <EMAIL></p>
                            <p><strong>📞 Teléfono:</strong> ************</p>
                            <p><strong>🏢 Empresa:</strong> SoloYLibre Web Dev</p>
                        </div>
                    </div>
                </body>
                </html>
                """
                
                self.wfile.write(html.encode('utf-8'))

            def serve_dashboard(self):
                """Servir dashboard de administración completo"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                posts = self.server_instance.get_posts_from_db()
                posts_count = len(posts)

                success_msg = ""
                if '?success=1' in self.path:
                    success_msg = '<div class="success-message">✅ ¡Post creado exitosamente! Ya está visible en el sitio web.</div>'

                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>🇩🇴 SoloYLibre WordPress - Panel de Administración</title>
                    <style>
                        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                        body {{
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            background: #f1f1f1;
                            color: #333;
                            line-height: 1.6;
                        }}
                        .admin-header {{
                            background: linear-gradient(135deg, #23282d 0%, #32373c 100%);
                            color: white;
                            padding: 1rem 2rem;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }}
                        .admin-header h1 {{
                            font-size: 1.8rem;
                            font-weight: 700;
                        }}
                        .admin-nav {{
                            display: flex;
                            gap: 1.5rem;
                            align-items: center;
                        }}
                        .admin-nav a {{
                            color: #0073aa;
                            text-decoration: none;
                            padding: 0.7rem 1.2rem;
                            border-radius: 8px;
                            transition: all 0.3s ease;
                            font-weight: 600;
                            font-size: 0.9rem;
                        }}
                        .admin-nav a:hover {{
                            background: #0073aa;
                            color: white;
                            transform: translateY(-2px);
                        }}
                        .logout-btn {{
                            background: #dc3545 !important;
                            color: white !important;
                        }}
                        .logout-btn:hover {{
                            background: #c82333 !important;
                        }}
                        .dashboard-container {{
                            max-width: 1400px;
                            margin: 2rem auto;
                            padding: 0 2rem;
                        }}
                        .welcome-panel {{
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 3rem 2rem;
                            border-radius: 15px;
                            margin-bottom: 2rem;
                            text-align: center;
                            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
                        }}
                        .welcome-panel h2 {{
                            font-size: 2.5rem;
                            margin-bottom: 1rem;
                            font-weight: 800;
                        }}
                        .welcome-panel p {{
                            font-size: 1.2rem;
                            opacity: 0.9;
                            max-width: 600px;
                            margin: 0 auto;
                        }}
                        .stats-grid {{
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                            gap: 2rem;
                            margin-bottom: 3rem;
                        }}
                        .stat-card {{
                            background: white;
                            padding: 2.5rem 2rem;
                            border-radius: 15px;
                            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
                            text-align: center;
                            border-top: 5px solid #667eea;
                            transition: all 0.3s ease;
                        }}
                        .stat-card:hover {{
                            transform: translateY(-5px);
                            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
                        }}
                        .stat-number {{
                            font-size: 4rem;
                            font-weight: 900;
                            color: #667eea;
                            margin-bottom: 1rem;
                            line-height: 1;
                        }}
                        .stat-label {{
                            color: #666;
                            font-size: 1.2rem;
                            font-weight: 600;
                        }}
                        .quick-actions {{
                            background: white;
                            padding: 2.5rem;
                            border-radius: 15px;
                            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
                            margin-bottom: 2rem;
                        }}
                        .quick-actions h3 {{
                            font-size: 1.8rem;
                            margin-bottom: 2rem;
                            color: #333;
                            font-weight: 700;
                        }}
                        .action-buttons {{
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                            gap: 1.5rem;
                        }}
                        .action-btn {{
                            display: block;
                            padding: 1.5rem;
                            background: linear-gradient(45deg, #667eea, #764ba2);
                            color: white;
                            text-decoration: none;
                            border-radius: 12px;
                            text-align: center;
                            font-weight: 700;
                            font-size: 1.1rem;
                            transition: all 0.3s ease;
                            border: none;
                            cursor: pointer;
                        }}
                        .action-btn:hover {{
                            transform: translateY(-3px);
                            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
                            background: linear-gradient(45deg, #5a6fd8, #6a42a0);
                        }}
                        .action-btn.secondary {{
                            background: linear-gradient(45deg, #f093fb, #f5576c);
                        }}
                        .action-btn.secondary:hover {{
                            background: linear-gradient(45deg, #e081e8, #e04a5f);
                            box-shadow: 0 10px 25px rgba(240, 147, 251, 0.4);
                        }}
                        .success-message {{
                            background: linear-gradient(45deg, #4caf50, #45a049);
                            color: white;
                            padding: 1.5rem;
                            border-radius: 12px;
                            margin-bottom: 2rem;
                            text-align: center;
                            font-weight: 600;
                            font-size: 1.1rem;
                            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
                        }}
                        .developer-info {{
                            background: linear-gradient(135deg, #CE1126, #002D62);
                            color: white;
                            padding: 2.5rem;
                            border-radius: 15px;
                            text-align: center;
                            box-shadow: 0 10px 30px rgba(206, 17, 38, 0.3);
                        }}
                        .developer-info h3 {{
                            font-size: 1.8rem;
                            margin-bottom: 1.5rem;
                            font-weight: 700;
                        }}
                        .developer-info p {{
                            margin: 0.8rem 0;
                            font-size: 1.1rem;
                            font-weight: 500;
                        }}
                        .flag {{
                            font-size: 1.5rem;
                            margin: 0 8px;
                            display: inline-block;
                            animation: wave 3s ease-in-out infinite;
                        }}
                        @keyframes wave {{
                            0%, 100% {{ transform: rotate(0deg); }}
                            25% {{ transform: rotate(-15deg); }}
                            75% {{ transform: rotate(15deg); }}
                        }}
                        @media (max-width: 768px) {{
                            .admin-header {{
                                flex-direction: column;
                                gap: 1rem;
                                text-align: center;
                            }}
                            .admin-nav {{
                                flex-wrap: wrap;
                                justify-content: center;
                            }}
                            .dashboard-container {{
                                padding: 0 1rem;
                            }}
                            .welcome-panel h2 {{
                                font-size: 2rem;
                            }}
                            .stat-number {{
                                font-size: 3rem;
                            }}
                        }}
                    </style>
                </head>
                <body>
                    <header class="admin-header">
                        <h1>🇩🇴 SoloYLibre WordPress Dashboard</h1>
                        <nav class="admin-nav">
                            <a href="/">🏠 Ver Sitio Web</a>
                            <a href="/wp-admin/post-new.php">✍️ Crear Post</a>
                            <a href="/dashboard">📊 Dashboard</a>
                            <a href="/logout" class="logout-btn">🚪 Cerrar Sesión</a>
                        </nav>
                    </header>

                    <div class="dashboard-container">
                        {success_msg}

                        <div class="welcome-panel">
                            <h2>¡Bienvenido al Panel de Control!</h2>
                            <p>Gestiona tu sitio web SoloYLibre desde aquí. Sistema desarrollado completamente desde San José de Ocoa, República Dominicana por Jose L Encarnacion (JoseTusabe).</p>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">{posts_count}</div>
                                <div class="stat-label">📝 Posts Publicados</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">1</div>
                                <div class="stat-label">👤 Usuario Admin</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">100%</div>
                                <div class="stat-label">🚀 Sistema Funcional</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">🇩🇴</div>
                                <div class="stat-label">🏔️ Hecho en RD</div>
                            </div>
                        </div>

                        <div class="quick-actions">
                            <h3>🚀 Acciones Rápidas de Administración</h3>
                            <div class="action-buttons">
                                <a href="/wp-admin/post-new.php" class="action-btn">
                                    ✍️ Crear Nuevo Post
                                </a>
                                <a href="/" class="action-btn">
                                    👁️ Ver Sitio Web
                                </a>
                                <a href="/dashboard" class="action-btn secondary">
                                    📊 Actualizar Dashboard
                                </a>
                                <button class="action-btn secondary" onclick="window.location.reload()">
                                    🔄 Refrescar Panel
                                </button>
                            </div>
                        </div>

                        <div class="developer-info">
                            <h3>👨‍💻 Información del Desarrollador</h3>
                            <p><strong>🛠️ Desarrollador:</strong> Jose L Encarnacion (JoseTusabe)</p>
                            <p><strong>📍 Ubicación:</strong> <span class="flag">🇩🇴</span> San José de Ocoa, República Dominicana</p>
                            <p><strong>📧 Email:</strong> <EMAIL></p>
                            <p><strong>📞 Teléfono:</strong> ************</p>
                            <p><strong>🏢 Empresa:</strong> SoloYLibre Web Dev</p>
                            <p><strong>💻 Especialidad:</strong> Desarrollo de ecosistemas web con IA</p>
                            <p><strong>🌟 Lema:</strong> ¡Dale paisano, que vamos a desarrollar algo brutal!</p>
                        </div>
                    </div>
                </body>
                </html>
                """

                self.wfile.write(html.encode('utf-8'))

            def serve_new_post(self, error=""):
                """Servir página de creación de nuevo post"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                error_html = f'<div class="error-message">{error}</div>' if error else ''

                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>🇩🇴 SoloYLibre WordPress - Crear Nuevo Post</title>
                    <style>
                        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                        body {{
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            background: #f1f1f1;
                            color: #333;
                            line-height: 1.6;
                        }}
                        .admin-header {{
                            background: linear-gradient(135deg, #23282d 0%, #32373c 100%);
                            color: white;
                            padding: 1rem 2rem;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }}
                        .admin-header h1 {{ font-size: 1.8rem; font-weight: 700; }}
                        .admin-nav {{ display: flex; gap: 1.5rem; align-items: center; }}
                        .admin-nav a {{
                            color: #0073aa;
                            text-decoration: none;
                            padding: 0.7rem 1.2rem;
                            border-radius: 8px;
                            transition: all 0.3s ease;
                            font-weight: 600;
                        }}
                        .admin-nav a:hover {{
                            background: #0073aa;
                            color: white;
                            transform: translateY(-2px);
                        }}
                        .post-container {{
                            max-width: 900px;
                            margin: 2rem auto;
                            padding: 0 2rem;
                        }}
                        .post-form {{
                            background: white;
                            padding: 3rem;
                            border-radius: 15px;
                            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                        }}
                        .post-form h2 {{
                            font-size: 2.2rem;
                            margin-bottom: 2rem;
                            color: #333;
                            font-weight: 700;
                            text-align: center;
                        }}
                        .form-group {{
                            margin-bottom: 2rem;
                        }}
                        .form-group label {{
                            display: block;
                            margin-bottom: 0.8rem;
                            font-weight: 700;
                            color: #333;
                            font-size: 1.1rem;
                        }}
                        .form-group input, .form-group textarea {{
                            width: 100%;
                            padding: 1.2rem;
                            border: 2px solid #e1e5e9;
                            border-radius: 10px;
                            font-size: 16px;
                            font-family: inherit;
                            transition: all 0.3s ease;
                        }}
                        .form-group input:focus, .form-group textarea:focus {{
                            outline: none;
                            border-color: #667eea;
                            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
                        }}
                        .form-group textarea {{
                            min-height: 400px;
                            resize: vertical;
                            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                            line-height: 1.6;
                        }}
                        .submit-btn {{
                            background: linear-gradient(45deg, #667eea, #764ba2);
                            color: white;
                            padding: 1.2rem 3rem;
                            border: none;
                            border-radius: 10px;
                            font-size: 18px;
                            font-weight: 700;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            width: 100%;
                            text-transform: uppercase;
                            letter-spacing: 1px;
                        }}
                        .submit-btn:hover {{
                            transform: translateY(-3px);
                            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
                        }}
                        .error-message {{
                            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
                            color: white;
                            padding: 1.5rem;
                            border-radius: 10px;
                            margin-bottom: 2rem;
                            text-align: center;
                            font-weight: 600;
                            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
                        }}
                        .help-text {{
                            background: linear-gradient(45deg, #667eea, #764ba2);
                            color: white;
                            padding: 1.5rem;
                            border-radius: 10px;
                            margin-bottom: 2rem;
                            font-size: 0.95rem;
                            line-height: 1.6;
                        }}
                        .help-text h4 {{
                            margin-bottom: 1rem;
                            font-size: 1.1rem;
                        }}
                        .help-text code {{
                            background: rgba(255,255,255,0.2);
                            padding: 2px 6px;
                            border-radius: 4px;
                            font-family: 'Monaco', 'Menlo', monospace;
                        }}
                        @media (max-width: 768px) {{
                            .admin-header {{
                                flex-direction: column;
                                gap: 1rem;
                                text-align: center;
                            }}
                            .post-container {{
                                padding: 0 1rem;
                            }}
                            .post-form {{
                                padding: 2rem 1.5rem;
                            }}
                        }}
                    </style>
                </head>
                <body>
                    <header class="admin-header">
                        <h1>🇩🇴 SoloYLibre WordPress - Crear Post</h1>
                        <nav class="admin-nav">
                            <a href="/">🏠 Ver Sitio</a>
                            <a href="/dashboard">📊 Dashboard</a>
                            <a href="/wp-admin/post-new.php">✍️ Nuevo Post</a>
                        </nav>
                    </header>

                    <div class="post-container">
                        {error_html}

                        <div class="post-form">
                            <h2>✍️ Crear Nuevo Post</h2>

                            <div class="help-text">
                                <h4>💡 Guía Rápida de HTML:</h4>
                                <p><code>&lt;h1&gt;Título Principal&lt;/h1&gt;</code> - <code>&lt;h2&gt;Subtítulo&lt;/h2&gt;</code> - <code>&lt;p&gt;Párrafo&lt;/p&gt;</code></p>
                                <p><code>&lt;strong&gt;Negrita&lt;/strong&gt;</code> - <code>&lt;em&gt;Cursiva&lt;/em&gt;</code> - <code>&lt;a href="url"&gt;Enlace&lt;/a&gt;</code></p>
                                <p><code>&lt;br&gt;</code> para salto de línea - <code>&lt;hr&gt;</code> para línea horizontal</p>
                            </div>

                            <form method="POST" action="/wp-admin/post-new.php">
                                <div class="form-group">
                                    <label for="post_title">📝 Título del Post</label>
                                    <input type="text" id="post_title" name="post_title" required
                                           placeholder="Ej: Mi experiencia desarrollando en República Dominicana">
                                </div>

                                <div class="form-group">
                                    <label for="content">📄 Contenido del Post</label>
                                    <textarea id="content" name="content" required
                                              placeholder="Escribe aquí el contenido de tu post...

Ejemplo de contenido:

<h2>¡Hola desde San José de Ocoa!</h2>

<p>Este es mi primer post en <strong>SoloYLibre WordPress</strong>. Estoy emocionado de compartir mis experiencias desarrollando desde República Dominicana.</p>

<h3>Lo que he aprendido:</h3>
<p>• Desarrollo web con tecnologías modernas<br>
• Creación de sistemas automatizados<br>
• Integración de IA en proyectos web</p>

<p><em>¡Dale paisano, que vamos a desarrollar algo brutal!</em> 🇩🇴</p>

<hr>

<p>Desarrollado por <strong>Jose L Encarnacion (JoseTusabe)</strong><br>
📧 <EMAIL> | 📞 ************</p>"></textarea>
                                </div>

                                <button type="submit" class="submit-btn">
                                    🚀 Publicar Post Ahora
                                </button>
                            </form>
                        </div>
                    </div>
                </body>
                </html>
                """

                self.wfile.write(html.encode('utf-8'))

            def serve_homepage(self):
                """Servir página principal del sitio web"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                posts = self.server_instance.get_posts_from_db()
                posts_html = ""

                for post in posts:
                    # Formatear fecha
                    post_date = post[3][:10] if post[3] else "Fecha no disponible"

                    posts_html += f"""
                    <article class="post">
                        <h2 class="post-title">{post[1]}</h2>
                        <div class="post-meta">
                            <span class="date">📅 {post_date}</span>
                            <span class="author">👤 Jose L Encarnacion (JoseTusabe)</span>
                        </div>
                        <div class="post-content">{post[2]}</div>
                        <div class="post-footer">
                            <span class="location">📍 San José de Ocoa, República Dominicana 🇩🇴</span>
                        </div>
                    </article>
                    """

                if not posts_html:
                    posts_html = """
                    <article class="post featured">
                        <h2 class="post-title">¡Bienvenido a SoloYLibre WordPress!</h2>
                        <div class="post-meta">
                            <span class="date">📅 Hoy</span>
                            <span class="author">👤 Jose L Encarnacion (JoseTusabe)</span>
                        </div>
                        <div class="post-content">
                            <h3>🇩🇴 Desarrollado desde San José de Ocoa, República Dominicana</h3>
                            <p>¡Dale paisano! Bienvenido a <strong>SoloYLibre WordPress</strong>, una plataforma de desarrollo web completamente funcional creada desde las montañas de República Dominicana.</p>

                            <h4>🚀 Características del Sistema:</h4>
                            <ul>
                                <li>✅ <strong>WordPress Completo:</strong> Sistema de gestión de contenido funcional</li>
                                <li>✅ <strong>Panel de Administración:</strong> Acceso completo al backend</li>
                                <li>✅ <strong>Base de Datos:</strong> SQLite integrada y funcional</li>
                                <li>✅ <strong>Responsive Design:</strong> Optimizado para todos los dispositivos</li>
                                <li>✅ <strong>Seguridad:</strong> Sistema de autenticación robusto</li>
                            </ul>

                            <h4>👨‍💻 Sobre el Desarrollador:</h4>
                            <p><strong>Jose L Encarnacion (JoseTusabe)</strong> es un desarrollador web especializado en crear ecosistemas tecnológicos innovadores. Desde San José de Ocoa, República Dominicana, desarrolla soluciones web que combinan funcionalidad, diseño y tecnología de vanguardia.</p>

                            <h4>📞 Información de Contacto:</h4>
                            <p>📧 <strong>Email:</strong> <EMAIL><br>
                            📞 <strong>Teléfono:</strong> ************<br>
                            🌐 <strong>Sitios Web:</strong> soloylibre.com | josetusabe.com<br>
                            🏢 <strong>Empresa:</strong> SoloYLibre Web Dev</p>

                            <p><em>¡Dale paisano, que vamos a desarrollar algo brutal!</em> 🇩🇴</p>
                        </div>
                        <div class="post-footer">
                            <span class="location">📍 San José de Ocoa, República Dominicana 🇩🇴</span>
                        </div>
                    </article>
                    """

                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>🇩🇴 SoloYLibre WordPress - Desarrollo Web desde República Dominicana</title>
                    <meta name="description" content="Plataforma de desarrollo web profesional creada desde San José de Ocoa, República Dominicana por Jose L Encarnacion (JoseTusabe)">
                    <style>
                        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                        body {{
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                            line-height: 1.7;
                            color: #333;
                            background: #f8f9fa;
                        }}
                        .header {{
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                            color: white;
                            padding: 4rem 0;
                            text-align: center;
                            position: relative;
                            overflow: hidden;
                        }}
                        .header::before {{
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
                            opacity: 0.3;
                        }}
                        .header-content {{
                            position: relative;
                            z-index: 1;
                            max-width: 1200px;
                            margin: 0 auto;
                            padding: 0 2rem;
                        }}
                        .header h1 {{
                            font-size: 4rem;
                            margin-bottom: 1rem;
                            font-weight: 900;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                        }}
                        .header p {{
                            font-size: 1.4rem;
                            opacity: 0.95;
                            max-width: 600px;
                            margin: 0 auto;
                            font-weight: 500;
                        }}
                        .admin-bar {{
                            background: #23282d;
                            color: white;
                            padding: 1rem 0;
                            text-align: center;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }}
                        .admin-bar a {{
                            color: #0073aa;
                            text-decoration: none;
                            margin: 0 1.5rem;
                            padding: 0.5rem 1rem;
                            border-radius: 5px;
                            transition: all 0.3s ease;
                            font-weight: 600;
                        }}
                        .admin-bar a:hover {{
                            background: #0073aa;
                            color: white;
                        }}
                        .nav {{
                            background: white;
                            padding: 1.5rem 0;
                            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
                            position: sticky;
                            top: 0;
                            z-index: 100;
                        }}
                        .nav-container {{
                            max-width: 1200px;
                            margin: 0 auto;
                            display: flex;
                            justify-content: center;
                            gap: 3rem;
                            padding: 0 2rem;
                        }}
                        .nav a {{
                            text-decoration: none;
                            color: #333;
                            font-weight: 700;
                            padding: 1rem 1.5rem;
                            border-radius: 10px;
                            transition: all 0.3s ease;
                            font-size: 1.1rem;
                        }}
                        .nav a:hover {{
                            background: linear-gradient(45deg, #667eea, #764ba2);
                            color: white;
                            transform: translateY(-2px);
                        }}
                        .container {{
                            max-width: 1200px;
                            margin: 3rem auto;
                            padding: 0 2rem;
                        }}
                        .post {{
                            background: white;
                            padding: 3rem;
                            margin-bottom: 3rem;
                            border-radius: 20px;
                            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                            transition: all 0.3s ease;
                            border-left: 5px solid #667eea;
                        }}
                        .post:hover {{
                            transform: translateY(-5px);
                            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
                        }}
                        .post.featured {{
                            border-left: 5px solid #f093fb;
                            background: linear-gradient(135deg, rgba(240, 147, 251, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);
                        }}
                        .post-title {{
                            color: #333;
                            margin-bottom: 1.5rem;
                            font-size: 2.2rem;
                            font-weight: 800;
                            line-height: 1.3;
                        }}
                        .post-meta {{
                            display: flex;
                            gap: 2rem;
                            margin-bottom: 2rem;
                            color: #666;
                            font-size: 0.95rem;
                            font-weight: 600;
                        }}
                        .post-content {{
                            line-height: 1.8;
                            font-size: 1.1rem;
                        }}
                        .post-content h2, .post-content h3, .post-content h4 {{
                            color: #667eea;
                            margin: 2rem 0 1rem 0;
                            font-weight: 700;
                        }}
                        .post-content h2 {{ font-size: 1.8rem; }}
                        .post-content h3 {{ font-size: 1.5rem; }}
                        .post-content h4 {{ font-size: 1.3rem; }}
                        .post-content p {{
                            margin-bottom: 1.5rem;
                        }}
                        .post-content ul, .post-content ol {{
                            margin: 1.5rem 0;
                            padding-left: 2rem;
                        }}
                        .post-content li {{
                            margin-bottom: 0.8rem;
                        }}
                        .post-content strong {{
                            color: #667eea;
                            font-weight: 700;
                        }}
                        .post-content em {{
                            color: #764ba2;
                            font-style: italic;
                        }}
                        .post-footer {{
                            margin-top: 2rem;
                            padding-top: 1.5rem;
                            border-top: 2px solid #f1f1f1;
                            color: #666;
                            font-weight: 600;
                            text-align: center;
                        }}
                        .footer {{
                            background: linear-gradient(135deg, #23282d 0%, #32373c 100%);
                            color: white;
                            padding: 3rem 0;
                            text-align: center;
                            margin-top: 4rem;
                        }}
                        .footer-content {{
                            max-width: 1200px;
                            margin: 0 auto;
                            padding: 0 2rem;
                        }}
                        .footer h3 {{
                            font-size: 1.8rem;
                            margin-bottom: 1.5rem;
                            font-weight: 700;
                        }}
                        .footer p {{
                            margin: 0.8rem 0;
                            font-size: 1.1rem;
                            opacity: 0.9;
                        }}
                        .flag {{
                            font-size: 1.5rem;
                            margin: 0 8px;
                            display: inline-block;
                            animation: wave 3s ease-in-out infinite;
                        }}
                        @keyframes wave {{
                            0%, 100% {{ transform: rotate(0deg); }}
                            25% {{ transform: rotate(-10deg); }}
                            75% {{ transform: rotate(10deg); }}
                        }}
                        @media (max-width: 768px) {{
                            .header h1 {{ font-size: 2.5rem; }}
                            .header p {{ font-size: 1.2rem; }}
                            .nav-container {{
                                flex-direction: column;
                                gap: 1rem;
                                text-align: center;
                            }}
                            .container {{ padding: 0 1rem; }}
                            .post {{ padding: 2rem 1.5rem; }}
                            .post-title {{ font-size: 1.8rem; }}
                            .post-meta {{ flex-direction: column; gap: 0.5rem; }}
                        }}
                    </style>
                </head>
                <body>
                    <div class="admin-bar">
                        <a href="/wp-admin">🔧 Panel de Administración</a>
                        <a href="/wp-admin/post-new.php">✍️ Crear Nuevo Post</a>
                        <a href="/dashboard">📊 Dashboard</a>
                    </div>

                    <header class="header">
                        <div class="header-content">
                            <h1>🇩🇴 SoloYLibre WordPress</h1>
                            <p>Plataforma de desarrollo web profesional desde República Dominicana</p>
                        </div>
                    </header>

                    <nav class="nav">
                        <div class="nav-container">
                            <a href="/">🏠 Inicio</a>
                            <a href="#about">📋 Acerca de</a>
                            <a href="#contact">📧 Contacto</a>
                            <a href="/wp-admin">🔧 Administración</a>
                        </div>
                    </nav>

                    <div class="container">
                        {posts_html}
                    </div>

                    <footer class="footer">
                        <div class="footer-content">
                            <h3>👨‍💻 SoloYLibre Web Dev</h3>
                            <p><strong>Desarrollador:</strong> Jose L Encarnacion (JoseTusabe)</p>
                            <p><strong>Ubicación:</strong> <span class="flag">🇩🇴</span> San José de Ocoa, República Dominicana</p>
                            <p><strong>Email:</strong> <EMAIL> | <strong>Teléfono:</strong> ************</p>
                            <p><strong>Sitios Web:</strong> soloylibre.com | josetusabe.com | 1and1photo.com</p>
                            <p><em>¡Dale paisano, que vamos a desarrollar algo brutal!</em></p>
                        </div>
                    </footer>
                </body>
                </html>
                """

                self.wfile.write(html.encode('utf-8'))

        # Crear instancia del handler con referencia al servidor
        def handler_factory(*args, **kwargs):
            return WordPressHandler(*args, server_instance=self, **kwargs)

        os.chdir(self.wordpress_dir)
        with socketserver.TCPServer(("", self.port), handler_factory) as httpd:
            print(f"✅ Servidor WordPress COMPLETO corriendo en http://localhost:{self.port}")
            print("\n🔐 CREDENCIALES DE ACCESO TOTAL:")
            print("👤 Usuario: josetusabe")
            print("🔑 Contraseña: JoseTusabe2025!")
            print("📧 Email: <EMAIL>")
            print("\n🌐 URLS DISPONIBLES:")
            print(f"🏠 Frontend (Sitio Web): http://localhost:{self.port}")
            print(f"🔧 Admin Login: http://localhost:{self.port}/wp-admin")
            print(f"📊 Dashboard: http://localhost:{self.port}/dashboard")
            print(f"✍️ Crear Post: http://localhost:{self.port}/wp-admin/post-new.php")
            print(f"🚪 Cerrar Sesión: http://localhost:{self.port}/logout")
            print("\n🚀 FUNCIONALIDADES DISPONIBLES:")
            print("✅ Acceso completo al panel de administración")
            print("✅ Creación y gestión de posts")
            print("✅ Frontend completamente funcional")
            print("✅ Base de datos SQLite integrada")
            print("✅ Sistema de autenticación seguro")
            print("✅ Diseño responsive y profesional")
            print("\n🛑 Presiona Ctrl+C para detener el servidor")
            print("="*70)

            # Abrir navegador automáticamente en la página de login
            threading.Thread(target=lambda: (time.sleep(2), webbrowser.open(f'http://localhost:{self.port}/wp-admin')), daemon=True).start()

            try:
                httpd.serve_forever()
            except KeyboardInterrupt:
                print("\n🛑 Servidor detenido")
                print("¡Dale paisano, que estuvo brutal! 🇩🇴")
                print("WordPress completo funcionando perfectamente.")

if __name__ == '__main__':
    server = WordPressCompleteServer()
    server.start_server()
