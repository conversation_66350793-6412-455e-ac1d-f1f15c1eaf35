#!/bin/bash
# WordPress SoloYLibre Ultimate Final - Instalación Simple y Completa
# Desarrollado por Jose L Encarnacion (JoseTusabe)
# Desde <PERSON>, República Dominicana 🇩🇴

echo "🇩🇴 =============================================="
echo "🚀 WORDPRESS SOLOYLIBRE ULTIMATE FINAL"
echo "👨‍💻 Jose <PERSON>nac<PERSON> (JoseTusabe)"
echo "🏔️ San José de Ocoa, República Dominicana"
echo "📧 <EMAIL> | 📞 ************"
echo "🎯 Instalación Simple y Completa"
echo "=============================================="

# Variables
PROJECT_DIR="WordPress-SoloYLibre-Final-Simple"
SITE_URL="http://localhost:8080"

echo ""
echo "🧹 Limpiando instalaciones anteriores..."

# Detener servidores anteriores
pkill -f "php -S localhost:8080" 2>/dev/null || true
pkill -f "php -S localhost:8081" 2>/dev/null || true

# Limpiar directorios anteriores
rm -rf WordPress-SoloYLibre-* 2>/dev/null || true

echo "✅ Limpieza completada"

echo ""
echo "📁 Creando directorio del proyecto..."

mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

echo "✅ Directorio creado: $PROJECT_DIR"

echo ""
echo "⬇️ Descargando WordPress..."

# Descargar WordPress
curl -O https://wordpress.org/latest.tar.gz
if [ $? -ne 0 ]; then
    echo "❌ Error descargando WordPress"
    exit 1
fi

# Extraer WordPress
tar -xzf latest.tar.gz
mv wordpress/* .
rmdir wordpress
rm latest.tar.gz

echo "✅ WordPress descargado y extraído"

echo ""
echo "⚙️ Configurando WordPress..."

# Crear wp-config.php
cat > wp-config.php << 'EOF'
<?php
/**
 * WordPress SoloYLibre Ultimate Final - Configuración Simple
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 * Desde San José de Ocoa, República Dominicana 🇩🇴
 */

// ** Configuración de Base de Datos ** //
define( 'DB_NAME', 'soloylibre_final' );
define( 'DB_USER', 'root' );
define( 'DB_PASSWORD', '' );
define( 'DB_HOST', 'localhost' );
define( 'DB_CHARSET', 'utf8mb4' );
define( 'DB_COLLATE', '' );

// ** Claves de Seguridad ** //
define('AUTH_KEY',         'SoloYLibre-Auth-Key-Final-JoseTusabe-San-Jose-Ocoa');
define('SECURE_AUTH_KEY',  'SoloYLibre-Secure-Auth-Key-Final-Republica-Dominicana');
define('LOGGED_IN_KEY',    'SoloYLibre-Logged-In-Key-Final-JoseTusabe-Developer');
define('NONCE_KEY',        'SoloYLibre-Nonce-Key-Final-WordPress-Ultimate');
define('AUTH_SALT',        'SoloYLibre-Auth-Salt-Final-admin-soloylibre-com');
define('SECURE_AUTH_SALT', 'SoloYLibre-Secure-Auth-Salt-Final-************');
define('LOGGED_IN_SALT',   'SoloYLibre-Logged-In-Salt-Final-Synology-RS3618xs');
define('NONCE_SALT',       'SoloYLibre-Nonce-Salt-Final-56GB-RAM-36TB-Storage');

// ** Prefijo de Tablas ** //
$table_prefix = 'wp_';

// ** Configuración de WordPress ** //
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('WP_MEMORY_LIMIT', '512M');
define('ALLOW_UNFILTERED_UPLOADS', true);

// ** Información del Desarrollador ** //
define('SOLOYLIBRE_DEVELOPER', 'Jose L Encarnacion (JoseTusabe)');
define('SOLOYLIBRE_LOCATION', 'San José de Ocoa, República Dominicana');
define('SOLOYLIBRE_EMAIL', '<EMAIL>');
define('SOLOYLIBRE_PHONE', '************');

// ** WordPress Core ** //
if ( ! defined( 'ABSPATH' ) ) {
    define( 'ABSPATH', __DIR__ . '/' );
}
require_once ABSPATH . 'wp-settings.php';
EOF

echo "✅ wp-config.php creado"

echo ""
echo "🎨 Creando tema SoloYLibre Ultimate Final..."

# Crear directorio del tema
THEME_DIR="wp-content/themes/soloylibre-final"
mkdir -p "$THEME_DIR"

# style.css
cat > "$THEME_DIR/style.css" << 'EOF'
/*
Theme Name: SoloYLibre Ultimate Final
Description: Tema profesional desarrollado por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana 🇩🇴
Author: Jose L Encarnacion (JoseTusabe)
Author URI: https://soloylibre.com
Version: 1.0.0
*/

:root {
    --primary: #667eea;
    --secondary: #764ba2;
    --dominican-red: #ce1126;
    --dominican-blue: #002d62;
    --success: #22c55e;
}

* { margin: 0; padding: 0; box-sizing: border-box; }

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1a202c;
    line-height: 1.6;
}

.site-header {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    padding: 2rem 0;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.site-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 900;
    margin-bottom: 0.5rem;
}

.site-title a {
    color: inherit;
    text-decoration: none;
}

.site-description {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.developer-info {
    font-size: 0.9rem;
    opacity: 0.8;
}

.dominican-flag {
    font-size: 1.2rem;
    margin: 0 0.5rem;
}

.content-area {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.site-main {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.entry-title {
    color: var(--primary);
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.entry-title a {
    color: inherit;
    text-decoration: none;
}

.entry-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success), #16a34a);
    color: white;
}

.site-footer {
    background: #1a202c;
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

.success-notice {
    background: linear-gradient(135deg, var(--success), #16a34a);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin: 2rem 0;
    text-align: center;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.info-card {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 15px;
    border-left: 5px solid var(--primary);
}

@media (max-width: 768px) {
    .content-area {
        margin: 1rem auto;
        padding: 0 0.5rem;
    }
    
    .site-main {
        padding: 1.5rem;
    }
}
EOF

# index.php
cat > "$THEME_DIR/index.php" << 'EOF'
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php wp_title( '|', true, 'right' ); ?><?php bloginfo( 'name' ); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<header class="site-header">
    <div class="container">
        <h1 class="site-title">
            <a href="<?php echo esc_url( home_url( '/' ) ); ?>">
                <?php bloginfo( 'name' ); ?>
            </a>
        </h1>
        <p class="site-description"><?php bloginfo( 'description' ); ?></p>
        <div class="developer-info">
            <span class="dominican-flag">🇩🇴</span>
            Desarrollado por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana
            <span class="dominican-flag">🇩🇴</span>
        </div>
    </div>
</header>

<main class="content-area">
    <div class="site-main">
        <?php if ( have_posts() ) : ?>
            <?php while ( have_posts() ) : the_post(); ?>
                <article>
                    <h2 class="entry-title">
                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                    </h2>
                    <div class="entry-content">
                        <?php the_content(); ?>
                    </div>
                </article>
            <?php endwhile; ?>
        <?php else : ?>
            <article>
                <h2 class="entry-title">¡WordPress SoloYLibre Ultimate Final Listo!</h2>
                <div class="entry-content">
                    <div class="success-notice">
                        <h3>🎉 ¡Instalación Completada!</h3>
                        <p>WordPress SoloYLibre Ultimate Final está funcionando perfectamente</p>
                    </div>
                    
                    <p>🇩🇴 <strong>¡Dale paisano!</strong> Tu WordPress está completamente instalado.</p>
                    <p>Sistema desarrollado por <strong>Jose L Encarnacion (JoseTusabe)</strong> desde <strong>San José de Ocoa, República Dominicana</strong>.</p>
                    
                    <div class="info-grid">
                        <div class="info-card">
                            <h3>✅ Características</h3>
                            <ul>
                                <li>🎨 Tema SoloYLibre personalizado</li>
                                <li>🔧 Configuración optimizada</li>
                                <li>🛡️ Seguridad configurada</li>
                                <li>🇩🇴 Diseño dominicano</li>
                                <li>📱 Responsive design</li>
                            </ul>
                        </div>
                        
                        <div class="info-card">
                            <h3>🔐 Panel Admin</h3>
                            <p>Completa la instalación accediendo al panel:</p>
                            <p><a href="/wp-admin/install.php" class="btn btn-primary">🚀 Completar Instalación</a></p>
                            <p><strong>Datos recomendados:</strong></p>
                            <ul>
                                <li><strong>Usuario:</strong> josetusabe</li>
                                <li><strong>Email:</strong> <EMAIL></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 2rem; border-radius: 15px; text-align: center; margin: 2rem 0;">
                        <h3>🇩🇴 Desarrollado con Amor Dominicano</h3>
                        <p><strong>Jose L Encarnacion (JoseTusabe)</strong></p>
                        <p>📧 <EMAIL> | 📞 ************</p>
                        <p>🏔️ San José de Ocoa, República Dominicana 🇩🇴</p>
                        <p><strong>¡Que viva la República Dominicana!</strong></p>
                    </div>
                </div>
            </article>
        <?php endif; ?>
    </div>
</main>

<footer class="site-footer">
    <div class="container">
        <p>&copy; <?php echo date('Y'); ?> <?php bloginfo( 'name' ); ?>. Todos los derechos reservados.</p>
        <p>
            <span class="dominican-flag">🇩🇴</span>
            Desarrollado con ❤️ por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana
            <span class="dominican-flag">🇩🇴</span>
        </p>
    </div>
</footer>

<?php wp_footer(); ?>
</body>
</html>
EOF

# functions.php
cat > "$THEME_DIR/functions.php" << 'EOF'
<?php
/**
 * SoloYLibre Ultimate Final Theme Functions
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

function soloylibre_final_setup() {
    add_theme_support( 'title-tag' );
    add_theme_support( 'post-thumbnails' );
    add_theme_support( 'html5', array( 'search-form', 'comment-form', 'comment-list', 'gallery', 'caption' ) );
    add_theme_support( 'automatic-feed-links' );
}
add_action( 'after_setup_theme', 'soloylibre_final_setup' );

function soloylibre_final_scripts() {
    wp_enqueue_style( 'soloylibre-final-style', get_stylesheet_uri(), array(), '1.0.0' );
}
add_action( 'wp_enqueue_scripts', 'soloylibre_final_scripts' );

function soloylibre_final_admin_footer() {
    echo '<span>🇩🇴 Desarrollado por <strong>Jose L Encarnacion (JoseTusabe)</strong> desde San José de Ocoa, República Dominicana | 📧 <EMAIL></span>';
}
add_filter( 'admin_footer_text', 'soloylibre_final_admin_footer' );
?>
EOF

echo "✅ Tema SoloYLibre Ultimate Final creado"

echo ""
echo "🔒 Configurando permisos..."

# Configurar permisos
chmod -R 755 wp-content/
chmod 644 wp-config.php

echo "✅ Permisos configurados"

echo ""
echo "🚀 Iniciando servidor WordPress..."

# Iniciar servidor
php -S localhost:8080 &
SERVER_PID=$!

echo "✅ Servidor iniciado (PID: $SERVER_PID)"

# Esperar a que el servidor inicie
sleep 3

echo ""
echo "🇩🇴 =============================================="
echo "🎉 ¡WORDPRESS SOLOYLIBRE ULTIMATE FINAL LISTO!"
echo "=============================================="
echo ""
echo "🌐 ACCESO AL SITIO:"
echo "   URL: $SITE_URL"
echo "   Instalación: $SITE_URL/wp-admin/install.php"
echo ""
echo "🔐 DATOS RECOMENDADOS PARA LA INSTALACIÓN:"
echo "   Título del sitio: SoloYLibre WordPress Ultimate Final"
echo "   Usuario: josetusabe"
echo "   Contraseña: JoseTusabe2025!"
echo "   Email: <EMAIL>"
echo ""
echo "✅ CARACTERÍSTICAS INCLUIDAS:"
echo "   🎨 Tema SoloYLibre Ultimate Final"
echo "   🇩🇴 Diseño personalizado dominicano"
echo "   📱 Responsive design completo"
echo "   🛡️ Configuración de seguridad"
echo "   🚀 Optimización de rendimiento"
echo ""
echo "🇩🇴 DESARROLLADO DESDE SAN JOSÉ DE OCOA:"
echo "   👨‍💻 Jose L Encarnacion (JoseTusabe)"
echo "   📧 <EMAIL>"
echo "   📞 ************"
echo "   🖥️ Synology RS3618xs - 56GB RAM - 36TB"
echo ""
echo "🎯 PRÓXIMOS PASOS:"
echo "   1. 🌐 Ve a: $SITE_URL"
echo "   2. 🔧 Completa la instalación web"
echo "   3. 🎨 Activa el tema SoloYLibre Ultimate Final"
echo "   4. 📝 Crea tu primer contenido"
echo ""
echo "🛑 Presiona Ctrl+C para detener el servidor"
echo "=============================================="

# Abrir navegador automáticamente
if command -v open &> /dev/null; then
    echo "🌐 Abriendo navegador automáticamente..."
    open "$SITE_URL"
elif command -v xdg-open &> /dev/null; then
    echo "🌐 Abriendo navegador automáticamente..."
    xdg-open "$SITE_URL"
fi

# Mantener servidor corriendo
wait $SERVER_PID
