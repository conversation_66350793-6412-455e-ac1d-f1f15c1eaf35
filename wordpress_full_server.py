#!/usr/bin/env python3
"""
SoloYLibre WordPress Full Server
Servidor completo con acceso total a WordPress Admin y Frontend
Desarrollado por <PERSON> Encarnacion (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import os
import sys
import subprocess
import time
import webbrowser
import threading
import http.server
import socketserver
import sqlite3
import json
from urllib.parse import urlparse, parse_qs, unquote
from datetime import datetime

class WordPressFullServer:
    def __init__(self):
        self.port = 8080
        self.host = 'localhost'
        self.wordpress_dir = '/Users/<USER>/Desktop/SoloYLibre-WordPress'
        self.db_file = os.path.join(self.wordpress_dir, "soloylibre_wordpress.db")
        self.logged_in = False
        
    def print_header(self):
        print("🇩🇴 " + "="*70)
        print("🚀 SoloYLibre WordPress Full Server")
        print("👨‍💻 Desarrollado por Jose L Encarnacion (JoseTusabe)")
        print("🏔️ Desde San José de Ocoa, República Dominicana")
        print("📧 <EMAIL> | 📞 718-713-5500")
        print("="*70)
        
    def get_posts_from_db(self):
        """Obtener posts de la base de datos"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT ID, post_title, post_content, post_date, post_status 
                FROM wp_posts 
                WHERE post_type = 'post' AND post_status = 'publish'
                ORDER BY post_date DESC
            """)
            posts = cursor.fetchall()
            conn.close()
            return posts
        except:
            return []
            
    def create_post(self, title, content):
        """Crear nuevo post"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO wp_posts 
                (post_author, post_date, post_date_gmt, post_content, post_title, 
                 post_status, post_name, post_type, to_ping, pinged, post_content_filtered)
                VALUES (1, datetime('now'), datetime('now'), ?, ?, 'publish', ?, 'post', '', '', '')
            """, (content, title, title.lower().replace(' ', '-')))
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Error creating post: {e}")
            return False
            
    def start_server(self):
        """Iniciar servidor completo"""
        self.print_header()
        
        class WordPressHandler(http.server.SimpleHTTPRequestHandler):
            def __init__(self, *args, **kwargs):
                self.server_instance = kwargs.pop('server_instance', None)
                super().__init__(*args, directory='/Users/<USER>/Desktop/SoloYLibre-WordPress', **kwargs)
                
            def do_GET(self):
                if self.path == '/' or self.path == '/index.php':
                    self.serve_homepage()
                elif self.path == '/wp-admin' or self.path == '/wp-admin/':
                    self.serve_login()
                elif self.path == '/wp-admin/index.php' or self.path == '/dashboard':
                    if self.server_instance.logged_in:
                        self.serve_dashboard()
                    else:
                        self.serve_login()
                elif self.path == '/wp-admin/post-new.php':
                    if self.server_instance.logged_in:
                        self.serve_new_post()
                    else:
                        self.serve_login()
                elif self.path.startswith('/wp-content'):
                    super().do_GET()
                else:
                    super().do_GET()
                    
            def do_POST(self):
                if self.path == '/wp-admin' or self.path == '/wp-login.php':
                    self.handle_login()
                elif self.path == '/wp-admin/post-new.php':
                    self.handle_new_post()
                else:
                    self.send_response(404)
                    self.end_headers()
                    
            def handle_login(self):
                """Manejar login"""
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length).decode('utf-8')
                params = parse_qs(post_data)
                
                username = params.get('log', [''])[0]
                password = params.get('pwd', [''])[0]
                
                if username == 'josetusabe' and password == 'JoseTusabe2025!':
                    self.server_instance.logged_in = True
                    self.send_response(302)
                    self.send_header('Location', '/dashboard')
                    self.end_headers()
                else:
                    self.serve_login(error="Credenciales incorrectas")
                    
            def handle_new_post(self):
                """Manejar creación de nuevo post"""
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length).decode('utf-8')
                params = parse_qs(post_data)
                
                title = params.get('post_title', [''])[0]
                content = params.get('content', [''])[0]
                
                if title and content:
                    if self.server_instance.create_post(title, content):
                        self.send_response(302)
                        self.send_header('Location', '/dashboard?success=1')
                        self.end_headers()
                        return
                
                self.serve_new_post(error="Error creando el post")
                
            def serve_login(self, error=""):
                """Servir página de login personalizada"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                
                error_html = f'<div class="error-message">{error}</div>' if error else ''
                
                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>🇩🇴 SoloYLibre WordPress - Acceso</title>
                    <style>
                        * {{
                            margin: 0;
                            padding: 0;
                            box-sizing: border-box;
                        }}
                        
                        body {{
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            min-height: 100vh;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding: 20px;
                        }}
                        
                        .login-container {{
                            background: rgba(255, 255, 255, 0.95);
                            backdrop-filter: blur(20px);
                            border-radius: 20px;
                            padding: 40px;
                            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                            width: 100%;
                            max-width: 400px;
                            border: 1px solid rgba(255, 255, 255, 0.2);
                        }}
                        
                        .logo {{
                            text-align: center;
                            margin-bottom: 30px;
                        }}
                        
                        .logo h1 {{
                            font-size: 2.5rem;
                            background: linear-gradient(45deg, #667eea, #764ba2);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            background-clip: text;
                            margin-bottom: 10px;
                        }}
                        
                        .logo p {{
                            color: #666;
                            font-size: 0.9rem;
                        }}
                        
                        .form-group {{
                            margin-bottom: 20px;
                        }}
                        
                        .form-group label {{
                            display: block;
                            margin-bottom: 8px;
                            font-weight: 600;
                            color: #333;
                        }}
                        
                        .form-group input {{
                            width: 100%;
                            padding: 15px;
                            border: 2px solid #e1e5e9;
                            border-radius: 10px;
                            font-size: 16px;
                            transition: all 0.3s ease;
                            background: rgba(255, 255, 255, 0.8);
                        }}
                        
                        .form-group input:focus {{
                            outline: none;
                            border-color: #667eea;
                            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                        }}
                        
                        .login-btn {{
                            width: 100%;
                            padding: 15px;
                            background: linear-gradient(45deg, #667eea, #764ba2);
                            color: white;
                            border: none;
                            border-radius: 10px;
                            font-size: 16px;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            margin-top: 10px;
                        }}
                        
                        .login-btn:hover {{
                            transform: translateY(-2px);
                            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
                        }}
                        
                        .credentials-info {{
                            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
                            color: white;
                            padding: 20px;
                            border-radius: 15px;
                            margin: 20px 0;
                            text-align: center;
                        }}
                        
                        .credentials-info h3 {{
                            margin-bottom: 15px;
                            font-size: 1.1rem;
                        }}
                        
                        .credential-item {{
                            margin: 8px 0;
                            font-size: 0.9rem;
                        }}
                        
                        .error-message {{
                            background: #ff6b6b;
                            color: white;
                            padding: 15px;
                            border-radius: 10px;
                            margin-bottom: 20px;
                            text-align: center;
                            font-weight: 500;
                        }}
                        
                        .developer-info {{
                            text-align: center;
                            margin-top: 30px;
                            padding-top: 20px;
                            border-top: 1px solid #e1e5e9;
                            color: #666;
                            font-size: 0.85rem;
                        }}
                        
                        .flag {{
                            font-size: 1.2rem;
                            margin: 0 5px;
                        }}
                        
                        @media (max-width: 480px) {{
                            .login-container {{
                                padding: 30px 20px;
                            }}
                            
                            .logo h1 {{
                                font-size: 2rem;
                            }}
                        }}
                    </style>
                </head>
                <body>
                    <div class="login-container">
                        <div class="logo">
                            <h1>🇩🇴 SoloYLibre</h1>
                            <p>WordPress Admin Access</p>
                        </div>
                        
                        {error_html}
                        
                        <form method="POST" action="/wp-admin">
                            <div class="form-group">
                                <label for="username">👤 Usuario</label>
                                <input type="text" id="username" name="log" value="josetusabe" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="password">🔑 Contraseña</label>
                                <input type="password" id="password" name="pwd" value="JoseTusabe2025!" required>
                            </div>
                            
                            <button type="submit" class="login-btn">
                                🚀 Acceder al Panel
                            </button>
                        </form>
                        
                        <div class="credentials-info">
                            <h3>🔐 Credenciales de Acceso</h3>
                            <div class="credential-item"><strong>Usuario:</strong> josetusabe</div>
                            <div class="credential-item"><strong>Contraseña:</strong> JoseTusabe2025!</div>
                            <div class="credential-item"><strong>Email:</strong> <EMAIL></div>
                        </div>
                        
                        <div class="developer-info">
                            <p><strong>Desarrollado por:</strong> Jose L Encarnacion (JoseTusabe)</p>
                            <p><span class="flag">🇩🇴</span> San José de Ocoa, República Dominicana</p>
                            <p>📧 <EMAIL> | 📞 718-713-5500</p>
                        </div>
                    </div>
                </body>
                </html>
                """
                
                self.wfile.write(html.encode('utf-8'))
                
            def serve_homepage(self):
                """Servir página principal"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                
                posts = self.server_instance.get_posts_from_db()
                posts_html = ""
                
                for post in posts:
                    posts_html += f"""
                    <article class="post">
                        <h2>{post[1]}</h2>
                        <div class="post-meta">📅 {post[3]}</div>
                        <div class="post-content">{post[2]}</div>
                    </article>
                    """
                
                if not posts_html:
                    posts_html = """
                    <article class="post">
                        <h2>¡Bienvenido a SoloYLibre WordPress!</h2>
                        <div class="post-content">
                            <p>🇩🇴 Desarrollado desde San José de Ocoa, República Dominicana</p>
                            <p>👨‍💻 Por Jose L Encarnacion (JoseTusabe)</p>
                            <p>📧 <EMAIL> | 📞 718-713-5500</p>
                            <p>¡Dale paisano, que vamos a desarrollar algo brutal!</p>
                        </div>
                    </article>
                    """
                
                html = f"""
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>🇩🇴 SoloYLibre WordPress</title>
                    <style>
                        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                        body {{
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            line-height: 1.6;
                            color: #333;
                            background: #f8f9fa;
                        }}
                        .header {{
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 2rem 0;
                            text-align: center;
                        }}
                        .header h1 {{ font-size: 3rem; margin-bottom: 0.5rem; }}
                        .header p {{ font-size: 1.2rem; opacity: 0.9; }}
                        .nav {{
                            background: white;
                            padding: 1rem 0;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }}
                        .nav-container {{
                            max-width: 1200px;
                            margin: 0 auto;
                            display: flex;
                            justify-content: center;
                            gap: 2rem;
                        }}
                        .nav a {{
                            text-decoration: none;
                            color: #333;
                            font-weight: 600;
                            padding: 0.5rem 1rem;
                            border-radius: 5px;
                            transition: all 0.3s ease;
                        }}
                        .nav a:hover {{
                            background: #667eea;
                            color: white;
                        }}
                        .container {{
                            max-width: 1200px;
                            margin: 2rem auto;
                            padding: 0 2rem;
                        }}
                        .post {{
                            background: white;
                            padding: 2rem;
                            margin-bottom: 2rem;
                            border-radius: 10px;
                            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                        }}
                        .post h2 {{
                            color: #667eea;
                            margin-bottom: 1rem;
                        }}
                        .post-meta {{
                            color: #666;
                            font-size: 0.9rem;
                            margin-bottom: 1rem;
                        }}
                        .post-content {{
                            line-height: 1.8;
                        }}
                        .admin-bar {{
                            background: #23282d;
                            color: white;
                            padding: 0.5rem 0;
                            text-align: center;
                        }}
                        .admin-bar a {{
                            color: #0073aa;
                            text-decoration: none;
                            margin: 0 1rem;
                        }}
                    </style>
                </head>
                <body>
                    <div class="admin-bar">
                        <a href="/wp-admin">🔧 Panel de Administración</a>
                        <a href="/wp-admin/post-new.php">✍️ Nuevo Post</a>
                    </div>
                    
                    <header class="header">
                        <h1>🇩🇴 SoloYLibre WordPress</h1>
                        <p>Desarrollo web profesional desde República Dominicana</p>
                    </header>
                    
                    <nav class="nav">
                        <div class="nav-container">
                            <a href="/">🏠 Inicio</a>
                            <a href="/about">📋 Acerca de</a>
                            <a href="/contact">📧 Contacto</a>
                            <a href="/wp-admin">🔧 Admin</a>
                        </div>
                    </nav>
                    
                    <div class="container">
                        {posts_html}
                    </div>
                </body>
                </html>
                """
                
                self.wfile.write(html.encode('utf-8'))
