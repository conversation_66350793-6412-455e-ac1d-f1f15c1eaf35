#!/usr/bin/env python3
"""
SoloYLibre WordPress Multisite Installer
Instalador automático completo de WordPress con MySQL
Desarrollado por Jose L Encarnacion (JoseTusabe)
Desde San José de Ocoa, República Dominicana 🇩🇴
"""

import subprocess
import os
import sys
import time
import requests
import shutil
from pathlib import Path

class SoloYLibreWordPressInstaller:
    def __init__(self):
        self.wordpress_dir = '/Users/<USER>/Desktop/SoloYLibre-WordPress'
        self.db_name = 'soloylibre_wordpress'
        self.db_user = 'admin_soloylibre'
        self.db_password = 'JoseTusabe2025!SanJoseDeOcoa'
        self.wp_user = 'josetusabe'
        self.wp_password = 'JoseTusabe2025!'
        self.wp_email = '<EMAIL>'
        self.site_title = 'SoloYLibre WordPress Multisite'
        self.site_url = 'http://localhost:8090'
        
    def print_header(self):
        """Print installation header"""
        print("=" * 80)
        print("🇩🇴 SOLOYLIBRE WORDPRESS MULTISITE INSTALLER")
        print("=" * 80)
        print("👨‍💻 Desarrollado por: Jose L Encarnacion (JoseTusabe)")
        print("🏔️ Desde: San José de Ocoa, República Dominicana")
        print("📧 Email: <EMAIL>")
        print("📱 Teléfono: ************")
        print("🌐 Empresa: SoloYLibre Web Dev")
        print("=" * 80)
        print("")
        
    def check_requirements(self):
        """Check system requirements"""
        print("🔍 Verificando requisitos del sistema...")
        
        requirements = {
            'php': 'PHP 7.4+',
            'mysql': 'MySQL 5.7+ o MariaDB',
            'curl': 'cURL',
            'git': 'Git'
        }
        
        missing = []
        
        for cmd, desc in requirements.items():
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"✅ {desc}: Instalado")
                else:
                    missing.append((cmd, desc))
                    print(f"❌ {desc}: No encontrado")
            except (subprocess.TimeoutExpired, FileNotFoundError):
                missing.append((cmd, desc))
                print(f"❌ {desc}: No encontrado")
        
        return missing
        
    def install_homebrew(self):
        """Install Homebrew if not present"""
        print("🍺 Instalando Homebrew...")
        try:
            subprocess.run(['brew', '--version'], check=True, capture_output=True)
            print("✅ Homebrew ya está instalado")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("📦 Instalando Homebrew...")
            install_cmd = '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"'
            result = subprocess.run(install_cmd, shell=True)
            return result.returncode == 0
            
    def install_requirements(self, missing):
        """Install missing requirements"""
        if not missing:
            return True
            
        print("📦 Instalando dependencias faltantes...")
        
        # Install Homebrew first
        if not self.install_homebrew():
            print("❌ Error instalando Homebrew")
            return False
        
        # Install missing packages
        for cmd, desc in missing:
            print(f"📦 Instalando {desc}...")
            
            if cmd == 'php':
                subprocess.run(['brew', 'install', 'php@8.1'])
            elif cmd == 'mysql':
                subprocess.run(['brew', 'install', 'mysql'])
                subprocess.run(['brew', 'services', 'start', 'mysql'])
            elif cmd == 'curl':
                subprocess.run(['brew', 'install', 'curl'])
            elif cmd == 'git':
                subprocess.run(['brew', 'install', 'git'])
        
        return True
        
    def setup_mysql(self):
        """Setup MySQL database"""
        print("🗄️ Configurando base de datos MySQL...")
        
        # Start MySQL service
        try:
            subprocess.run(['brew', 'services', 'start', 'mysql'], check=True)
            time.sleep(5)  # Wait for MySQL to start
        except subprocess.CalledProcessError:
            print("⚠️ MySQL ya está corriendo o no se pudo iniciar")
        
        # Create database and user
        mysql_commands = [
            f"CREATE DATABASE IF NOT EXISTS {self.db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;",
            f"CREATE USER IF NOT EXISTS '{self.db_user}'@'localhost' IDENTIFIED BY '{self.db_password}';",
            f"GRANT ALL PRIVILEGES ON {self.db_name}.* TO '{self.db_user}'@'localhost';",
            "FLUSH PRIVILEGES;"
        ]
        
        for cmd in mysql_commands:
            try:
                subprocess.run([
                    'mysql', '-u', 'root', '-e', cmd
                ], check=True, capture_output=True)
                print(f"✅ Ejecutado: {cmd[:50]}...")
            except subprocess.CalledProcessError as e:
                print(f"⚠️ Comando MySQL falló: {cmd[:50]}...")
                # Continue anyway, might already exist
        
        print("✅ Base de datos MySQL configurada")
        return True
        
    def download_wordpress(self):
        """Download and extract WordPress"""
        print("📥 Descargando WordPress...")
        
        # Create directory
        os.makedirs(self.wordpress_dir, exist_ok=True)
        os.chdir(self.wordpress_dir)
        
        # Download WordPress
        wp_url = "https://wordpress.org/latest.tar.gz"
        try:
            subprocess.run(['curl', '-O', wp_url], check=True)
            subprocess.run(['tar', '-xzf', 'latest.tar.gz'], check=True)
            
            # Move files
            for item in os.listdir('wordpress'):
                shutil.move(f'wordpress/{item}', '.')
            
            os.rmdir('wordpress')
            os.remove('latest.tar.gz')
            
            print("✅ WordPress descargado y extraído")
            return True
            
        except subprocess.CalledProcessError:
            print("❌ Error descargando WordPress")
            return False
            
    def configure_wordpress(self):
        """Configure WordPress"""
        print("⚙️ Configurando WordPress...")
        
        # Create wp-config.php
        wp_config = f"""<?php
/**
 * SoloYLibre WordPress Multisite Configuration
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 * Desde San José de Ocoa, República Dominicana 🇩🇴
 */

// ** Configuración de Base de Datos ** //
define('DB_NAME', '{self.db_name}');
define('DB_USER', '{self.db_user}');
define('DB_PASSWORD', '{self.db_password}');
define('DB_HOST', 'localhost');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// ** Configuración Multisite ** //
define('WP_ALLOW_MULTISITE', true);
define('MULTISITE', true);
define('SUBDOMAIN_INSTALL', false);
define('DOMAIN_CURRENT_SITE', 'localhost');
define('PATH_CURRENT_SITE', '/');
define('SITE_ID_CURRENT_SITE', 1);
define('BLOG_ID_CURRENT_SITE', 1);

// ** Claves de Seguridad Dominicanas ** //
define('AUTH_KEY',         'SanJoseDeOcoa2025!SoloYLibreWebDev#JoseTusabe$RepublicaDominicana');
define('SECURE_AUTH_KEY',  'MontagnasDominicanas!TecnologiaRD#InnovacionCaribe$OrgulloDominicano');
define('LOGGED_IN_KEY',    'KlkManito!DaleQueVamos#TechDominicano$SoloYLibreAI2025');
define('NONCE_KEY',        'VacanoTigueraje!BrutalPaisano#TecnologiaOcoa$DominicanPride');
define('AUTH_SALT',        'MerengueBachata!ColmadoSancocho#PlátanoYuca$MalecónPlaya');
define('SECURE_AUTH_SALT', 'CaribeTech!InnovaciónRD#SoloYLibreDev$JoseTusabeGenius');
define('LOGGED_IN_SALT',   'OcoaMountains!DominicanSpirit#TechInnovation$CaribbeanPower');
define('NONCE_SALT',       'RepublicaDominicana!SanJoseDeOcoa#SoloYLibreEcosystem$TechPride');

// ** Configuración de Desarrollo ** //
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);

// ** Configuración de Memoria ** //
define('WP_MEMORY_LIMIT', '512M');
define('MAX_EXECUTION_TIME', 300);

// ** URLs ** //
define('WP_HOME', '{self.site_url}');
define('WP_SITEURL', '{self.site_url}');

// ** Configuración SoloYLibre ** //
define('SOLOYLIBRE_DEVELOPER', 'Jose L Encarnacion (JoseTusabe)');
define('SOLOYLIBRE_LOCATION', 'San José de Ocoa, República Dominicana');
define('SOLOYLIBRE_EMAIL', '{self.wp_email}');
define('SOLOYLIBRE_PHONE', '************');

// ** Tabla de WordPress ** //
$table_prefix = 'sl_';

// ** Configuración de Red Multisite ** //
define('COOKIE_DOMAIN', '.localhost');
define('SUNRISE', 'on');

if ( !defined('ABSPATH') )
    define('ABSPATH', dirname(__FILE__) . '/');

require_once(ABSPATH . 'wp-settings.php');
"""
        
        with open('wp-config.php', 'w') as f:
            f.write(wp_config)
        
        print("✅ wp-config.php creado")
        return True
        
    def install_wordpress(self):
        """Install WordPress via WP-CLI"""
        print("🚀 Instalando WordPress...")
        
        # Download WP-CLI
        try:
            subprocess.run(['curl', '-O', 'https://raw.githubusercontent.com/wp-cli/wp-cli/v2.8.1/phar/wp-cli.phar'], check=True)
            subprocess.run(['chmod', '+x', 'wp-cli.phar'], check=True)
            subprocess.run(['sudo', 'mv', 'wp-cli.phar', '/usr/local/bin/wp'], check=True)
        except subprocess.CalledProcessError:
            print("⚠️ WP-CLI ya está instalado o error en instalación")
        
        # Install WordPress
        wp_commands = [
            ['wp', 'core', 'install',
             '--url=' + self.site_url,
             '--title=' + self.site_title,
             '--admin_user=' + self.wp_user,
             '--admin_password=' + self.wp_password,
             '--admin_email=' + self.wp_email,
             '--allow-root'],
            
            ['wp', 'core', 'multisite-convert',
             '--title=' + self.site_title,
             '--allow-root']
        ]
        
        for cmd in wp_commands:
            try:
                subprocess.run(cmd, check=True, cwd=self.wordpress_dir)
                print(f"✅ Ejecutado: {' '.join(cmd[:3])}...")
            except subprocess.CalledProcessError:
                print(f"⚠️ Error en comando: {' '.join(cmd[:3])}...")
        
        return True
        
    def start_server(self):
        """Start PHP development server"""
        print("🌐 Iniciando servidor PHP...")
        
        os.chdir(self.wordpress_dir)
        
        print(f"�� Servidor iniciado en: {self.site_url}")
        print("🔐 Credenciales de acceso:")
        print(f"   URL: {self.site_url}/wp-admin")
        print(f"   Usuario: {self.wp_user}")
        print(f"   Contraseña: {self.wp_password}")
        print(f"   Email: {self.wp_email}")
        print("")
        print("🛑 Presiona Ctrl+C para detener")
        
        try:
            subprocess.run(['php', '-S', 'localhost:8090'])
        except KeyboardInterrupt:
            print("\n🛑 Servidor detenido")
            
    def run_installation(self):
        """Run complete installation"""
        self.print_header()
        
        print("🚀 Iniciando instalación de WordPress Multisite...")
        print("")
        
        # Check requirements
        missing = self.check_requirements()
        
        if missing:
            print("📦 Instalando dependencias faltantes...")
            if not self.install_requirements(missing):
                print("❌ Error instalando dependencias")
                return False
        
        # Setup MySQL
        if not self.setup_mysql():
            print("❌ Error configurando MySQL")
            return False
        
        # Download WordPress
        if not self.download_wordpress():
            print("❌ Error descargando WordPress")
            return False
        
        # Configure WordPress
        if not self.configure_wordpress():
            print("❌ Error configurando WordPress")
            return False
        
        # Install WordPress
        if not self.install_wordpress():
            print("❌ Error instalando WordPress")
            return False
        
        print("")
        print("🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE! 🇩🇴")
        print("=" * 80)
        print(f"🌐 URL: {self.site_url}")
        print(f"🔐 Usuario: {self.wp_user}")
        print(f"🔑 Contraseña: {self.wp_password}")
        print(f"📧 Email: {self.wp_email}")
        print("=" * 80)
        print("¡Dale paisano, que WordPress está más que brutal!")
        print("")
        
        # Start server
        self.start_server()
        
        return True

if __name__ == '__main__':
    installer = SoloYLibreWordPressInstaller()
    installer.run_installation()
