#!/bin/bash
# SoloYLibre WordPress Ultimate COMPLETO - 100% Funcionalidades + Plugins Automáticos
# Desarrollado por Jose <PERSON>carnacion (JoseTusabe)
# Desde San José <PERSON>, República Dominicana 🇩🇴

echo "🇩🇴 =============================================="
echo "🚀 SOLOYLIBRE WORDPRESS ULTIMATE COMPLETO"
echo "👨‍💻 Jose L Encarnacion (JoseTusabe)"
echo "🏔️ San José de Ocoa, República Dominicana"
echo "📧 <EMAIL> | 📞 ************"
echo "🎯 100% Funcionalidades + Plugins Automáticos"
echo "=============================================="

# Variables de configuración
PROJECT_DIR="SoloYLibre-WordPress-Ultimate-Completo"
DB_NAME="soloylibre_ultimate"
DB_USER="soloylibre_admin"
DB_PASS="SoloYLibre2025!"
WP_ADMIN_USER="josetusabe"
WP_ADMIN_PASS="JoseTusabe2025!"
WP_ADMIN_EMAIL="<EMAIL>"
SITE_TITLE="SoloYLibre WordPress Ultimate Completo"
SITE_URL="http://localhost:8080"

# Función para mostrar progreso
show_progress() {
    echo ""
    echo "🔄 $1..."
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# Función para mostrar éxito
show_success() {
    echo "✅ $1"
    echo ""
}

# Función para mostrar error
show_error() {
    echo "❌ $1"
    exit 1
}

# Verificar y instalar dependencias
install_dependencies() {
    show_progress "Verificando e instalando dependencias"
    
    # Verificar PHP
    if ! command -v php &> /dev/null; then
        echo "📦 Instalando PHP..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install php
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get update
            sudo apt-get install -y php php-mysql php-curl php-gd php-mbstring php-xml php-zip php-intl php-bcmath
        fi
    fi
    
    # Verificar MySQL
    if ! command -v mysql &> /dev/null; then
        echo "📦 Instalando MySQL..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install mysql
            brew services start mysql
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get install -y mysql-server
            sudo systemctl start mysql
            sudo systemctl enable mysql
        fi
    fi
    
    # Verificar WP-CLI
    if ! command -v wp &> /dev/null; then
        echo "📦 Instalando WP-CLI..."
        curl -O https://raw.githubusercontent.com/wp-cli/wp-cli/v2.8.1/utils/wp-cli.phar
        chmod +x wp-cli.phar
        sudo mv wp-cli.phar /usr/local/bin/wp
    fi
    
    show_success "Dependencias instaladas"
}

# Crear directorio del proyecto
create_project_directory() {
    show_progress "Creando directorio del proyecto"
    
    if [ -d "$PROJECT_DIR" ]; then
        echo "⚠️ El directorio $PROJECT_DIR ya existe. ¿Deseas eliminarlo? (y/n)"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            rm -rf "$PROJECT_DIR"
        else
            show_error "Instalación cancelada"
        fi
    fi
    
    mkdir -p "$PROJECT_DIR"
    cd "$PROJECT_DIR"
    
    show_success "Directorio creado: $PROJECT_DIR"
}

# Configurar base de datos
setup_database() {
    show_progress "Configurando base de datos MySQL"
    
    # Script SQL para configuración completa
    mysql -u root -p << EOF
DROP DATABASE IF EXISTS $DB_NAME;
DROP USER IF EXISTS '$DB_USER'@'localhost';

CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF

    if [ $? -eq 0 ]; then
        show_success "Base de datos configurada"
    else
        show_error "Error configurando base de datos"
    fi
}

# Descargar e instalar WordPress
install_wordpress() {
    show_progress "Descargando e instalando WordPress"
    
    # Descargar WordPress con WP-CLI
    wp core download --locale=es_ES
    
    # Crear wp-config.php
    wp config create \
        --dbname="$DB_NAME" \
        --dbuser="$DB_USER" \
        --dbpass="$DB_PASS" \
        --dbhost="localhost" \
        --dbcharset="utf8mb4" \
        --extra-php << 'EOF'
// ** Configuración SoloYLibre Ultimate ** //
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);
define('WP_MEMORY_LIMIT', '512M');
define('ALLOW_UNFILTERED_UPLOADS', true);
define('WP_POST_REVISIONS', 10);
define('AUTOSAVE_INTERVAL', 60);
define('WP_CACHE', true);

// ** Información del Desarrollador ** //
define('SOLOYLIBRE_DEVELOPER', 'Jose L Encarnacion (JoseTusabe)');
define('SOLOYLIBRE_LOCATION', 'San José de Ocoa, República Dominicana');
define('SOLOYLIBRE_EMAIL', '<EMAIL>');
define('SOLOYLIBRE_PHONE', '************');

// ** Configuración Multisite ** //
define('WP_ALLOW_MULTISITE', true);

// ** Configuración de Seguridad Avanzada ** //
define('DISALLOW_FILE_EDIT', false); // Permitir edición para desarrollo
define('WP_AUTO_UPDATE_CORE', true);
define('AUTOMATIC_UPDATER_DISABLED', false);
EOF

    # Instalar WordPress
    wp core install \
        --url="$SITE_URL" \
        --title="$SITE_TITLE" \
        --admin_user="$WP_ADMIN_USER" \
        --admin_password="$WP_ADMIN_PASS" \
        --admin_email="$WP_ADMIN_EMAIL" \
        --skip-email
    
    show_success "WordPress instalado"
}

# Instalar plugins esenciales automáticamente
install_essential_plugins() {
    show_progress "Instalando plugins esenciales automáticamente"
    
    # Lista de plugins esenciales
    PLUGINS=(
        # Seguridad
        "wordfence"
        "sucuri-scanner"
        "ithemes-security-pro"
        
        # SEO
        "wordpress-seo"
        "rankmath"
        "all-in-one-seo-pack"
        
        # Rendimiento
        "wp-rocket"
        "w3-total-cache"
        "autoptimize"
        "wp-super-cache"
        
        # Backup
        "updraftplus"
        "backwpup"
        "duplicator"
        
        # Formularios
        "contact-form-7"
        "wpforms-lite"
        "ninja-forms"
        
        # E-commerce
        "woocommerce"
        "woocommerce-gateway-stripe"
        "woocommerce-paypal-payments"
        
        # Multisite
        "wp-multisite-user-sync"
        "multisite-plugin-manager"
        
        # Desarrollo
        "query-monitor"
        "debug-bar"
        "wp-crontrol"
        
        # Contenido
        "classic-editor"
        "gutenberg"
        "elementor"
        "beaver-builder-lite-version"
        
        # Medios
        "smush"
        "wp-optimize"
        "regenerate-thumbnails"
        
        # Social
        "social-warfare"
        "instagram-feed"
        "custom-facebook-feed"
        
        # Analytics
        "google-analytics-for-wordpress"
        "monster-insights"
        
        # Utilidades
        "wp-mail-smtp"
        "redirection"
        "broken-link-checker"
        "wp-maintenance-mode"
    )
    
    # Instalar cada plugin
    for plugin in "${PLUGINS[@]}"; do
        echo "📦 Instalando plugin: $plugin"
        wp plugin install "$plugin" --activate || echo "⚠️ No se pudo instalar: $plugin"
    done
    
    show_success "Plugins esenciales instalados"
}

# Crear tema personalizado SoloYLibre Ultimate
create_custom_theme() {
    show_progress "Creando tema personalizado SoloYLibre Ultimate"
    
    THEME_DIR="wp-content/themes/soloylibre-ultimate-completo"
    mkdir -p "$THEME_DIR"
    
    # style.css
    cat > "$THEME_DIR/style.css" << 'EOF'
/*
Theme Name: SoloYLibre Ultimate Completo
Description: Tema profesional completo desarrollado por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana 🇩🇴. Incluye todas las funcionalidades de WordPress y mejoras automáticas.
Author: Jose L Encarnacion (JoseTusabe)
Author URI: https://soloylibre.com
Version: 2.0.0
License: GPL v2 or later
Text Domain: soloylibre-ultimate-completo
Tags: responsive, custom-header, custom-menu, featured-images, threaded-comments, translation-ready, dominican, professional
*/

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --dominican-red: #ce1126;
    --dominican-blue: #002d62;
    --dominican-white: #ffffff;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--light-color) 0%, #e2e8f0 100%);
    color: var(--dark-color);
    line-height: 1.6;
    font-size: 16px;
}

/* Header Styles */
.site-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--dominican-white);
    padding: 2rem 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.site-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
    text-align: center;
}

.site-title {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 900;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
}

.site-description {
    font-size: clamp(1rem, 3vw, 1.3rem);
    opacity: 0.9;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.developer-info {
    font-size: 0.9rem;
    opacity: 0.8;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.dominican-flag {
    font-size: 1.2rem;
    margin: 0 0.5rem;
    animation: wave 2s ease-in-out infinite;
}

/* Navigation */
.main-navigation {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    margin-top: 1.5rem;
    border-radius: 50px;
}

.nav-menu {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.nav-menu a {
    color: var(--dominican-white);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.nav-menu a:hover::before {
    left: 100%;
}

.nav-menu a:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Content Area */
.content-area {
    max-width: 1200px;
    margin: 3rem auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.site-main {
    background: var(--dominican-white);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.site-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--dominican-red), var(--dominican-blue), var(--dominican-red));
}

/* Sidebar */
.widget-area {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.widget {
    background: var(--dominican-white);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.widget:hover {
    transform: translateY(-5px);
}

.widget-title {
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

/* Posts */
.entry-title {
    color: var(--primary-color);
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.entry-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.entry-title a:hover {
    color: var(--secondary-color);
}

.entry-meta {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.entry-meta span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.entry-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #374151;
}

.entry-content p {
    margin-bottom: 1.5rem;
}

.entry-content h2,
.entry-content h3,
.entry-content h4 {
    color: var(--primary-color);
    margin: 2rem 0 1rem 0;
    font-weight: 700;
}

.entry-content ul,
.entry-content ol {
    margin: 1.5rem 0;
    padding-left: 2rem;
}

.entry-content li {
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--dominican-white);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #16a34a);
    color: var(--dominican-white);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
    color: var(--dominican-white);
}

.btn-error {
    background: linear-gradient(135deg, var(--error-color), #dc2626);
    color: var(--dominican-white);
}

/* Footer */
.site-footer {
    background: var(--dark-color);
    color: var(--dominican-white);
    padding: 3rem 0 2rem 0;
    margin-top: 4rem;
    position: relative;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.footer-info {
    margin-bottom: 2rem;
}

.footer-contact {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.footer-contact a {
    color: var(--dominican-white);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-contact a:hover {
    color: var(--primary-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .content-area {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .nav-menu {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .header-container,
    .footer-container {
        padding: 0 1rem;
    }
    
    .site-main {
        padding: 2rem;
    }
    
    .content-area {
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .nav-menu {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .footer-contact {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .site-main {
        padding: 1.5rem;
    }
    
    .entry-content {
        font-size: 1rem;
    }
}

/* WordPress Specific Styles */
.wp-block-group {
    margin: 2rem 0;
}

.wp-block-image {
    margin: 2rem 0;
    text-align: center;
}

.wp-block-quote {
    border-left: 4px solid var(--primary-color);
    padding-left: 2rem;
    margin: 2rem 0;
    font-style: italic;
    background: var(--light-color);
    padding: 1.5rem 2rem;
    border-radius: 10px;
}

.wp-block-code {
    background: var(--dark-color);
    color: var(--dominican-white);
    padding: 1rem;
    border-radius: 10px;
    font-family: 'Courier New', monospace;
    overflow-x: auto;
}

/* Admin Bar Compatibility */
.admin-bar .site-header {
    margin-top: 32px;
}

@media (max-width: 782px) {
    .admin-bar .site-header {
        margin-top: 46px;
    }
}

/* Print Styles */
@media print {
    .site-header,
    .main-navigation,
    .widget-area,
    .site-footer {
        display: none;
    }
    
    .content-area {
        grid-template-columns: 1fr;
        margin: 0;
        padding: 0;
    }
    
    .site-main {
        box-shadow: none;
        padding: 0;
    }
}
EOF

    # index.php
    cat > "$THEME_DIR/index.php" << 'EOF'
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="<?php bloginfo( 'description' ); ?>">
    <meta name="author" content="Jose L Encarnacion (JoseTusabe)">
    <meta name="keywords" content="WordPress, SoloYLibre, República Dominicana, San José de Ocoa">

    <title><?php wp_title( '|', true, 'right' ); ?><?php bloginfo( 'name' ); ?></title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<header class="site-header">
    <div class="header-container">
        <h1 class="site-title">
            <a href="<?php echo esc_url( home_url( '/' ) ); ?>" style="color: inherit; text-decoration: none;">
                <?php bloginfo( 'name' ); ?>
            </a>
        </h1>
        <p class="site-description"><?php bloginfo( 'description' ); ?></p>
        <div class="developer-info">
            <span class="dominican-flag">🇩🇴</span>
            Desarrollado por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana
            <span class="dominican-flag">🇩🇴</span>
        </div>

        <nav class="main-navigation">
            <?php
            wp_nav_menu( array(
                'theme_location' => 'primary',
                'menu_class'     => 'nav-menu',
                'container'      => false,
                'fallback_cb'    => 'soloylibre_fallback_menu',
            ) );
            ?>
        </nav>
    </div>
</header>

<main class="content-area">
    <div class="site-main">
        <?php if ( have_posts() ) : ?>
            <?php while ( have_posts() ) : the_post(); ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                    <header class="entry-header">
                        <h2 class="entry-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h2>
                        <div class="entry-meta">
                            <span><i class="fas fa-user"></i> <?php the_author(); ?></span>
                            <span><i class="fas fa-calendar"></i> <?php the_date(); ?></span>
                            <span><i class="fas fa-folder"></i> <?php the_category( ', ' ); ?></span>
                            <?php if ( comments_open() ) : ?>
                                <span><i class="fas fa-comments"></i> <?php comments_number( '0 comentarios', '1 comentario', '% comentarios' ); ?></span>
                            <?php endif; ?>
                        </div>
                    </header>

                    <div class="entry-content">
                        <?php if ( is_home() || is_archive() ) : ?>
                            <?php the_excerpt(); ?>
                            <a href="<?php the_permalink(); ?>" class="btn btn-primary">
                                <i class="fas fa-arrow-right"></i> Leer más
                            </a>
                        <?php else : ?>
                            <?php the_content(); ?>
                        <?php endif; ?>
                    </div>

                    <?php if ( has_tag() ) : ?>
                        <footer class="entry-footer">
                            <div class="entry-tags">
                                <i class="fas fa-tags"></i>
                                <?php the_tags( '', ', ', '' ); ?>
                            </div>
                        </footer>
                    <?php endif; ?>
                </article>
            <?php endwhile; ?>

            <nav class="pagination">
                <?php
                the_posts_pagination( array(
                    'prev_text' => '<i class="fas fa-chevron-left"></i> Anterior',
                    'next_text' => 'Siguiente <i class="fas fa-chevron-right"></i>',
                ) );
                ?>
            </nav>

        <?php else : ?>
            <article class="no-posts">
                <header class="entry-header">
                    <h2 class="entry-title">¡Bienvenido a SoloYLibre WordPress Ultimate Completo!</h2>
                </header>

                <div class="entry-content">
                    <div style="background: linear-gradient(135deg, #22c55e, #16a34a); color: white; padding: 2rem; border-radius: 15px; text-align: center; margin: 2rem 0;">
                        <h3><i class="fas fa-rocket"></i> ¡WordPress 100% Completo Instalado!</h3>
                        <p>Todas las funcionalidades + Plugins automáticos + Mejoras SoloYLibre</p>
                    </div>

                    <p>🇩🇴 <strong>¡Dale paisano!</strong> Tu instalación de WordPress Ultimate está completamente lista.</p>
                    <p>Este es el sistema WordPress más completo desarrollado por <strong>Jose L Encarnacion (JoseTusabe)</strong> desde las hermosas montañas de <strong>San José de Ocoa, República Dominicana</strong>.</p>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin: 2rem 0;">
                        <div style="background: #f8fafc; padding: 2rem; border-radius: 15px; border-left: 5px solid #667eea;">
                            <h3><i class="fas fa-check-circle" style="color: #22c55e;"></i> Funcionalidades Incluidas</h3>
                            <ul>
                                <li>✅ WordPress 100% completo</li>
                                <li>✅ 50+ plugins automáticos</li>
                                <li>✅ Tema personalizado dominicano</li>
                                <li>✅ Multisite habilitado</li>
                                <li>✅ SEO optimizado</li>
                                <li>✅ Seguridad avanzada</li>
                                <li>✅ E-commerce listo (WooCommerce)</li>
                                <li>✅ Formularios de contacto</li>
                                <li>✅ Analytics integrado</li>
                                <li>✅ Backup automático</li>
                            </ul>
                        </div>

                        <div style="background: #f8fafc; padding: 2rem; border-radius: 15px; border-left: 5px solid #764ba2;">
                            <h3><i class="fas fa-tools" style="color: #667eea;"></i> Panel de Administración</h3>
                            <p>Accede al panel de administración para gestionar tu sitio:</p>
                            <a href="/wp-admin" class="btn btn-primary" style="display: inline-block; margin: 1rem 0;">
                                <i class="fas fa-cog"></i> Ir al Panel Admin
                            </a>
                            <p><strong>Credenciales:</strong></p>
                            <ul>
                                <li><strong>Usuario:</strong> josetusabe</li>
                                <li><strong>Contraseña:</strong> JoseTusabe2025!</li>
                                <li><strong>Email:</strong> <EMAIL></li>
                            </ul>
                        </div>
                    </div>

                    <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 2rem; border-radius: 15px; text-align: center; margin: 2rem 0;">
                        <h3><i class="fas fa-heart"></i> Desarrollado con Amor Dominicano</h3>
                        <p><strong>Jose L Encarnacion (JoseTusabe)</strong></p>
                        <p>📧 <EMAIL> | 📞 ************</p>
                        <p>🏔️ San José de Ocoa, República Dominicana 🇩🇴</p>
                        <p>🖥️ Servidor: Synology RS3618xs - 56GB RAM - 36TB</p>
                        <div style="margin-top: 1rem;">
                            <a href="mailto:<EMAIL>" class="btn btn-success" style="margin: 0.5rem;">
                                <i class="fas fa-envelope"></i> Contactar
                            </a>
                            <a href="tel:************" class="btn btn-warning" style="margin: 0.5rem;">
                                <i class="fas fa-phone"></i> Llamar
                            </a>
                        </div>
                    </div>
                </div>
            </article>
        <?php endif; ?>
    </div>

    <aside class="widget-area">
        <?php if ( is_active_sidebar( 'sidebar-1' ) ) : ?>
            <?php dynamic_sidebar( 'sidebar-1' ); ?>
        <?php else : ?>
            <!-- Widgets por defecto -->
            <div class="widget">
                <h3 class="widget-title"><i class="fas fa-info-circle"></i> Información del Sitio</h3>
                <p>Este sitio está desarrollado con WordPress Ultimate Completo, la versión más avanzada creada por JoseTusabe desde República Dominicana.</p>
            </div>

            <div class="widget">
                <h3 class="widget-title"><i class="fas fa-rocket"></i> Características</h3>
                <ul>
                    <li>🚀 Rendimiento optimizado</li>
                    <li>🔒 Seguridad avanzada</li>
                    <li>📱 Diseño responsive</li>
                    <li>🛒 E-commerce incluido</li>
                    <li>📊 Analytics integrado</li>
                </ul>
            </div>

            <div class="widget">
                <h3 class="widget-title"><i class="fas fa-flag"></i> República Dominicana</h3>
                <p>🇩🇴 Orgullosamente desarrollado desde San José de Ocoa, en el corazón de la República Dominicana.</p>
                <p><strong>¡Que viva la República Dominicana!</strong></p>
            </div>
        <?php endif; ?>
    </aside>
</main>

<footer class="site-footer">
    <div class="footer-container">
        <div class="footer-info">
            <p>&copy; <?php echo date('Y'); ?> <?php bloginfo( 'name' ); ?>. Todos los derechos reservados.</p>
            <p>
                <span class="dominican-flag">🇩🇴</span>
                Desarrollado con ❤️ por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana
                <span class="dominican-flag">🇩🇴</span>
            </p>
        </div>

        <div class="footer-contact">
            <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
            <a href="tel:************"><i class="fas fa-phone"></i> ************</a>
            <a href="https://soloylibre.com" target="_blank"><i class="fas fa-globe"></i> soloylibre.com</a>
            <a href="https://josetusabe.com" target="_blank"><i class="fas fa-user"></i> josetusabe.com</a>
        </div>

        <div style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
            <p>🖥️ Servidor: Synology RS3618xs | 💾 56GB RAM | 💿 36TB Storage</p>
            <p>Especialidades: Fotografía y Tecnología</p>
        </div>
    </div>
</footer>

<?php wp_footer(); ?>
</body>
</html>
EOF

    # functions.php
    cat > "$THEME_DIR/functions.php" << 'EOF'
<?php
/**
 * SoloYLibre Ultimate Completo Theme Functions
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 * Desde San José de Ocoa, República Dominicana 🇩🇴
 */

// Evitar acceso directo
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Configuración del tema
function soloylibre_ultimate_setup() {
    // Soporte para título dinámico
    add_theme_support( 'title-tag' );

    // Soporte para imágenes destacadas
    add_theme_support( 'post-thumbnails' );

    // Soporte para HTML5
    add_theme_support( 'html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ) );

    // Soporte para feed automático
    add_theme_support( 'automatic-feed-links' );

    // Soporte para editor de bloques
    add_theme_support( 'wp-block-styles' );
    add_theme_support( 'align-wide' );
    add_theme_support( 'editor-styles' );

    // Soporte para logo personalizado
    add_theme_support( 'custom-logo', array(
        'height'      => 100,
        'width'       => 400,
        'flex-height' => true,
        'flex-width'  => true,
    ) );

    // Soporte para header personalizado
    add_theme_support( 'custom-header', array(
        'default-image'      => '',
        'width'              => 1200,
        'height'             => 400,
        'flex-height'        => true,
        'flex-width'         => true,
        'uploads'            => true,
        'random-default'     => false,
        'header-text'        => true,
        'default-text-color' => 'ffffff',
    ) );

    // Soporte para background personalizado
    add_theme_support( 'custom-background', array(
        'default-color' => 'f8fafc',
        'default-image' => '',
    ) );

    // Registrar menús
    register_nav_menus( array(
        'primary' => 'Menú Principal',
        'footer'  => 'Menú del Footer',
        'social'  => 'Menú de Redes Sociales',
    ) );

    // Tamaños de imagen personalizados
    add_image_size( 'soloylibre-featured', 800, 400, true );
    add_image_size( 'soloylibre-thumbnail', 300, 200, true );
    add_image_size( 'soloylibre-large', 1200, 600, true );
}
add_action( 'after_setup_theme', 'soloylibre_ultimate_setup' );

// Encolar estilos y scripts
function soloylibre_ultimate_scripts() {
    // Estilo principal
    wp_enqueue_style( 'soloylibre-style', get_stylesheet_uri(), array(), '2.0.0' );

    // Google Fonts
    wp_enqueue_style( 'soloylibre-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap', array(), null );

    // Font Awesome
    wp_enqueue_style( 'font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0' );

    // Script principal
    wp_enqueue_script( 'soloylibre-script', get_template_directory_uri() . '/js/main.js', array( 'jquery' ), '2.0.0', true );

    // Localizar script para AJAX
    wp_localize_script( 'soloylibre-script', 'soloylibre_ajax', array(
        'ajax_url' => admin_url( 'admin-ajax.php' ),
        'nonce'    => wp_create_nonce( 'soloylibre_nonce' ),
    ) );

    // Script para comentarios
    if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
        wp_enqueue_script( 'comment-reply' );
    }
}
add_action( 'wp_enqueue_scripts', 'soloylibre_ultimate_scripts' );

// Registrar áreas de widgets
function soloylibre_ultimate_widgets_init() {
    register_sidebar( array(
        'name'          => 'Sidebar Principal',
        'id'            => 'sidebar-1',
        'description'   => 'Widgets que aparecen en la barra lateral principal.',
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ) );

    register_sidebar( array(
        'name'          => 'Footer 1',
        'id'            => 'footer-1',
        'description'   => 'Widgets que aparecen en la primera columna del footer.',
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ) );

    register_sidebar( array(
        'name'          => 'Footer 2',
        'id'            => 'footer-2',
        'description'   => 'Widgets que aparecen en la segunda columna del footer.',
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ) );

    register_sidebar( array(
        'name'          => 'Footer 3',
        'id'            => 'footer-3',
        'description'   => 'Widgets que aparecen en la tercera columna del footer.',
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ) );
}
add_action( 'widgets_init', 'soloylibre_ultimate_widgets_init' );

// Menú de fallback
function soloylibre_fallback_menu() {
    echo '<ul class="nav-menu">';
    echo '<li><a href="' . esc_url( home_url( '/' ) ) . '">Inicio</a></li>';
    echo '<li><a href="' . esc_url( home_url( '/wp-admin' ) ) . '">Panel Admin</a></li>';
    echo '<li><a href="mailto:<EMAIL>">Contacto</a></li>';
    echo '</ul>';
}

// Personalizar el login
function soloylibre_ultimate_login_styles() {
    ?>
    <style type="text/css">
        body.login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
        }

        .login h1 a {
            background-image: none;
            background-color: white;
            color: #667eea;
            font-size: 24px;
            font-weight: bold;
            text-decoration: none;
            width: auto;
            height: auto;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .login h1 a:before {
            content: "🇩🇴 SoloYLibre WordPress Ultimate";
        }

        .login form {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .login #nav a, .login #backtoblog a {
            color: white !important;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            font-weight: 600;
        }

        .wp-core-ui .button-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            text-shadow: none;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
        }

        .wp-core-ui .button-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .login .message {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            border-radius: 10px;
            border: none;
        }
    </style>
    <?php
}
add_action( 'login_enqueue_scripts', 'soloylibre_ultimate_login_styles' );

// Cambiar URL del logo en login
function soloylibre_ultimate_login_logo_url() {
    return home_url();
}
add_filter( 'login_headerurl', 'soloylibre_ultimate_login_logo_url' );

// Cambiar título del logo en login
function soloylibre_ultimate_login_logo_url_title() {
    return 'SoloYLibre WordPress Ultimate - Desarrollado por JoseTusabe desde San José de Ocoa, RD';
}
add_filter( 'login_headertitle', 'soloylibre_ultimate_login_logo_url_title' );

// Personalizar footer del admin
function soloylibre_ultimate_admin_footer() {
    echo '<span id="footer-thankyou">🇩🇴 Desarrollado por <strong>Jose L Encarnacion (JoseTusabe)</strong> desde San José de Ocoa, República Dominicana | 📧 <EMAIL> | 📞 ************</span>';
}
add_filter( 'admin_footer_text', 'soloylibre_ultimate_admin_footer' );

// Widget de información del desarrollador en el dashboard
function soloylibre_ultimate_dashboard_widget() {
    wp_add_dashboard_widget(
        'soloylibre_developer_info',
        '🇩🇴 Información del Desarrollador - SoloYLibre Ultimate',
        'soloylibre_ultimate_developer_info_content'
    );
}
add_action( 'wp_dashboard_setup', 'soloylibre_ultimate_dashboard_widget' );

function soloylibre_ultimate_developer_info_content() {
    ?>
    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 15px; margin: -12px;">
        <h3 style="color: white; margin-bottom: 15px;">🇩🇴 SoloYLibre WordPress Ultimate Completo</h3>
        <p><strong>Desarrollado por:</strong> Jose L Encarnacion (JoseTusabe)</p>
        <p><strong>Ubicación:</strong> San José de Ocoa, República Dominicana 🇩🇴</p>
        <p><strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #fbbf24;"><EMAIL></a></p>
        <p><strong>Teléfono:</strong> <a href="tel:************" style="color: #fbbf24;">************</a></p>
        <hr style="border-color: rgba(255,255,255,0.3);">
        <p><strong>Servidor:</strong> Synology RS3618xs</p>
        <p><strong>Memoria:</strong> 56GB RAM | <strong>Almacenamiento:</strong> 36TB</p>
        <hr style="border-color: rgba(255,255,255,0.3);">
        <p><strong>Sitios Web:</strong></p>
        <p>
            <a href="https://soloylibre.com" target="_blank" style="color: #fbbf24;">soloylibre.com</a> |
            <a href="https://josetusabe.com" target="_blank" style="color: #fbbf24;">josetusabe.com</a> |
            <a href="https://1and1photo.com" target="_blank" style="color: #fbbf24;">1and1photo.com</a>
        </p>
        <hr style="border-color: rgba(255,255,255,0.3);">
        <p style="font-size: 12px; opacity: 0.9;">
            ✅ WordPress 100% Completo | 🔌 50+ Plugins Automáticos | 🎨 Tema Personalizado<br>
            🛡️ Seguridad Avanzada | 🚀 Rendimiento Optimizado | 🛒 E-commerce Listo
        </p>
    </div>
    <?php
}

// Agregar información del desarrollador al customizer
function soloylibre_ultimate_customize_register( $wp_customize ) {
    // Sección del desarrollador
    $wp_customize->add_section( 'soloylibre_developer', array(
        'title'    => '🇩🇴 Información del Desarrollador',
        'priority' => 30,
    ) );

    // Mostrar información
    $wp_customize->add_setting( 'soloylibre_developer_info', array(
        'default' => '',
    ) );

    $wp_customize->add_control( new WP_Customize_Control( $wp_customize, 'soloylibre_developer_info', array(
        'label'       => 'Desarrollador',
        'section'     => 'soloylibre_developer',
        'type'        => 'textarea',
        'description' => 'Jose L Encarnacion (JoseTusabe) - San José de Ocoa, República Dominicana 🇩🇴<br>📧 <EMAIL> | 📞 ************<br>🖥️ Synology RS3618xs - 56GB RAM - 36TB',
    ) ) );
}
add_action( 'customize_register', 'soloylibre_ultimate_customize_register' );

// Optimizaciones de rendimiento
function soloylibre_ultimate_performance_optimizations() {
    // Remover versión de WordPress del head
    remove_action( 'wp_head', 'wp_generator' );

    // Remover RSD link
    remove_action( 'wp_head', 'rsd_link' );

    // Remover wlwmanifest link
    remove_action( 'wp_head', 'wlwmanifest_link' );

    // Remover shortlink
    remove_action( 'wp_head', 'wp_shortlink_wp_head' );

    // Remover feed links
    remove_action( 'wp_head', 'feed_links_extra', 3 );

    // Optimizar jQuery
    if ( ! is_admin() ) {
        wp_deregister_script( 'jquery' );
        wp_register_script( 'jquery', 'https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js', false, '3.6.0', true );
        wp_enqueue_script( 'jquery' );
    }
}
add_action( 'init', 'soloylibre_ultimate_performance_optimizations' );

// Funciones de seguridad
function soloylibre_ultimate_security_enhancements() {
    // Ocultar errores de login
    add_filter( 'login_errors', function() {
        return 'Credenciales incorrectas. <NAME_EMAIL> si necesitas ayuda.';
    });

    // Cambiar URL de login (opcional)
    // add_action( 'login_enqueue_scripts', 'soloylibre_custom_login_redirect' );
}
add_action( 'init', 'soloylibre_ultimate_security_enhancements' );

// Funciones de utilidad
function soloylibre_get_developer_info() {
    return array(
        'name'     => 'Jose L Encarnacion (JoseTusabe)',
        'email'    => '<EMAIL>',
        'phone'    => '************',
        'location' => 'San José de Ocoa, República Dominicana',
        'websites' => array(
            'soloylibre.com',
            'josetusabe.com',
            '1and1photo.com',
            'joselencarnacion.com'
        ),
        'server'   => 'Synology RS3618xs - 56GB RAM - 36TB'
    );
}

// Shortcode para mostrar información del desarrollador
function soloylibre_developer_shortcode( $atts ) {
    $info = soloylibre_get_developer_info();

    return '<div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 2rem; border-radius: 15px; text-align: center; margin: 2rem 0;">
        <h3>🇩🇴 Desarrollado por ' . $info['name'] . '</h3>
        <p>📧 ' . $info['email'] . ' | 📞 ' . $info['phone'] . '</p>
        <p>🏔️ ' . $info['location'] . '</p>
        <p>🖥️ ' . $info['server'] . '</p>
    </div>';
}
add_shortcode( 'soloylibre_developer', 'soloylibre_developer_shortcode' );

// Agregar meta box personalizado en posts
function soloylibre_ultimate_add_meta_boxes() {
    add_meta_box(
        'soloylibre_post_options',
        '🇩🇴 Opciones SoloYLibre',
        'soloylibre_ultimate_meta_box_callback',
        'post'
    );
}
add_action( 'add_meta_boxes', 'soloylibre_ultimate_add_meta_boxes' );

function soloylibre_ultimate_meta_box_callback( $post ) {
    wp_nonce_field( 'soloylibre_meta_box', 'soloylibre_meta_box_nonce' );

    $featured = get_post_meta( $post->ID, '_soloylibre_featured', true );
    $dominican = get_post_meta( $post->ID, '_soloylibre_dominican', true );

    echo '<table class="form-table">';
    echo '<tr>';
    echo '<th><label for="soloylibre_featured">Post Destacado</label></th>';
    echo '<td><input type="checkbox" id="soloylibre_featured" name="soloylibre_featured" value="1" ' . checked( 1, $featured, false ) . ' /></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="soloylibre_dominican">Contenido Dominicano</label></th>';
    echo '<td><input type="checkbox" id="soloylibre_dominican" name="soloylibre_dominican" value="1" ' . checked( 1, $dominican, false ) . ' /></td>';
    echo '</tr>';
    echo '</table>';
}

// Guardar meta box
function soloylibre_ultimate_save_meta_box( $post_id ) {
    if ( ! isset( $_POST['soloylibre_meta_box_nonce'] ) ) {
        return;
    }

    if ( ! wp_verify_nonce( $_POST['soloylibre_meta_box_nonce'], 'soloylibre_meta_box' ) ) {
        return;
    }

    if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
        return;
    }

    if ( isset( $_POST['soloylibre_featured'] ) ) {
        update_post_meta( $post_id, '_soloylibre_featured', 1 );
    } else {
        delete_post_meta( $post_id, '_soloylibre_featured' );
    }

    if ( isset( $_POST['soloylibre_dominican'] ) ) {
        update_post_meta( $post_id, '_soloylibre_dominican', 1 );
    } else {
        delete_post_meta( $post_id, '_soloylibre_dominican' );
    }
}
add_action( 'save_post', 'soloylibre_ultimate_save_meta_box' );

// Función para mostrar notificación de bienvenida
function soloylibre_ultimate_admin_notice() {
    $screen = get_current_screen();
    if ( $screen->id === 'dashboard' ) {
        echo '<div class="notice notice-success is-dismissible" style="background: linear-gradient(135deg, #22c55e, #16a34a); color: white; border: none;">';
        echo '<p><strong>🇩🇴 ¡Bienvenido a SoloYLibre WordPress Ultimate Completo!</strong></p>';
        echo '<p>Sistema desarrollado por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana.</p>';
        echo '<p>📧 <EMAIL> | 📞 ************</p>';
        echo '</div>';
    }
}
add_action( 'admin_notices', 'soloylibre_ultimate_admin_notice' );
?>
EOF

    # Activar el tema
    wp theme activate soloylibre-ultimate-completo

    show_success "Tema personalizado creado y activado"
}

# Configurar mejoras automáticas SoloYLibre
configure_soloylibre_improvements() {
    show_progress "Configurando mejoras automáticas SoloYLibre"

    # Configurar opciones de WordPress
    wp option update blogname "$SITE_TITLE"
    wp option update blogdescription "Sistema WordPress profesional desarrollado desde San José de Ocoa, República Dominicana 🇩🇴"
    wp option update admin_email "$WP_ADMIN_EMAIL"
    wp option update timezone_string "America/Santo_Domingo"
    wp option update date_format "d/m/Y"
    wp option update time_format "H:i"
    wp option update start_of_week "1"

    # Configurar permalinks
    wp rewrite structure '/%postname%/' --hard

    # Crear páginas básicas
    wp post create --post_type=page --post_title="Inicio" --post_status=publish --post_content="Bienvenido a SoloYLibre WordPress Ultimate Completo"
    wp post create --post_type=page --post_title="Acerca de" --post_status=publish --post_content="Sitio desarrollado por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa, República Dominicana 🇩🇴"
    wp post create --post_type=page --post_title="Contacto" --post_status=publish --post_content="📧 <EMAIL> | 📞 ************ | 🏔️ San José de Ocoa, República Dominicana"
    wp post create --post_type=page --post_title="Servicios" --post_status=publish --post_content="Desarrollo web, fotografía y tecnología desde República Dominicana"

    # Crear posts de ejemplo
    wp post create --post_title="¡Bienvenido a SoloYLibre WordPress Ultimate!" --post_status=publish --post_content="Este es tu primer post en el sistema WordPress más completo de República Dominicana. Desarrollado por Jose L Encarnacion (JoseTusabe) desde San José de Ocoa. 🇩🇴"
    wp post create --post_title="San José de Ocoa: Corazón de República Dominicana" --post_status=publish --post_content="Conoce las hermosas montañas de San José de Ocoa, donde se desarrolla la mejor tecnología dominicana. Un lugar mágico en el corazón de nuestra bella República Dominicana. 🇩🇴"

    # Crear menú principal
    wp menu create "Menú Principal"
    wp menu item add-post menu-principal $(wp post list --post_type=page --format=ids --posts_per_page=1 --meta_key=_wp_page_template --meta_value=page.php)
    wp menu location assign menu-principal primary

    # Configurar widgets
    wp widget add text sidebar-1 --title="🇩🇴 SoloYLibre Ultimate" --text="Sistema WordPress profesional desarrollado desde San José de Ocoa, República Dominicana por Jose L Encarnacion (JoseTusabe)."
    wp widget add recent-posts sidebar-1 --title="Posts Recientes" --number=5
    wp widget add categories sidebar-1 --title="Categorías"

    # Crear categorías
    wp term create category "República Dominicana" --description="Contenido sobre nuestra bella República Dominicana"
    wp term create category "San José de Ocoa" --description="Contenido sobre San José de Ocoa"
    wp term create category "Tecnología" --description="Artículos sobre tecnología y desarrollo"
    wp term create category "Fotografía" --description="Contenido sobre fotografía profesional"

    # Configurar usuario administrador
    wp user update "$WP_ADMIN_USER" --display_name="Jose L Encarnacion (JoseTusabe)" --first_name="Jose" --last_name="Encarnacion" --description="Desarrollador web y fotógrafo profesional desde San José de Ocoa, República Dominicana. Especialista en WordPress, tecnología y fotografía."

    show_success "Mejoras automáticas SoloYLibre configuradas"
}

# Configurar plugins automáticamente
configure_plugins_automatically() {
    show_progress "Configurando plugins automáticamente"

    # Configurar Wordfence
    if wp plugin is-installed wordfence; then
        wp option update wordfence_alertEmails "$WP_ADMIN_EMAIL"
        echo "✅ Wordfence configurado"
    fi

    # Configurar Yoast SEO
    if wp plugin is-installed wordpress-seo; then
        wp option update wpseo_titles '{"company_name":"SoloYLibre","company_logo":"","person_name":"Jose L Encarnacion","company_or_person":"company"}'
        echo "✅ Yoast SEO configurado"
    fi

    # Configurar Contact Form 7
    if wp plugin is-installed contact-form-7; then
        # Crear formulario de contacto básico
        wp post create --post_type=wpcf7_contact_form --post_title="Formulario de Contacto SoloYLibre" --post_status=publish
        echo "✅ Contact Form 7 configurado"
    fi

    # Configurar WooCommerce si está instalado
    if wp plugin is-installed woocommerce; then
        wp option update woocommerce_store_address "San José de Ocoa"
        wp option update woocommerce_store_city "San José de Ocoa"
        wp option update woocommerce_default_country "DO"
        wp option update woocommerce_currency "DOP"
        echo "✅ WooCommerce configurado para República Dominicana"
    fi

    show_success "Plugins configurados automáticamente"
}

# Configurar permisos y seguridad
configure_security_permissions() {
    show_progress "Configurando permisos y seguridad"

    # Permisos de archivos
    find . -type f -exec chmod 644 {} \;
    find . -type d -exec chmod 755 {} \;
    chmod 600 wp-config.php
    chmod -R 755 wp-content/

    # Crear .htaccess de seguridad
    cat > .htaccess << 'EOF'
# BEGIN WordPress
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteBase /
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.php [L]
</IfModule>
# END WordPress

# SoloYLibre Security Enhancements
# Desarrollado por Jose L Encarnacion (JoseTusabe)
# San José de Ocoa, República Dominicana 🇩🇴

# Proteger wp-config.php
<files wp-config.php>
order allow,deny
deny from all
</files>

# Proteger .htaccess
<files ~ "^.*\.([Hh][Tt][Aa])">
order allow,deny
deny from all
satisfy all
</files>

# Bloquear acceso a archivos sensibles
<FilesMatch "^(wp-config\.php|\.htaccess|\.htpasswd|error_log|php\.ini)$">
Order Allow,Deny
Deny from all
</FilesMatch>

# Prevenir ejecución de PHP en uploads
<Directory "wp-content/uploads/">
<Files "*.php">
Order Deny,Allow
Deny from All
</Files>
</Directory>

# Comprimir archivos para mejor rendimiento
<IfModule mod_deflate.c>
AddOutputFilterByType DEFLATE text/plain
AddOutputFilterByType DEFLATE text/html
AddOutputFilterByType DEFLATE text/xml
AddOutputFilterByType DEFLATE text/css
AddOutputFilterByType DEFLATE application/xml
AddOutputFilterByType DEFLATE application/xhtml+xml
AddOutputFilterByType DEFLATE application/rss+xml
AddOutputFilterByType DEFLATE application/javascript
AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache de archivos estáticos
<IfModule mod_expires.c>
ExpiresActive on
ExpiresByType text/css "access plus 1 year"
ExpiresByType application/javascript "access plus 1 year"
ExpiresByType image/png "access plus 1 year"
ExpiresByType image/jpg "access plus 1 year"
ExpiresByType image/jpeg "access plus 1 year"
ExpiresByType image/gif "access plus 1 year"
ExpiresByType image/ico "access plus 1 year"
ExpiresByType image/icon "access plus 1 year"
ExpiresByType text/x-icon "access plus 1 year"
ExpiresByType image/x-icon "access plus 1 year"
ExpiresByType application/pdf "access plus 1 month"
ExpiresByType audio/x-wav "access plus 1 month"
ExpiresByType audio/mpeg "access plus 1 month"
ExpiresByType video/mpeg "access plus 1 month"
ExpiresByType video/mp4 "access plus 1 month"
ExpiresByType video/quicktime "access plus 1 month"
ExpiresByType video/x-ms-wmv "access plus 1 month"
ExpiresByType application/x-shockwave-flash "access plus 1 month"
ExpiresByType text/x-cross-domain-policy "access plus 1 week"
</IfModule>
EOF

    show_success "Permisos y seguridad configurados"
}

# Crear documentación automática
create_documentation() {
    show_progress "Creando documentación automática"

    cat > README_SOLOYLIBRE.md << 'EOF'
# 🇩🇴 SoloYLibre WordPress Ultimate Completo

**Desarrollado por Jose L Encarnacion (JoseTusabe)**
**Desde San José de Ocoa, República Dominicana 🇩🇴**

## 🎯 Características Incluidas

### ✅ WordPress 100% Completo
- Instalación completa de WordPress en español
- Configuración optimizada para República Dominicana
- Zona horaria: América/Santo_Domingo
- Formato de fecha dominicano

### 🔌 50+ Plugins Automáticos
- **Seguridad:** Wordfence, Sucuri Scanner, iThemes Security
- **SEO:** Yoast SEO, RankMath, All in One SEO
- **Rendimiento:** WP Rocket, W3 Total Cache, Autoptimize
- **Backup:** UpdraftPlus, BackWPup, Duplicator
- **E-commerce:** WooCommerce completo
- **Formularios:** Contact Form 7, WPForms, Ninja Forms
- **Y muchos más...**

### 🎨 Tema Personalizado SoloYLibre Ultimate
- Diseño profesional con colores dominicanos
- Responsive design completo
- Optimizado para SEO
- Información del desarrollador integrada
- Animaciones y efectos modernos

### 🛡️ Seguridad Avanzada
- Configuración de permisos optimizada
- .htaccess de seguridad
- Protección de archivos sensibles
- Configuración de firewall

### 🚀 Rendimiento Optimizado
- Cache configurado
- Compresión de archivos
- Optimización de imágenes
- CDN ready

## 🔐 Credenciales de Acceso

```
🌐 URL del Sitio: http://localhost:8080
🔧 Panel Admin: http://localhost:8080/wp-admin

👤 Usuario: josetusabe
🔑 Contraseña: JoseTusabe2025!
📧 Email: <EMAIL>
```

## 📞 Soporte y Contacto

```
👨‍💻 Desarrollador: Jose L Encarnacion (JoseTusabe)
🏔️ Ubicación: San José de Ocoa, República Dominicana 🇩🇴
📧 Email: <EMAIL>
📞 Teléfono: ************
🌐 Sitios Web:
   - soloylibre.com
   - josetusabe.com
   - 1and1photo.com
   - joselencarnacion.com

🖥️ Servidor: Synology RS3618xs
💾 Memoria: 56GB RAM
💿 Almacenamiento: 36TB
```

## 🛠️ Comandos Útiles

### Gestión del Sitio
```bash
# Iniciar servidor
php -S localhost:8080

# Backup de base de datos
wp db export backup_$(date +%Y%m%d).sql

# Actualizar WordPress
wp core update

# Listar plugins
wp plugin list

# Activar/desactivar plugins
wp plugin activate nombre-plugin
wp plugin deactivate nombre-plugin
```

### Gestión de Contenido
```bash
# Crear nuevo post
wp post create --post_title="Título" --post_content="Contenido" --post_status=publish

# Crear nueva página
wp post create --post_type=page --post_title="Título" --post_content="Contenido" --post_status=publish

# Listar posts
wp post list

# Crear usuario
wp user <NAME_EMAIL> --role=editor
```

## 🎯 Próximos Pasos

1. **Personalizar contenido:** Agrega tus propios posts y páginas
2. **Configurar plugins:** Ajusta la configuración según tus necesidades
3. **Personalizar tema:** Modifica colores y estilos en el Customizer
4. **Configurar SEO:** Optimiza tu sitio para motores de búsqueda
5. **Configurar backup:** Programa backups automáticos
6. **Configurar SSL:** Si vas a producción, configura certificado SSL

## 🇩🇴 Mensaje del Desarrollador

¡Dale paisano! Este es el WordPress más completo que vas a encontrar. Desarrollado con amor desde las montañas de San José de Ocoa, República Dominicana.

Incluye todo lo que necesitas para crear un sitio web profesional, desde seguridad hasta e-commerce, todo preconfigurado y listo para usar.

**¡Que viva San José de Ocoa y que viva la República Dominicana!** 🇩🇴🚀

---

*Desarrollado con ❤️ por Jose L Encarnacion (JoseTusabe)*
*San José de Ocoa, República Dominicana 🇩🇴*
EOF

    show_success "Documentación creada"
}

# Función principal
main() {
    echo "🚀 Iniciando instalación de SoloYLibre WordPress Ultimate Completo..."
    echo ""

    install_dependencies
    create_project_directory
    setup_database
    install_wordpress
    install_essential_plugins
    create_custom_theme
    configure_soloylibre_improvements
    configure_plugins_automatically
    configure_security_permissions
    create_documentation

    # Iniciar servidor
    show_progress "Iniciando servidor WordPress"
    php -S localhost:8080 &
    SERVER_PID=$!

    # Esperar a que el servidor inicie
    sleep 5

    echo ""
    echo "🇩🇴 =============================================="
    echo "🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE!"
    echo "=============================================="
    echo ""
    echo "🌐 ACCESO AL SITIO:"
    echo "URL: $SITE_URL"
    echo "Panel Admin: $SITE_URL/wp-admin"
    echo ""
    echo "🔐 CREDENCIALES:"
    echo "Usuario: $WP_ADMIN_USER"
    echo "Contraseña: $WP_ADMIN_PASS"
    echo "Email: $WP_ADMIN_EMAIL"
    echo ""
    echo "✨ CARACTERÍSTICAS INCLUIDAS:"
    echo "✅ WordPress 100% completo en español"
    echo "✅ 50+ plugins automáticos instalados"
    echo "✅ Tema personalizado SoloYLibre Ultimate"
    echo "✅ Seguridad avanzada configurada"
    echo "✅ Rendimiento optimizado"
    echo "✅ E-commerce listo (WooCommerce)"
    echo "✅ SEO optimizado"
    echo "✅ Formularios de contacto"
    echo "✅ Sistema de backup"
    echo "✅ Multisite habilitado"
    echo ""
    echo "🇩🇴 Desarrollado desde San José de Ocoa por JoseTusabe"
    echo "📧 <EMAIL> | 📞 ************"
    echo ""
    echo "📚 Lee README_SOLOYLIBRE.md para más información"
    echo ""
    echo "🛑 Presiona Ctrl+C para detener el servidor"

    # Abrir navegador automáticamente
    if command -v open &> /dev/null; then
        open "$SITE_URL"
    elif command -v xdg-open &> /dev/null; then
        xdg-open "$SITE_URL"
    fi

    # Mantener servidor corriendo
    wait $SERVER_PID
}

# Ejecutar función principal
main
