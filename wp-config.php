<?php
/**
 * SoloYLibre WordPress Configuration
 * Desarrollado por Jose L Encarnacion (JoseTusabe)
 * Desde San José de Ocoa, República Dominicana 🇩🇴
 */

// ** Configuración de Base de Datos SQLite ** //
define('DB_NAME', 'soloylibre_wordpress');
define('DB_USER', 'admin_soloylibre');
define('DB_PASSWORD', 'JoseTusabe2025!SanJoseDeOcoa');
define('DB_HOST', 'localhost');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// ** Claves de Seguridad ** //
define('AUTH_KEY',         'SanJoseDeOcoa2025!SoloYLibreWebDev#JoseTusabe$RepublicaDominicana');
define('SECURE_AUTH_KEY',  'MontagnasDominicanas!TecnologiaRD#InnovacionCaribe$OrgulloDominicano');
define('LOGGED_IN_KEY',    'KlkManito!DaleQueVamos#TechDominicano$SoloYLibreAI2025');
define('NONCE_KEY',        'VacanoTigueraje!BrutalPaisano#TecnologiaOcoa$DominicanPride');
define('AUTH_SALT',        'MerengueBachata!ColmadoSancocho#PlátanoYuca$MalecónPlaya');
define('SECURE_AUTH_SALT', 'CaribeTech!InnovaciónRD#SoloYLibreDev$JoseTusabeGenius');
define('LOGGED_IN_SALT',   'OcoaMountains!DominicanSpirit#TechInnovation$CaribbeanPower');
define('NONCE_SALT',       'RepublicaDominicana!SanJoseDeOcoa#SoloYLibreEcosystem$TechPride');

// ** Configuración de Desarrollo ** //
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);

// ** Configuración de Memoria ** //
define('WP_MEMORY_LIMIT', '512M');

// ** URLs ** //
define('WP_HOME', 'http://localhost:8080');
define('WP_SITEURL', 'http://localhost:8080');

// ** Configuración SoloYLibre ** //
define('SOLOYLIBRE_DEVELOPER', 'Jose L Encarnacion (JoseTusabe)');
define('SOLOYLIBRE_LOCATION', 'San José de Ocoa, República Dominicana');
define('SOLOYLIBRE_EMAIL', '<EMAIL>');
define('SOLOYLIBRE_PHONE', '************');

// ** Tabla de WordPress ** //
$table_prefix = 'wp_';

if ( !defined('ABSPATH') )
    define('ABSPATH', dirname(__FILE__) . '/');

require_once(ABSPATH . 'wp-settings.php');
